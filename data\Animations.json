[null, {"id": 1, "animation1Hue": 0, "animation1Name": "Hit1", "animation2Hue": 0, "animation2Name": "", "frames": [[], [[0, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0]], [[1, 0, 0, 200, 0, 0, 255, 1]], [[2, 0, 0, 200, 0, 0, 255, 1]], [[3, 0, 0, 200, 0, 0, 255, 1]]], "name": "Hit Physical", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 0, "se": {"name": "Blow3", "pan": 0, "pitch": 100, "volume": 90}}]}, {"id": 2, "animation1Hue": 0, "animation1Name": "Hit1", "animation2Hue": 0, "animation2Name": "HitPhoton", "frames": [[], [[0, 0, 0, 250, 0, 0, 255, 1], [100, -16, 0, 230, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0]], [[1, 0, 0, 200, 0, 0, 255, 1], [101, -16, 0, 230, 0, 0, 255, 1]], [[2, 0, 0, 200, 0, 0, 255, 1], [102, -16, 0, 230, 0, 0, 255, 1]], [[3, 0, 0, 200, 0, 0, 255, 1], [103, -16, 0, 230, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [104, -16, 0, 230, 0, 0, 180, 1]]], "name": "Hit Effect", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 0, "frame": 0, "se": {"name": "Evasion2", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Damage4", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Powerup", "pan": 0, "pitch": 180, "volume": 90}}]}, {"id": 3, "animation1Hue": 0, "animation1Name": "Hit2", "animation2Hue": 0, "animation2Name": "HitFire", "frames": [[[0, 0, -12, 150, 0, 0, 255, 1]], [[1, 0, -12, 150, 0, 0, 255, 1]], [[2, 0, -12, 150, 0, 0, 255, 1], [100, 0, -12, 225, 0, 0, 255, 1]], [[2, 0, -12, 165, 0, 0, 180, 1], [101, 0, -12, 225, 0, 0, 255, 1]], [[2, 0, -12, 172, 0, 0, 100, 1], [102, 0, -12, 225, 0, 0, 255, 1]], [[-1, 0, -12, 172, 0, 0, 100, 1], [103, 0, -12, 225, 0, 0, 255, 1]], [[-1, 0, -12, 172, 0, 0, 100, 1], [104, 0, -12, 225, 0, 0, 255, 1]], [[-1, 0, -12, 172, 0, 0, 100, 1], [105, 0, -12, 225, 0, 0, 255, 1]], [[-1, 0, -12, 172, 0, 0, 100, 1], [106, 0, -12, 225, 0, 0, 255, 1]], [[-1, 0, -12, 172, 0, 0, 100, 1], [107, 0, -12, 225, 0, 0, 255, 1]]], "name": "Hit Fire", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Fire1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 119, 102, 221], "flashDuration": 3, "flashScope": 1, "frame": 0, "se": {"name": "Blow3", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 136, 51, 153], "flashDuration": 5, "flashScope": 2, "frame": 2, "se": null}]}, {"id": 4, "animation1Hue": 0, "animation1Name": "Hit2", "animation2Hue": 0, "animation2Name": "HitIce", "frames": [[[0, 0, -12, 150, 0, 0, 255, 1]], [[1, 0, -12, 150, 0, 0, 255, 1]], [[2, 0, -12, 150, 0, 0, 255, 1], [101, 0, -12, 120, 0, 0, 255, 1], [102, 0, -12, 195, 0, 0, 255, 1]], [[2, 0, -12, 165, 0, 0, 180, 1], [100, 0, -12, 210, 0, 0, 255, 1], [103, 0, -12, 240, 0, 0, 255, 1]], [[2, 0, -12, 172, 0, 0, 100, 1], [100, 0, -12, 195, 0, 0, 255, 1], [104, 0, -12, 240, 0, 0, 255, 1], [105, 0, 0, 225, 0, 0, 255, 1], [108, 0, -12, 225, 0, 0, 255, 1]], [[101, 0, -12, 195, 0, 0, 255, 1], [104, 0, -12, 255, 0, 0, 255, 1], [108, 0, -12, 255, 0, 0, 255, 1], [106, 0, 0, 165, 0, 0, 255, 1]], [[100, 0, -12, 180, 0, 0, 255, 1], [104, 0, -12, 262, 0, 0, 180, 1], [109, 0, -12, 210, 0, 0, 255, 1], [106, 0, 0, 180, 0, 0, 255, 1]], [[101, 0, -12, 195, 0, 0, 255, 1], [104, 0, -12, 270, 0, 0, 100, 1], [109, 0, -12, 225, 0, 0, 200, 1], [107, 0, 0, 180, 0, 0, 255, 1]], [[100, 0, -12, 180, 0, 0, 255, 1], [109, 0, -12, 240, 0, 0, 100, 1], [106, 0, 0, 180, 0, 0, 255, 1]], [[101, 0, -12, 195, 0, 0, 180, 1], [107, 0, 0, 180, 0, 0, 255, 1]], [[101, 0, -12, 195, 0, 0, 100, 1]]], "name": "Hit Ice", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Ice4", "pan": 0, "pitch": 75, "volume": 100}}, {"flashColor": [119, 187, 255, 221], "flashDuration": 3, "flashScope": 1, "frame": 0, "se": {"name": "Blow3", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [187, 221, 221, 153], "flashDuration": 5, "flashScope": 2, "frame": 2, "se": null}]}, {"id": 5, "animation1Hue": 0, "animation1Name": "Hit2", "animation2Hue": 0, "animation2Name": "HitThunder", "frames": [[[0, 0, -12, 150, 0, 0, 255, 1]], [[1, 0, -12, 150, 0, 0, 255, 1]], [[2, 0, -12, 150, 0, 0, 255, 1], [100, 0, -12, 225, 0, 0, 255, 1]], [[2, 0, -12, 165, 0, 0, 180, 1], [101, 0, -12, 225, 0, 0, 255, 1]], [[2, 0, -12, 172, 0, 0, 100, 1], [102, 0, -12, 225, 0, 0, 255, 1]], [[103, 0, -12, 225, 0, 0, 255, 1]], [[104, 0, -12, 225, 0, 0, 255, 1]], [[105, 0, -12, 225, 0, 0, 255, 1]], [[106, 0, -12, 225, 0, 0, 255, 1]], [[-1, 0, -12, 225, 0, 0, 255, 1], [106, 0, -12, 232, 20, 0, 150, 1]]], "name": "Hit Thunder", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder8", "pan": 0, "pitch": 80, "volume": 100}}, {"flashColor": [255, 255, 102, 221], "flashDuration": 3, "flashScope": 1, "frame": 0, "se": {"name": "Blow3", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 119, 153], "flashDuration": 5, "flashScope": 2, "frame": 2, "se": null}]}, {"id": 6, "animation1Hue": 0, "animation1Name": "Slash", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 24, -42, 180, 0, 0, 255, 1]], [[1, 24, -42, 180, 0, 0, 255, 1]], [[2, 0, -18, 180, 0, 0, 255, 1]], [[3, 0, -18, 180, 0, 0, 255, 1]], [[4, 0, -18, 180, 0, 0, 255, 1]]], "name": "Slash Physical", "position": 1, "timings": [{"flashColor": [255, 255, 255, 187], "flashDuration": 2, "flashScope": 1, "frame": 0, "se": {"name": "Slash1", "pan": 0, "pitch": 140, "volume": 80}}]}, {"id": 7, "animation1Hue": 0, "animation1Name": "Slash", "animation2Hue": 0, "animation2Name": "SlashPhoton", "frames": [[[0, 0, -42, 180, 0, 0, 255, 1], [100, 0, -2, 180, 0, 0, 255, 1]], [[1, 0, -42, 180, 0, 0, 255, 1], [101, 0, -2, 180, 0, 0, 255, 1]], [[2, 0, -18, 180, 0, 0, 255, 1], [102, 16, -2, 180, 0, 0, 255, 1]], [[3, 0, -18, 180, 0, 0, 255, 1], [103, 60.5, -25.5, 180, 0, 0, 255, 1]], [[4, 0, -18, 180, 0, 0, 255, 1], [103, 79, -17.5, 180, 2, 0, 127, 1]]], "name": "Slash Effect", "position": 1, "timings": [{"flashColor": [255, 255, 255, 187], "flashDuration": 2, "flashScope": 0, "frame": 0, "se": {"name": "Evasion2", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [255, 255, 255, 187], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Slash1", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Powerup", "pan": 0, "pitch": 180, "volume": 90}}]}, {"id": 8, "animation1Hue": 0, "animation1Name": "SlashFire", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 24, -72, 180, 0, 0, 255, 1]], [[1, 24, -72, 180, 0, 0, 255, 1], [6, 24, -72, 195, 0, 0, 255, 1]], [[2, 0, -48, 180, 0, 0, 255, 1], [7, 0, -48, 195, 0, 0, 255, 1]], [[3, 0, -48, 180, 0, 0, 255, 1], [8, 0, -48, 195, 0, 0, 255, 1]], [[4, 0, -48, 180, 0, 0, 255, 1], [9, 0, -48, 195, 0, 0, 255, 1]], [[5, 0, -48, 180, 0, 0, 255, 1], [10, 0, -48, 195, 0, 0, 255, 1]], [[11, 0, -48, 195, 0, 0, 255, 1]], [[12, 0, -48, 195, 0, 0, 255, 1]], [[13, 0, -48, 195, 0, 0, 255, 1]]], "name": "Slash Fire", "position": 1, "timings": [{"flashColor": [255, 255, 255, 187], "flashDuration": 3, "flashScope": 1, "frame": 0, "se": {"name": "Slash1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 136, 51, 153], "flashDuration": 5, "flashScope": 2, "frame": 1, "se": {"name": "Fire1", "pan": 0, "pitch": 120, "volume": 100}}]}, {"id": 9, "animation1Hue": 0, "animation1Name": "SlashIce", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 24, -72, 180, 0, 0, 255, 1]], [[1, 24, -72, 180, 0, 0, 255, 1], [6, 24, -72, 195, 0, 0, 255, 1]], [[2, 0, -48, 180, 0, 0, 255, 1], [7, 0, -48, 195, 0, 0, 255, 1]], [[3, 0, -48, 180, 0, 0, 255, 1], [8, 0, -48, 195, 0, 0, 255, 1]], [[4, 0, -48, 180, 0, 0, 255, 1], [9, 0, -48, 195, 0, 0, 255, 1]], [[5, 0, -48, 180, 0, 0, 255, 1], [10, 0, -48, 195, 0, 0, 255, 1]], [[11, 0, -48, 195, 0, 0, 255, 1]], [[12, 0, -48, 195, 0, 0, 255, 1]]], "name": "Slash Ice", "position": 1, "timings": [{"flashColor": [255, 255, 255, 187], "flashDuration": 3, "flashScope": 1, "frame": 0, "se": {"name": "Slash1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [187, 221, 221, 153], "flashDuration": 5, "flashScope": 2, "frame": 1, "se": {"name": "Ice4", "pan": 0, "pitch": 100, "volume": 100}}]}, {"id": 10, "animation1Hue": 0, "animation1Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 24, -72, 180, 0, 0, 255, 1]], [[1, 24, -72, 180, 0, 0, 255, 1], [6, 24, -72, 195, 0, 0, 255, 1]], [[2, 0, -48, 180, 0, 0, 255, 1], [7, 0, -48, 195, 0, 0, 255, 1]], [[3, 0, -48, 180, 0, 0, 255, 1], [8, 0, -48, 195, 0, 0, 255, 1]], [[4, 0, -48, 180, 0, 0, 255, 1], [9, 0, -48, 195, 0, 0, 255, 1]], [[5, 0, -48, 180, 0, 0, 255, 1], [10, 0, -48, 195, 0, 0, 255, 1]], [[11, 0, -48, 195, 0, 0, 255, 1]], [[12, 0, -48, 195, 0, 0, 255, 1]], [[13, 0, -48, 195, 0, 0, 255, 1]]], "name": "Slash Thunder", "position": 1, "timings": [{"flashColor": [255, 255, 255, 187], "flashDuration": 3, "flashScope": 1, "frame": 0, "se": {"name": "Slash1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 119, 153], "flashDuration": 5, "flashScope": 2, "frame": 1, "se": {"name": "Thunder8", "pan": 0, "pitch": 100, "volume": 100}}]}, {"id": 11, "animation1Hue": 0, "animation1Name": "Stick", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 280, 0, 0, 172, 1]], [[2, 0, -2, 200, 0, 0, 255, 1]], [[3, 0, -2, 180, 0, 0, 255, 1]], [[4, 0, -2, 180, 0, 0, 255, 1]], [[5, 0, -2, 180, 0, 0, 255, 1]], [[6, 0, -2, 180, 0, 0, 255, 1], [6, 0, -2, 220, 45, 0, 255, 1]], [[6, 0, -2, 200, 0, 0, 127, 1], [6, 0, -2, 240, 45, 0, 127, 1]]], "name": "<PERSON>", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 2, "se": {"name": "Sword5", "pan": 0, "pitch": 120, "volume": 90}}]}, {"id": 12, "animation1Hue": 0, "animation1Name": "Stick", "animation2Hue": 0, "animation2Name": "StickP<PERSON><PERSON>", "frames": [[[0, 0, 0, 280, 0, 0, 172, 1], [-1, 0, -2, 0, 0, 0, 0, 0], [100, 0, 0, 260, 0, 0, 255, 1]], [[2, 0, 0, 200, 0, 0, 255, 1], [-1, 0, -2, 0, 0, 0, 0, 0], [101, 0, 0, 220, 0, 0, 255, 1]], [[3, 0, 0, 180, 0, 0, 255, 1], [-1, 0, -2, 0, 0, 0, 0, 0], [102, 0, 0, 220, 0, 0, 255, 1]], [[4, 0, 0, 180, 0, 0, 255, 1], [-1, 0, -2, 0, 0, 0, 0, 0], [103, 0, 0, 220, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 255, 1], [-1, 0, -2, 0, 0, 0, 0, 0], [104, 0, 0, 220, 0, 0, 255, 1]], [[6, 0, 0, 180, 0, 0, 255, 1], [6, 0, 0, 220, 45, 1, 255, 1], [105, 0, 0, 220, 0, 0, 255, 1]], [[6, 0, 0, 200, 0, 0, 127, 1], [6, 0, 0, 240, 45, 1, 127, 1], [107, 0, 0, 220, 0, 0, 255, 1]], [[107, 0, 0, 240, 180, 0, 255, 1], [-1, 220, -233, 100, 0, 0, 255, 1], [108, 0, 0, 280, 0, 0, 255, 1]], [[107, 0, 0, 240, 180, 0, 127, 1], [-1, 220, -233, 100, 0, 0, 255, 1], [108, 0, 0, 300, 0, 0, 127, 1]]], "name": "Pierce Effect", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 0, "frame": 0, "se": {"name": "Evasion2", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Thunder4", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Powerup", "pan": 0, "pitch": 180, "volume": 90}}]}, {"id": 13, "animation1Hue": 0, "animation1Name": "Stick", "animation2Hue": 0, "animation2Name": "HitFire", "frames": [[[-1, 0, -12, 150, 0, 0, 255, 1], [0, 0, -12, 300, 0, 0, 255, 1]], [[2, 0, -12, 200, 0, 0, 255, 1]], [[4, 0, -12, 300, 0, 0, 255, 1], [5, 0, -12, 180, 0, 0, 255, 1]], [[6, 0, -12, 240, 0, 0, 255, 1], [100, 0, -12, 225, 0, 0, 255, 1]], [[7, 0, -12, 255, 0, 0, 255, 1], [101, 0, -12, 225, 0, 0, 255, 1]], [[102, 0, -12, 225, 0, 0, 255, 1], [8, 0, -12, 270, 355, 0, 255, 1]], [[103, 0, -12, 240, 0, 0, 255, 1], [8, 0, -12, 277, 354, 0, 150, 1]], [[104, 0, -12, 225, 0, 0, 255, 1]], [[105, 0, -12, 225, 0, 0, 255, 1]], [[106, 0, -12, 225, 0, 0, 255, 1]], [[107, 0, -12, 225, 0, 0, 255, 1]]], "name": "Pierce Fire", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 119, 102, 221], "flashDuration": 3, "flashScope": 1, "frame": 2, "se": {"name": "Sword5", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 136, 51, 153], "flashDuration": 5, "flashScope": 2, "frame": 3, "se": {"name": "Fire1", "pan": 0, "pitch": 100, "volume": 100}}]}, {"id": 14, "animation1Hue": 135, "animation1Name": "Stick", "animation2Hue": 0, "animation2Name": "HitIce", "frames": [[[0, 0, -12, 300, 0, 0, 255, 1]], [[2, 0, -12, 200, 0, 0, 255, 1]], [[5, 0, -12, 180, 0, 0, 255, 1], [4, 0, -12, 300, 0, 0, 255, 1]], [[2, 0, -12, 150, 0, 0, 255, 1], [101, 0, -12, 120, 0, 0, 255, 1], [102, 0, -12, 195, 0, 0, 255, 1], [6, 0, -12, 240, 0, 0, 255, 1]], [[2, 0, -12, 165, 0, 0, 180, 1], [100, 0, -12, 210, 0, 0, 255, 1], [103, 0, -12, 240, 0, 0, 255, 1], [7, 0, -12, 255, 0, 0, 255, 1]], [[2, 0, -12, 172, 0, 0, 100, 1], [100, 0, -12, 195, 0, 0, 255, 1], [104, 0, -12, 240, 0, 0, 255, 1], [108, 0, -12, 225, 0, 0, 255, 1], [105, 0, 0, 225, 0, 0, 255, 1], [8, 0, -12, 270, 355, 0, 255, 1]], [[101, 0, -12, 195, 0, 0, 255, 1], [104, 0, -12, 255, 0, 0, 255, 1], [108, 0, -12, 255, 0, 0, 255, 1], [106, 0, 0, 165, 0, 0, 255, 1], [8, 0, -12, 277, 354, 0, 150, 1]], [[100, 0, -12, 180, 0, 0, 255, 1], [104, 0, -12, 262, 0, 0, 180, 1], [109, 0, -12, 210, 0, 0, 255, 1], [106, 0, 0, 180, 0, 0, 255, 1]], [[101, 0, -12, 195, 0, 0, 255, 1], [104, 0, -12, 270, 0, 0, 100, 1], [109, 0, -12, 225, 0, 0, 200, 1], [107, 0, 0, 180, 0, 0, 255, 1]], [[100, 0, -12, 180, 0, 0, 255, 1], [109, 0, -12, 240, 0, 0, 100, 1], [106, 0, 0, 180, 0, 0, 255, 1]], [[101, 0, -12, 195, 0, 0, 180, 1], [107, 0, 0, 180, 0, 0, 255, 1]], [[101, 0, -12, 195, 0, 0, 100, 1]]], "name": "<PERSON>", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [136, 221, 221, 221], "flashDuration": 3, "flashScope": 1, "frame": 2, "se": {"name": "Sword5", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [0, 255, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 3, "se": {"name": "Ice4", "pan": 0, "pitch": 75, "volume": 100}}]}, {"id": 15, "animation1Hue": 0, "animation1Name": "Stick", "animation2Hue": 0, "animation2Name": "HitThunder", "frames": [[[0, 0, -12, 300, 0, 0, 255, 1]], [[2, 0, -12, 200, 0, 0, 255, 1]], [[5, 0, -12, 180, 0, 0, 255, 1], [4, 0, -12, 300, 0, 0, 255, 1]], [[6, 0, -12, 240, 0, 0, 255, 1], [100, 0, -12, 225, 0, 0, 255, 1]], [[7, 0, -12, 255, 0, 0, 255, 1], [101, 0, -12, 225, 0, 0, 255, 1]], [[8, 0, -12, 270, 355, 0, 255, 1], [102, 0, -12, 225, 0, 0, 255, 1]], [[8, 0, -12, 277, 354, 0, 150, 1], [103, 0, -12, 225, 0, 0, 255, 1]], [[104, 0, -12, 225, 0, 0, 255, 1]], [[105, 0, -12, 225, 0, 0, 255, 1]], [[106, 0, -12, 225, 0, 0, 255, 1]], [[-1, 0, -12, 225, 0, 0, 255, 1], [106, 0, -12, 232, 20, 0, 150, 1]]], "name": "Pierce Thunder", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 255, 102, 221], "flashDuration": 3, "flashScope": 1, "frame": 2, "se": {"name": "Sword5", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 85, 153], "flashDuration": 5, "flashScope": 2, "frame": 3, "se": {"name": "Thunder8", "pan": 0, "pitch": 80, "volume": 100}}]}, {"id": 16, "animation1Hue": 0, "animation1Name": "Claw", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -26, 170, 0, 0, 255, 1]], [[1, 0, -26, 170, 0, 0, 255, 1]], [[2, 0, -26, 170, 0, 0, 255, 1]], [[3, 0, -26, 170, 0, 0, 255, 1]], [[4, 0, -26, 170, 0, 0, 255, 1]], [[5, 0, -26, 170, 0, 0, 255, 1]]], "name": "Claw Physical", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Attack2", "pan": 0, "pitch": 200, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Slash2", "pan": 0, "pitch": 100, "volume": 90}}]}, {"id": 17, "animation1Hue": 0, "animation1Name": "Claw", "animation2Hue": 0, "animation2Name": "ClawPhoton", "frames": [[[0, 0, -26, 170, 0, 0, 255, 1], [100, 0, -10, 190, 0, 0, 255, 1], [-1, -46, 217.5, 100, 0, 0, 255, 1], [-1, 195.5, 230.5, 100, 0, 0, 255, 1], [-1, 357, 209.5, 100, 0, 0, 255, 1], [-1, 364.5, 31, 100, 0, 0, 255, 1], [-1, 340, -151.5, 100, 0, 0, 255, 1], [-1, 267, -201, 100, 0, 0, 255, 1]], [[1, 0, -26, 170, 0, 0, 255, 1], [101, 0, -10, 190, 0, 0, 255, 1]], [[2, 0, -26, 170, 0, 0, 255, 1], [102, 0, -10, 190, 0, 0, 255, 1]], [[3, 0, -26, 170, 0, 0, 255, 1], [103, 0, -10, 190, 0, 0, 255, 1]], [[4, 0, -26, 170, 0, 0, 255, 1], [104, 0, -10, 190, 0, 0, 255, 1]], [[5, 0, -26, 170, 0, 0, 255, 1], [105, 0, -10, 190, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [106, 0, -10, 190, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [107, 0, -10, 190, 0, 0, 255, 1]]], "name": "Claw Effect", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 0, "frame": 0, "se": {"name": "Evasion2", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Attack2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Powerup", "pan": 0, "pitch": 180, "volume": 90}}]}, {"id": 18, "animation1Hue": 0, "animation1Name": "Claw", "animation2Hue": 0, "animation2Name": "SlashFire", "frames": [[[0, 0, -26, 170, 0, 0, 255, 1]], [[1, 0, -26, 170, 0, 0, 255, 1], [106, -50, 30, 160, 0, 0, 100, 1]], [[2, 0, -26, 170, 0, 0, 255, 1], [107, -50, 30, 160, 0, 0, 178, 1]], [[3, 0, -26, 170, 0, 0, 255, 1], [108, -50, 30, 160, 0, 0, 255, 1]], [[4, 0, -26, 170, 0, 0, 255, 1], [109, -50, 30, 160, 0, 0, 255, 1]], [[5, 0, -26, 170, 0, 0, 255, 1], [110, -50, 30, 160, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [111, -50, 30, 160, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [112, -50, 30, 160, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [113, -50, 30, 160, 0, 0, 255, 1]]], "name": "Claw Fire", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Attack2", "pan": 0, "pitch": 200, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Slash2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 136, 51, 153], "flashDuration": 5, "flashScope": 2, "frame": 3, "se": {"name": "Fire1", "pan": 0, "pitch": 100, "volume": 100}}]}, {"id": 19, "animation1Hue": 161, "animation1Name": "Claw", "animation2Hue": 0, "animation2Name": "SlashIce", "frames": [[[0, 0, -26, 170, 0, 0, 255, 1]], [[1, 0, -26, 170, 0, 0, 255, 1], [106, -25, -10, 160, 0, 0, 100, 1]], [[2, 0, -26, 170, 0, 0, 255, 1], [107, -25, -10, 160, 0, 0, 180, 1]], [[3, 0, -26, 170, 0, 0, 255, 1], [108, -25, -10, 160, 0, 0, 255, 1]], [[4, 0, -26, 170, 0, 0, 255, 1], [109, -25, -10, 160, 0, 0, 255, 1]], [[5, 0, -26, 170, 0, 0, 255, 1], [110, -25, -10, 160, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [111, -25, -10, 160, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [112, -25, -10, 160, 0, 0, 255, 1]]], "name": "Claw Ice", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Attack2", "pan": 0, "pitch": 200, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Slash2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [0, 255, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 3, "se": {"name": "Ice4", "pan": 0, "pitch": 75, "volume": 100}}]}, {"id": 20, "animation1Hue": 17, "animation1Name": "Claw", "animation2Hue": 0, "animation2Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frames": [[[0, 0, -26, 170, 0, 0, 255, 1]], [[1, 0, -26, 170, 0, 0, 255, 1], [106, -30, 0, 120, 0, 0, 255, 1]], [[2, 0, -26, 170, 0, 0, 255, 1], [107, -35, 5, 120, 0, 0, 255, 1]], [[3, 0, -26, 170, 0, 0, 255, 1], [108, -40, 9, 120, 0, 0, 255, 1]], [[4, 0, -26, 170, 0, 0, 255, 1], [109, -45, 14, 120, 0, 0, 255, 1]], [[5, 0, -26, 170, 0, 0, 255, 1], [110, -49, 18, 120, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [111, -54, 23, 120, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [112, -59, 27, 120, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [113, -64, 32, 120, 0, 0, 255, 1]]], "name": "Claw Thunder", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Attack2", "pan": 0, "pitch": 200, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Slash2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 85, 153], "flashDuration": 5, "flashScope": 2, "frame": 3, "se": {"name": "Thunder8", "pan": 0, "pitch": 80, "volume": 100}}]}, {"id": 21, "animation1Hue": 0, "animation1Name": "HitSpecial1", "animation2Hue": 0, "animation2Name": "", "frames": [[[-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [0, -273, 82, 100, -20, 0, 255, 1]], [[2, -95, 5, 180, 0, 0, 255, 1], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [1, -176, 38, 100, -20, 0, 255, 1], [0, 72, 312, 130, -100, 0, 255, 1]], [[3, -95, 5, 180, 0, 0, 255, 1], [2, 20, 90, 260, 90, 0, 255, 1], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [1, 41, 191.5, 130, -100, 0, 255, 1]], [[4, -95, 5, 180, 0, 0, 255, 1], [3, 20, 90, 260, 90, 0, 255, 1]], [[5, -95, 5, 180, 0, 0, 255, 1], [4, 20, 90, 260, 90, 0, 255, 1], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [0, 54, -256.5, 100, 100, 0, 255, 1]], [[6, -95, 5, 180, 0, 0, 255, 1], [5, 20, 90, 260, 90, 0, 255, 1], [2, 10, -80, 200, 180, 0, 255, 1], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [1, 27, -159.5, 100, 100, 0, 255, 1], [0, -250, -64, 110, 30, 0, 255, 1]], [[6, -95, 5, 180, 0, 0, 0, 1], [6, 20, 90, 260, 90, 0, 255, 1], [3, 10, -80, 200, 180, 0, 255, 1], [2, -65, 30, 260, -90, 0, 255, 1], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [1, -155, -12, 110, 30, 0, 255, 1]], [[4, -95, 5, 180, 0, 0, 0, 1], [4, 20, 90, 260, 90, 0, 0, 1], [4, 10, -80, 200, 180, 0, 255, 1], [3, -65, 30, 260, -90, 0, 255, 1], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [0, 332, 75, 120, 190, 0, 255, 1]], [[5, -95, 5, 180, 0, 0, 0, 1], [5, 20, 90, 260, 90, 0, 0, 1], [5, 10, -80, 200, 180, 0, 255, 1], [4, -65, 30, 260, -90, 0, 255, 1], [2, 95, 40, 220, 0, 1, 255, 1], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [-1, 0, 10, 0, 0, 0, 0, 0], [1, 178.5, 52, 120, 190, 0, 255, 1]], [[6, -95, 5, 180, 0, 0, 0, 1], [6, 20, 90, 260, 90, 0, 0, 1], [6, 10, -80, 200, 180, 0, 255, 1], [5, -65, 30, 260, -90, 0, 255, 1], [3, 95, 40, 220, 0, 1, 255, 1]], [[6, -95, 5, 180, 0, 0, 0, 1], [6, 20, 90, 260, 90, 0, 0, 1], [6, 10, -80, 200, 180, 0, 0, 1], [6, -65, 30, 260, -90, 0, 255, 1], [4, 95, 40, 220, 0, 1, 255, 1]], [[5, -95, 5, 180, 0, 0, 0, 1], [5, 20, 90, 260, 90, 0, 0, 1], [5, 10, -80, 200, 180, 0, 0, 1], [5, -65, 30, 260, -90, 0, 0, 1], [5, 95, 40, 220, 0, 1, 255, 1]], [[6, -95, 5, 180, 0, 0, 0, 1], [6, 20, 90, 260, 90, 0, 0, 1], [6, 10, -80, 200, 180, 0, 0, 1], [6, -65, 30, 260, -90, 0, 0, 1], [6, 95, 40, 220, 0, 1, 255, 1]], [], [[7, 116, 15, 400, 0, 0, 150, 1]], [[8, 52, 15, 240, 0, 0, 255, 1]], [[4, 20, 15, 300, 0, 0, 255, 1]], [[5, 36, 15, 240, 0, 0, 255, 1], [5, 4, 15, 360, 45, 0, 255, 1]], [[6, -44, 15, 320, 0, 0, 255, 1]], [[6, -44, 17, 360, 0, 0, 70, 1]]], "name": "Hit Special 1", "position": 1, "timings": [{"flashColor": [255, 221, 187, 187], "flashDuration": 2, "flashScope": 1, "frame": 2, "se": {"name": "Blow1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 221, 221, 136], "flashDuration": 2, "flashScope": 2, "frame": 2, "se": null}, {"flashColor": [255, 221, 187, 187], "flashDuration": 2, "flashScope": 1, "frame": 6, "se": {"name": "Blow3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 221, 221, 136], "flashDuration": 2, "flashScope": 2, "frame": 6, "se": null}, {"flashColor": [255, 221, 187, 187], "flashDuration": 2, "flashScope": 1, "frame": 9, "se": {"name": "Blow1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 221, 221, 136], "flashDuration": 2, "flashScope": 2, "frame": 9, "se": null}, {"flashColor": [255, 221, 187, 187], "flashDuration": 2, "flashScope": 1, "frame": 15, "se": {"name": "Slash1", "pan": 0, "pitch": 90, "volume": 90}}, {"flashColor": [255, 221, 221, 136], "flashDuration": 2, "flashScope": 2, "frame": 15, "se": {"name": "Thunder5", "pan": 0, "pitch": 120, "volume": 90}}]}, {"id": 22, "animation1Hue": 0, "animation1Name": "Hit2", "animation2Hue": 0, "animation2Name": "HitSpecial2", "frames": [[[0, 24, 48, 150, 0, 0, 255, 1]], [[1, 24, 48, 150, 0, 0, 255, 1]], [[2, 24, 48, 150, 0, 0, 255, 1], [100, -60, 12, 195, 0, 0, 255, 1]], [[2, 24, 48, 165, 0, 0, 255, 1], [101, -60, 12, 225, 0, 0, 255, 1]], [[2, 24, 48, 180, 0, 0, 150, 1], [102, -60, 12, 255, 0, 0, 255, 1]], [[103, -60, 12, 255, 0, 0, 255, 1], [100, 36, -48, 195, 0, 1, 255, 1]], [[104, -60, 12, 255, 0, 0, 255, 1], [101, 36, -48, 225, 20, 1, 255, 1]], [[105, -60, 12, 255, 0, 0, 255, 1], [102, 36, -48, 255, 20, 1, 255, 1], [0, -72, -72, 150, 0, 0, 255, 1]], [[103, 36, -48, 255, 20, 1, 255, 1], [1, -72, -72, 150, 0, 0, 255, 1]], [[104, 36, -48, 255, 20, 1, 255, 1], [2, -72, -72, 150, 0, 0, 255, 1], [100, -24, 84, 195, 45, 0, 255, 1]], [[105, 36, -48, 255, 20, 1, 255, 1], [2, -72, -72, 165, 0, 0, 255, 1], [101, -24, 84, 225, 45, 0, 255, 1]], [[2, -72, -72, 180, 0, 0, 150, 1], [102, -24, 84, 255, 45, 0, 255, 1]], [[103, -24, 84, 255, 45, 0, 255, 1], [0, -60, 60, 150, 0, 0, 255, 1], [0, 36, -48, 150, 0, 0, 255, 1]], [[104, -24, 84, 255, 45, 0, 255, 1], [1, -60, 60, 150, 0, 0, 255, 1], [1, 36, -48, 150, 0, 0, 255, 1]], [[105, -24, 84, 255, 45, 0, 255, 1], [2, -60, 60, 150, 0, 0, 255, 1], [2, 36, -48, 150, 0, 0, 255, 1], [100, -60, -60, 195, 0, 0, 255, 1], [100, 60, 60, 195, 90, 0, 255, 1]], [[2, -60, 60, 165, 0, 0, 255, 1], [2, 36, -48, 165, 0, 0, 255, 1], [101, -60, -60, 225, 0, 0, 255, 1], [101, 60, 60, 225, 90, 0, 255, 1]], [[2, -60, 60, 180, 0, 0, 150, 1], [2, 36, -48, 180, 0, 0, 150, 1], [102, -60, -60, 255, 0, 0, 255, 1], [102, 60, 60, 255, 90, 0, 255, 1]], [[103, -60, -60, 255, 0, 0, 255, 1], [103, 60, 60, 255, 90, 0, 255, 1], [100, 0, 0, 195, 0, 0, 255, 1], [1, 0, 0, 150, 0, 0, 255, 1]], [[104, -60, -60, 255, 0, 0, 255, 1], [104, 60, 60, 255, 90, 0, 255, 1], [101, 0, 0, 225, 0, 1, 255, 1], [2, 0, 0, 150, 0, 0, 255, 1]], [[105, -60, -60, 255, 0, 0, 255, 1], [105, 60, 60, 255, 90, 0, 255, 1], [102, 0, 0, 255, 0, 1, 255, 1], [2, 0, 0, 165, 0, 0, 255, 1]], [[103, 0, 0, 255, 0, 1, 255, 1]], [[104, 0, 0, 270, 0, 1, 255, 1]], [[105, 0, 0, 277, 0, 1, 255, 1]]], "name": "Hit Special 2", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 2, "flashScope": 2, "frame": 0, "se": {"name": "Blow1", "pan": 0, "pitch": 100, "volume": 90}}, {"conditions": 0, "flashColor": [255, 255, 221, 119], "flashDuration": 5, "flashScope": 1, "frame": 0, "se": null}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Blow1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 221, 119], "flashDuration": 5, "flashScope": 1, "frame": 3, "se": {"name": "Blow3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 5, "se": {"name": "Blow1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 221, 204], "flashDuration": 3, "flashScope": 2, "frame": 7, "se": {"name": "Blow2", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 9, "se": {"name": "Blow1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 221, 119], "flashDuration": 5, "flashScope": 1, "frame": 12, "se": {"name": "Blow3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 187], "flashDuration": 5, "flashScope": 1, "frame": 14, "se": {"name": "Blow3", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 255, 170], "flashDuration": 3, "flashScope": 2, "frame": 17, "se": {"name": "Blow2", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 18, "se": {"name": "Blow3", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [255, 255, 255, 170], "flashDuration": 3, "flashScope": 2, "frame": 19, "se": {"name": "Damage2", "pan": 0, "pitch": 120, "volume": 100}}]}, {"id": 23, "animation1Hue": 0, "animation1Name": "SlashSpecial1", "animation2Hue": 0, "animation2Name": "", "frames": [[[6, -110, -15, 300, 0, 0, 255, 0], [0, -50, 10, 180, -45, 0, 255, 1]], [[6, -110, -15, 300, 0, 0, 255, 0], [1, -50, 10, 180, -45, 0, 255, 1]], [[7, -110, -15, 300, 0, 0, 255, 0], [2, -50, 10, 180, -45, 0, 255, 1]], [[7, -110, -15, 300, 0, 0, 255, 0], [3, -10, -85, 220, -30, 1, 255, 1]], [[8, -110, -15, 300, 0, 0, 255, 0], [4, -10, -85, 220, -30, 1, 255, 1]], [[9, -110, -15, 300, 0, 0, 255, 0], [5, -10, -85, 220, -30, 1, 255, 1]], [[9, -110, -15, 300, 0, 0, 255, 0], [3, 20, -70, 240, 220, 0, 255, 1]], [[10, -110, -15, 300, 0, 0, 255, 0], [4, 20, -70, 240, 220, 0, 255, 1]], [[11, -110, -15, 300, 0, 0, 255, 0], [5, 20, -70, 240, 220, 0, 255, 1]], [[11, -110, -15, 300, 0, 0, 255, 0], [0, -16, 96, 200, 210, 1, 255, 1]], [[12, -110, -15, 300, 0, 0, 255, 0], [1, -16, 96, 200, 210, 1, 255, 1]], [[13, -110, -15, 300, 0, 0, 255, 0], [2, -16, 96, 200, 210, 1, 255, 1]], [[13, -110, -15, 300, 0, 0, 255, 0]], [[13, -110, -15, 300, 0, 0, 255, 0]], [[13, -110, -15, 300, 0, 0, 255, 0], [3, 96, 0, 360, 0, 0, 255, 1]], [[14, -110, -15, 300, 0, 0, 255, 0], [4, 0, 0, 360, 0, 0, 255, 1]], [[15, -110, -15, 300, 0, 0, 255, 1], [5, -64, 0, 360, 0, 0, 255, 1]], [[16, -110, -15, 300, 0, 0, 255, 1]], [[17, -110, -15, 300, 0, 0, 255, 1]]], "name": "Slash Special 1", "position": 1, "timings": [{"flashColor": [255, 238, 204, 221], "flashDuration": 3, "flashScope": 1, "frame": 1, "se": {"name": "Slash1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 238, 204, 221], "flashDuration": 3, "flashScope": 1, "frame": 3, "se": {"name": "Sword3", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 238, 204, 221], "flashDuration": 3, "flashScope": 1, "frame": 5, "se": {"name": "Sword4", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 238, 204, 221], "flashDuration": 3, "flashScope": 1, "frame": 7, "se": {"name": "Slash1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 238, 204, 221], "flashDuration": 3, "flashScope": 1, "frame": 9, "se": {"name": "Sword3", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 238, 204, 221], "flashDuration": 3, "flashScope": 1, "frame": 11, "se": {"name": "Sword4", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 14, "se": {"name": "Slash1", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 238, 204, 221], "flashDuration": 3, "flashScope": 1, "frame": 15, "se": {"name": "Sword4", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [255, 238, 204, 170], "flashDuration": 3, "flashScope": 2, "frame": 15, "se": {"name": "Attack3", "pan": 0, "pitch": 120, "volume": 90}}]}, {"id": 24, "animation1Hue": 0, "animation1Name": "SlashSpecial2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -12, 240, 0, 0, 255, 1]], [[1, 0, -12, 240, 0, 0, 255, 1]], [[2, 0, -12, 240, 0, 0, 255, 1]], [[3, 0, -12, 240, 0, 0, 255, 1]], [[4, 0, -12, 240, 0, 0, 255, 1]], [[5, 0, -12, 240, 0, 0, 255, 1]], [[6, 0, -12, 240, 0, 0, 255, 1]], [[7, 0, -12, 240, 0, 0, 255, 1]], [[8, 0, -12, 240, 0, 0, 255, 1]], [[9, 0, -12, 240, 0, 0, 255, 1]], [[10, 0, -12, 240, 0, 0, 255, 1]], [[11, 0, -12, 240, 0, 0, 255, 1]], [[12, 0, -12, 240, 0, 0, 255, 1]], [[13, 0, -12, 240, 0, 0, 255, 1]], [[13, 0, -12, 240, 0, 0, 255, 1], [14, 0, -12, 240, 0, 0, 100, 1]], [[13, 0, -12, 240, 0, 0, 255, 1], [14, 0, -12, 240, 0, 0, 200, 1]], [[13, 0, -12, 240, 0, 0, 255, 1], [14, 0, -12, 240, 0, 0, 220, 1], [15, 0, -12, 255, 0, 0, 255, 1]], [[13, 0, -12, 240, 0, 0, 255, 1], [14, 0, -12, 240, 0, 0, 255, 1], [15, 0, -12, 270, 0, 0, 255, 1]], [[13, 0, -12, 240, 0, 0, 255, 1], [14, 0, -12, 240, 0, 0, 255, 1], [15, 0, -12, 285, 0, 0, 200, 1]]], "name": "Slash Special 2", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Sword4", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 102, 0, 221], "flashDuration": 5, "flashScope": 1, "frame": 1, "se": null}, {"flashColor": [255, 255, 170, 170], "flashDuration": 2, "flashScope": 2, "frame": 1, "se": {"name": "Slash2", "pan": 0, "pitch": 110, "volume": 100}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 3, "se": {"name": "Wind7", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 170, 204], "flashDuration": 5, "flashScope": 1, "frame": 7, "se": {"name": "Slash3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 170, 170], "flashDuration": 3, "flashScope": 2, "frame": 7, "se": {"name": "Wind7", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 8, "flashScope": 1, "frame": 9, "se": {"name": "Slash1", "pan": 0, "pitch": 90, "volume": 90}}, {"flashColor": [255, 255, 255, 170], "flashDuration": 1, "flashScope": 3, "frame": 14, "se": null}, {"flashColor": [255, 255, 255, 255], "flashDuration": 8, "flashScope": 1, "frame": 16, "se": {"name": "Ice4", "pan": 0, "pitch": 90, "volume": 90}}]}, {"id": 25, "animation1Hue": 0, "animation1Name": "SlashSpecial3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -96, 375, 330, 0, 255, 1]], [[1, 0, -96, 375, 330, 0, 255, 1], [12, 0, -96, 150, 0, 0, 150, 0]], [[2, 0, -96, 375, 330, 0, 255, 1], [12, 0, -96, 210, 0, 0, 200, 0]], [[3, 0, -96, 375, 330, 0, 255, 1], [12, 0, -96, 225, 0, 0, 255, 0]], [[4, 0, -96, 375, 330, 0, 255, 1], [12, 0, -96, 232, 0, 0, 255, 0]], [[5, 0, -96, 375, 330, 0, 255, 1], [12, 0, -96, 240, 0, 0, 150, 0]], [[12, 0, -96, 244, 0, 0, 100, 0]], [[0, 0, -48, 435, 40, 1, 255, 1], [12, 0, -96, 247, 0, 0, 50, 0]], [[1, 0, -48, 435, 40, 1, 255, 1], [13, 0, -96, 150, 0, 0, 150, 0]], [[2, 0, -48, 435, 40, 1, 255, 1], [13, 0, -96, 210, 0, 0, 200, 0]], [[3, 0, -48, 435, 40, 1, 255, 1], [13, 0, -96, 225, 0, 0, 255, 0]], [[4, 0, -48, 435, 40, 1, 255, 1], [13, 0, -96, 232, 0, 0, 255, 0]], [[5, 0, -48, 435, 40, 1, 255, 1], [13, 0, -96, 240, 0, 0, 150, 0]], [[13, 0, -96, 244, 0, 0, 100, 0]], [[0, 0, -120, 435, 290, 0, 255, 1], [13, 0, -96, 247, 0, 0, 50, 0]], [[1, 0, -120, 435, 290, 0, 255, 1], [14, 0, -96, 150, 0, 0, 150, 0]], [[2, 0, -120, 435, 290, 0, 255, 1], [14, 0, -96, 210, 0, 0, 200, 0]], [[3, 0, -120, 435, 290, 0, 255, 1], [14, 0, -96, 225, 0, 0, 255, 0]], [[4, 0, -120, 435, 290, 0, 255, 1], [14, 0, -96, 232, 0, 0, 255, 0]], [[5, 0, -120, 435, 290, 0, 255, 1], [14, 0, -96, 240, 0, 0, 150, 0]], [[14, 0, -96, 244, 0, 0, 100, 0]], [[6, 0, -108, 435, 80, 0, 255, 1], [14, 0, -96, 247, 0, 0, 50, 0]], [[7, 0, -108, 435, 80, 0, 255, 1], [16, 0, -96, 225, 0, 0, 255, 0], [15, 0, -96, 150, 0, 0, 150, 0]], [[8, 0, -108, 435, 80, 0, 255, 1], [17, 0, -96, 270, 0, 0, 255, 0], [15, 0, -96, 210, 0, 0, 200, 0]], [[9, 0, -108, 435, 80, 0, 255, 1], [18, 0, -96, 375, 0, 0, 255, 0], [15, 0, -96, 225, 0, 0, 255, 0]], [[10, 0, -108, 435, 80, 0, 255, 1], [15, 0, -96, 232, 0, 0, 255, 0], [19, 0, -96, 382, 0, 0, 255, 0], [-1, 0, 0, 30, 0, 0, 0, 0], [-1, 0, 0, 30, 0, 0, 0, 0]], [[11, 0, -108, 435, 80, 0, 255, 1], [15, 0, -96, 240, 0, 0, 150, 0], [20, 0, -96, 390, 0, 0, 255, 0]], [[15, 0, -96, 244, 0, 0, 100, 0], [21, 0, -96, 390, 0, 0, 255, 0]], [[15, 0, -96, 247, 0, 0, 50, 0], [22, 0, -96, 390, 0, 0, 255, 0]], [[23, 0, -96, 390, 0, 0, 255, 0]], [[24, 0, -96, 390, 0, 0, 255, 0]], [[25, 0, -96, 390, 0, 0, 200, 0]], [[26, 0, -96, 390, 0, 0, 150, 0]], [[27, 0, -96, 390, 0, 0, 100, 0]]], "name": "Slash Special 3", "position": 3, "timings": [{"flashColor": [238, 170, 255, 170], "flashDuration": 2, "flashScope": 1, "frame": 1, "se": {"name": "Slash2", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [187, 187, 255, 187], "flashDuration": 2, "flashScope": 2, "frame": 1, "se": null}, {"flashColor": [238, 170, 255, 170], "flashDuration": 2, "flashScope": 1, "frame": 8, "se": {"name": "Slash3", "pan": 0, "pitch": 120, "volume": 100}}, {"flashColor": [187, 187, 255, 187], "flashDuration": 2, "flashScope": 2, "frame": 8, "se": null}, {"flashColor": [238, 170, 255, 170], "flashDuration": 2, "flashScope": 1, "frame": 15, "se": {"name": "Slash2", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [187, 187, 255, 187], "flashDuration": 2, "flashScope": 2, "frame": 15, "se": null}, {"flashColor": [238, 170, 255, 170], "flashDuration": 5, "flashScope": 1, "frame": 22, "se": {"name": "Slash3", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [187, 187, 255, 187], "flashDuration": 5, "flashScope": 2, "frame": 22, "se": {"name": "Wind6", "pan": 0, "pitch": 150, "volume": 70}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 21, "se": {"name": "Sword3", "pan": 0, "pitch": 120, "volume": 90}}]}, {"id": 26, "animation1Hue": 0, "animation1Name": "StickSpecial1", "animation2Hue": 0, "animation2Name": "", "frames": [[[4, 32, 48, 200, 20, 1, 255, 1]], [[5, 32, 48, 200, 20, 1, 255, 1]], [[6, 32, 48, 200, 20, 1, 255, 1], [0, -100, -93, 200, 30, 0, 255, 1]], [[1, -100, -93, 200, 30, 0, 255, 1]], [[2, -100, -93, 200, 30, 0, 255, 1], [4, -16, 48, 200, -40, 0, 255, 1]], [[5, -16, 48, 200, -40, 0, 255, 1]], [[6, -16, 48, 200, -40, 0, 255, 1], [4, 32, -48, 200, -30, 1, 255, 1]], [[5, 32, -48, 200, -30, 1, 255, 1]], [[6, 32, -48, 200, -30, 1, 255, 1], [0, 112, 48, 200, 20, 1, 255, 1]], [[1, 112, 48, 200, 20, 1, 255, 1]], [[2, 112, 48, 200, 20, 1, 255, 1], [0, -112, 0, 200, 0, 0, 255, 1]], [[1, -112, 0, 200, 0, 0, 255, 1]], [[2, -112, 0, 200, 0, 0, 255, 1]], [], [[-1, 334.5, 68, 200, 20, 1, 255, 1], [3, 0, 0, 420, 0, 0, 180, 1]], [[4, 4, -8, 300, 0, 0, 255, 1]], [[5, 4, -8, 300, 0, 0, 255, 1]], [[6, 4, -8, 300, 0, 0, 255, 1]], [[7, 4, -8, 300, 0, 0, 255, 1]], [[8, 4, -8, 300, 0, 0, 255, 1]], [[9, 4, -8, 300, 0, 0, 255, 1]]], "name": "Pierce Special 1", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind6", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 2, "flashScope": 1, "frame": 2, "se": {"name": "Sword5", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 119], "flashDuration": 2, "flashScope": 2, "frame": 3, "se": {"name": "Slash1", "pan": 0, "pitch": 180, "volume": 90}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 2, "flashScope": 1, "frame": 4, "se": {"name": "Sword5", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 119], "flashDuration": 2, "flashScope": 2, "frame": 5, "se": {"name": "Slash1", "pan": 0, "pitch": 180, "volume": 90}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 2, "flashScope": 1, "frame": 6, "se": {"name": "Sword5", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 119], "flashDuration": 2, "flashScope": 2, "frame": 7, "se": {"name": "Slash1", "pan": 0, "pitch": 180, "volume": 90}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 2, "flashScope": 1, "frame": 8, "se": {"name": "Sword5", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 119], "flashDuration": 2, "flashScope": 2, "frame": 9, "se": {"name": "Slash1", "pan": 0, "pitch": 180, "volume": 90}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 5, "flashScope": 1, "frame": 12, "se": {"name": "Sword4", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 119], "flashDuration": 2, "flashScope": 2, "frame": 12, "se": {"name": "Slash2", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 204], "flashDuration": 5, "flashScope": 2, "frame": 16, "se": null}]}, {"id": 27, "animation1Hue": 160, "animation1Name": "StickSpecial2", "animation2Hue": 210, "animation2Name": "StickSpecial3", "frames": [[[2, -48, 72, 300, 0, 0, 255, 1]], [[3, -48, 72, 225, 0, 0, 255, 1]], [[4, -48, 72, 255, 0, 0, 255, 1], [2, 84, -48, 300, 0, 0, 255, 1], [5, -48, 72, 150, 0, 0, 255, 1]], [[6, -48, 72, 180, 0, 0, 255, 1], [3, 84, -48, 225, 0, 0, 255, 1]], [[7, -48, 72, 180, 0, 0, 255, 1], [4, 84, -48, 255, 0, 0, 255, 1], [5, 84, -48, 150, 0, 0, 255, 1]], [[109, -48, 72, 195, 0, 0, 255, 1], [8, -48, 72, 180, 0, 0, 255, 1], [6, 84, -48, 180, 0, 0, 255, 1], [2, -72, -72, 300, 0, 0, 255, 1]], [[110, -48, 72, 195, 0, 0, 255, 1], [7, 84, -48, 180, 0, 0, 255, 1], [3, -72, -72, 225, 0, 0, 255, 1]], [[109, 84, -48, 195, 0, 0, 255, 1], [8, 84, -48, 180, 0, 0, 255, 1], [4, -72, -72, 255, 0, 0, 255, 1], [5, -72, -72, 150, 0, 0, 255, 1]], [[110, 84, -48, 195, 0, 0, 255, 1], [6, -72, -72, 180, 0, 0, 255, 1], [2, 60, 60, 300, 0, 0, 255, 1]], [[7, -72, -72, 180, 0, 0, 255, 1], [3, 60, 60, 225, 0, 0, 255, 1]], [[109, -72, -72, 195, 0, 0, 255, 1], [8, -72, -72, 180, 0, 0, 255, 1], [4, 60, 60, 255, 0, 0, 255, 1], [5, 60, 60, 150, 0, 0, 255, 1]], [[110, -72, -72, 195, 0, 0, 255, 1], [6, 60, 60, 180, 0, 0, 255, 1], [102, -72, 36, 300, 0, 0, 255, 1]], [[7, 60, 60, 180, 0, 0, 255, 1], [103, -72, 36, 225, 0, 0, 255, 1]], [[109, 60, 60, 195, 0, 0, 255, 1], [8, 60, 60, 180, 0, 0, 255, 1], [104, -72, 36, 255, 0, 0, 255, 1], [105, -72, 36, 150, 0, 0, 255, 1]], [[110, 60, 60, 195, 0, 0, 255, 1], [106, -72, 36, 180, 0, 0, 255, 1], [102, 72, 0, 300, 0, 0, 255, 1]], [[107, -72, 36, 180, 0, 0, 255, 1], [103, 72, 0, 225, 0, 0, 255, 1]], [[108, -72, 36, 195, 0, 0, 255, 1], [104, 72, 0, 255, 0, 0, 255, 1], [105, 72, 0, 150, 0, 0, 255, 1]], [[109, -72, 36, 195, 0, 0, 255, 1], [106, 72, 0, 180, 0, 0, 255, 1]], [[110, -72, 36, 195, 0, 0, 255, 1], [107, 72, 0, 180, 0, 0, 255, 1]], [[-1, 84, 0, 180, 0, 0, 255, 1], [108, 72, 0, 195, 0, 0, 255, 1]], [[109, 72, 0, 195, 0, 0, 255, 1]], [[110, 72, 0, 195, 0, 0, 255, 1]]], "name": "Pierce Special 2", "position": 1, "timings": [{"flashColor": [187, 221, 255, 238], "flashDuration": 2, "flashScope": 2, "frame": 0, "se": {"name": "Wind5", "pan": 0, "pitch": 80, "volume": 80}}, {"flashColor": [255, 187, 119, 221], "flashDuration": 3, "flashScope": 1, "frame": 2, "se": {"name": "Sword4", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [255, 187, 119, 221], "flashDuration": 3, "flashScope": 1, "frame": 4, "se": {"name": "Sword5", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [187, 221, 255, 187], "flashDuration": 2, "flashScope": 2, "frame": 6, "se": {"name": "Slash2", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [255, 187, 119, 221], "flashDuration": 3, "flashScope": 1, "frame": 7, "se": {"name": "Sword4", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [187, 221, 255, 170], "flashDuration": 2, "flashScope": 2, "frame": 9, "se": {"name": "Sword5", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [255, 187, 119, 221], "flashDuration": 3, "flashScope": 1, "frame": 10, "se": {"name": "Slash2", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [255, 187, 119, 221], "flashDuration": 3, "flashScope": 1, "frame": 13, "se": {"name": "Sword4", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [255, 187, 119, 221], "flashDuration": 3, "flashScope": 1, "frame": 16, "se": {"name": "Sword5", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [255, 187, 119, 221], "flashDuration": 3, "flashScope": 1, "frame": 17, "se": {"name": "Slash2", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [187, 221, 255, 170], "flashDuration": 2, "flashScope": 2, "frame": 19, "se": {"name": "Sword4", "pan": 0, "pitch": 150, "volume": 80}}]}, {"id": 28, "animation1Hue": 0, "animation1Name": "ClawSpecial1", "animation2Hue": 0, "animation2Name": "ClawSpecial2", "frames": [[[0, 0, -6, 240, 0, 0, 255, 1]], [[1, 0, -6, 240, 0, 0, 255, 1]], [[2, 0, -6, 240, 0, 0, 255, 1]], [[3, 0, -6, 240, 0, 0, 255, 1]], [[4, 0, -6, 240, 0, 0, 255, 1]], [[5, 0, -6, 240, 0, 0, 255, 1]], [[6, 0, -6, 240, 0, 0, 255, 1]], [[7, 0, -6, 240, 0, 0, 255, 1]], [[8, 0, -6, 240, 0, 0, 255, 1]], [[9, 0, -6, 240, 0, 0, 255, 1]], [[10, 0, -6, 240, 0, 0, 255, 1]], [[11, 0, -6, 240, 0, 0, 255, 1]], [[12, 0, -6, 240, 0, 0, 255, 1]], [[13, 0, -6, 240, 0, 0, 255, 1]], [[14, 0, -6, 240, 0, 0, 255, 1]], [[15, 0, -6, 240, 0, 0, 255, 1]], [[16, 0, -6, 240, 0, 0, 255, 1]], [[17, 0, -6, 240, 0, 0, 255, 1], [100, 0, -18, 240, 250, 1, 255, 1]], [[18, 0, -6, 240, 0, 0, 255, 1], [101, 0, -18, 240, 250, 1, 255, 1]], [[19, 0, -6, 240, 0, 0, 255, 1], [102, 0, -18, 240, 250, 1, 255, 1]], [[20, 0, -6, 240, 0, 0, 255, 1], [103, 0, -18, 240, 250, 1, 255, 1]], [[21, 0, -6, 240, 0, 0, 255, 1], [104, 0, -18, 240, 250, 1, 255, 1]], [[22, 0, -6, 240, 0, 0, 255, 1], [103, 0, -18, 240, 250, 1, 255, 1]], [[104, 0, -18, 240, 250, 1, 255, 1]], [[103, 0, -18, 240, 250, 1, 255, 1]], [[104, 0, -18, 240, 250, 1, 255, 1]], [[105, 0, -18, 240, 250, 1, 255, 1]], [[106, 0, -18, 240, 250, 1, 255, 1]], [[107, 0, -18, 240, 250, 1, 255, 1]], [[108, 0, -18, 240, 250, 1, 255, 1]], [[109, 0, -18, 240, 250, 1, 255, 1]], [[110, 0, -18, 240, 250, 1, 255, 1]], [[111, 0, -18, 240, 250, 1, 255, 1]]], "name": "Claw Special", "position": 1, "timings": [{"flashColor": [255, 119, 153, 187], "flashDuration": 3, "flashScope": 2, "frame": 0, "se": {"name": "Attack3", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 119, 153, 187], "flashDuration": 3, "flashScope": 1, "frame": 1, "se": {"name": "Slash2", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 3, "se": {"name": "Attack2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 119, 153, 187], "flashDuration": 3, "flashScope": 1, "frame": 4, "se": {"name": "Slash3", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 6, "se": {"name": "Attack2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 119, 153, 187], "flashDuration": 3, "flashScope": 1, "frame": 7, "se": {"name": "Slash2", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 9, "se": {"name": "Attack2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 119, 153, 187], "flashDuration": 3, "flashScope": 1, "frame": 10, "se": {"name": "Slash3", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 16, "se": {"name": "Attack2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 119, 153, 187], "flashDuration": 15, "flashScope": 1, "frame": 17, "se": {"name": "Slash1", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [255, 119, 153, 187], "flashDuration": 5, "flashScope": 2, "frame": 18, "se": {"name": "Monster2", "pan": 0, "pitch": 115, "volume": 90}}]}, {"id": 29, "animation1Hue": 0, "animation1Name": "ArrowSpecial", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 130, -160, 300, -135, 0, 255, 0]], [[1, 130, -160, 340, -135, 0, 255, 0]], [[2, 130, -160, 340, -135, 0, 255, 0], [0, 150, 90, 300, -60, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [1, 150, 90, 340, -60, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [2, 150, 90, 340, -60, 0, 255, 0], [0, -200, -40, 300, 100, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [1, -200, -40, 340, 100, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [2, -200, -40, 340, 100, 0, 255, 0], [0, -130, 150, 300, 40, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [1, -130, 150, 340, 40, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [2, -130, 150, 340, 40, 0, 255, 0], [0, -100, -190, 300, 150, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [3, -130, 150, 340, 40, 0, 255, 0], [1, -100, -190, 340, 150, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [3, -130, 150, 340, 40, 0, 255, 0], [2, -100, -190, 340, 150, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [3, -130, 150, 340, 40, 0, 255, 0], [3, -100, -190, 340, 150, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [3, -130, 150, 340, 40, 0, 255, 0], [3, -100, -190, 340, 150, 0, 255, 0], [4, 0, -48, 600, 0, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [3, -130, 150, 340, 40, 0, 255, 0], [3, -100, -190, 340, 150, 0, 255, 0], [4, 0, -32, 380, 0, 0, 255, 0]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [3, -130, 150, 340, 40, 0, 255, 0], [3, -100, -190, 340, 150, 0, 255, 0], [5, 0, -20, 380, 0, 0, 255, 1]], [[3, 130, -160, 340, -135, 0, 255, 0], [3, 150, 90, 340, -60, 0, 255, 0], [3, -200, -40, 340, 100, 0, 255, 0], [3, -130, 150, 340, 40, 0, 255, 0], [3, -100, -190, 340, 150, 0, 255, 0], [6, 0, -20, 400, 0, 0, 255, 1], [8, 0, 0, 380, 0, 0, 255, 1]], [[3, 130, -160, 340, -135, 0, 180, 0], [3, 150, 90, 340, -60, 0, 180, 0], [3, -200, -40, 340, 100, 0, 180, 0], [3, -130, 150, 340, 40, 0, 180, 0], [3, -100, -190, 340, 150, 0, 180, 0], [7, 0, -20, 400, 0, 0, 255, 1], [9, 0, 0, 380, 0, 0, 255, 1]], [[3, 130, -160, 340, -135, 0, 100, 0], [3, 150, 90, 340, -60, 0, 100, 0], [3, -200, -40, 340, 100, 0, 100, 0], [3, -130, 150, 340, 40, 0, 100, 0], [3, -100, -190, 340, 150, 0, 100, 0], [7, 0, -20, 400, 0, 0, 255, 1], [10, 0, 0, 380, 0, 0, 255, 1]], [[3, 130, -160, 340, -135, 0, 0, 0], [3, 150, 90, 340, -60, 0, 0, 0], [3, -200, -40, 340, 100, 0, 0, 0], [3, -130, 150, 340, 40, 0, 0, 0], [3, -100, -190, 340, 150, 0, 0, 0], [7, 0, -20, 380, 0, 0, 255, 1]], [[3, 130, -160, 340, -135, 0, 0, 0], [3, 150, 90, 340, -60, 0, 0, 0], [3, -200, -40, 340, 100, 0, 0, 0], [3, -130, 150, 340, 40, 0, 0, 0], [3, -100, -190, 340, 150, 0, 0, 0], [7, 0, -20, 380, 0, 0, 255, 1]], [[3, 130, -160, 340, -135, 0, 0, 0], [3, 150, 90, 340, -60, 0, 0, 0], [3, -200, -40, 340, 100, 0, 0, 0], [3, -130, 150, 340, 40, 0, 0, 0], [3, -100, -190, 340, 150, 0, 0, 0], [7, 0, -20, 380, 0, 0, 180, 1]], [[3, 130, -160, 340, -135, 0, 0, 0], [3, 150, 90, 340, -60, 0, 0, 0], [3, -200, -40, 340, 100, 0, 0, 0], [3, -130, 150, 340, 40, 0, 0, 0], [3, -100, -190, 340, 150, 0, 0, 0], [7, 0, -20, 380, 0, 0, 100, 1]]], "name": "Arrow Special", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Crossbow", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 204, 204], "flashDuration": 3, "flashScope": 1, "frame": 2, "se": {"name": "Attack3", "pan": 0, "pitch": 180, "volume": 90}}, {"flashColor": [255, 255, 204, 204], "flashDuration": 3, "flashScope": 1, "frame": 5, "se": {"name": "Attack3", "pan": 0, "pitch": 180, "volume": 90}}, {"flashColor": [255, 255, 204, 204], "flashDuration": 3, "flashScope": 1, "frame": 8, "se": {"name": "Attack3", "pan": 0, "pitch": 180, "volume": 90}}, {"flashColor": [255, 255, 204, 255], "flashDuration": 7, "flashScope": 1, "frame": 13, "se": {"name": "Attack3", "pan": 0, "pitch": 180, "volume": 90}}, {"flashColor": [255, 255, 204, 204], "flashDuration": 5, "flashScope": 2, "frame": 13, "se": {"name": "Wind6", "pan": 0, "pitch": 180, "volume": 90}}]}, {"id": 30, "animation1Hue": 0, "animation1Name": "Special1", "animation2Hue": 0, "animation2Name": "Special2", "frames": [[[118, -48, 12, 195, 0, 1, 255, 1]], [[119, -48, 12, 195, 0, 1, 255, 1], [124, 0, 12, 150, 0, 0, 255, 1]], [[120, -48, 12, 195, 0, 1, 255, 1], [125, 0, 12, 150, 0, 0, 255, 1]], [[121, -48, 12, 195, 0, 1, 255, 1], [126, 0, 12, 150, 0, 0, 255, 1]], [[122, -48, 12, 195, 0, 1, 255, 1], [0, 48, -24, 195, 0, 0, 255, 1], [127, 0, 12, 150, 0, 0, 255, 1]], [[123, -48, 12, 195, 0, 1, 255, 1], [1, 48, -24, 195, 0, 0, 255, 1], [128, 0, 12, 150, 0, 0, 255, 1], [124, -24, -12, 180, 0, 0, 255, 1]], [[2, 48, -24, 195, 0, 0, 255, 1], [125, -24, -12, 195, 0, 0, 255, 1]], [[3, 48, -24, 195, 0, 0, 255, 1], [106, -36, -24, 195, 0, 0, 255, 1], [126, -24, -12, 195, 0, 0, 255, 1]], [[4, 48, -24, 195, 0, 0, 255, 1], [107, -36, -24, 195, 0, 0, 255, 1], [127, -24, -12, 195, 0, 0, 255, 1]], [[5, 48, -24, 195, 0, 0, 255, 1], [108, -36, -24, 195, 0, 0, 255, 1], [128, -24, -12, 195, 0, 0, 255, 1], [124, 12, 0, 195, 0, 0, 255, 1]], [[109, -36, -24, 195, 0, 0, 255, 1], [112, 12, 12, 225, 0, 0, 255, 1], [125, 12, 0, 195, 0, 0, 255, 1]], [[110, -36, -24, 195, 0, 0, 255, 1], [113, 12, 12, 225, 0, 0, 255, 1], [126, 12, 0, 195, 0, 0, 255, 1]], [[111, -36, -24, 195, 0, 0, 255, 1], [114, 12, 12, 225, 0, 0, 255, 1], [127, 12, 0, 195, 0, 0, 255, 1], [124, -36, -12, 195, 0, 0, 255, 1]], [[115, 12, 12, 225, 0, 0, 255, 1], [128, 12, 0, 195, 0, 0, 255, 1], [125, -36, -12, 195, 0, 0, 255, 1]], [[116, 12, 12, 225, 0, 0, 255, 1], [100, 0, -120, 225, 0, 0, 255, 1], [126, -36, -12, 195, 0, 0, 255, 1]], [[117, 12, 12, 225, 0, 0, 255, 1], [101, 0, -84, 225, 0, 0, 255, 1], [102, 0, -60, 225, 0, 0, 255, 1], [127, -36, -12, 195, 0, 0, 255, 1], [124, 0, 0, 225, 0, 0, 255, 1]], [[103, 0, -48, 225, 0, 0, 255, 1], [128, -36, -12, 195, 0, 0, 255, 1], [125, 0, 0, 225, 0, 0, 255, 1]], [[104, 0, -36, 225, 0, 0, 255, 1], [126, 0, 0, 225, 0, 0, 255, 1]], [[105, 0, -36, 225, 0, 0, 200, 1], [127, 0, 0, 225, 0, 0, 255, 1]], [[128, 0, 0, 225, 0, 0, 255, 1]]], "name": "General Special 1", "position": 1, "timings": [{"flashColor": [255, 255, 204, 187], "flashDuration": 3, "flashScope": 2, "frame": 0, "se": null}, {"flashColor": [255, 170, 119, 204], "flashDuration": 3, "flashScope": 1, "frame": 1, "se": {"name": "Slash1", "pan": 0, "pitch": 110, "volume": 100}}, {"flashColor": [255, 255, 255, 204], "flashDuration": 3, "flashScope": 1, "frame": 5, "se": {"name": "Slash4", "pan": 0, "pitch": 90, "volume": 100}}, {"flashColor": [255, 170, 119, 204], "flashDuration": 3, "flashScope": 1, "frame": 8, "se": {"name": "Slash5", "pan": 0, "pitch": 90, "volume": 100}}, {"flashColor": [255, 255, 255, 187], "flashDuration": 3, "flashScope": 1, "frame": 12, "se": {"name": "Slash2", "pan": 0, "pitch": 110, "volume": 100}}, {"flashColor": [255, 255, 187, 221], "flashDuration": 2, "flashScope": 1, "frame": 15, "se": {"name": "Slash1", "pan": 0, "pitch": 120, "volume": 100}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 3, "flashScope": 2, "frame": 15, "se": null}, {"flashColor": [255, 255, 187, 221], "flashDuration": 2, "flashScope": 1, "frame": 17, "se": {"name": "Damage2", "pan": 0, "pitch": 110, "volume": 100}}]}, {"id": 31, "animation1Hue": 0, "animation1Name": "Special3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, -42, 64, 200, 0, 0, 255, 1]], [[1, -42, 64, 200, 0, 0, 255, 1]], [[2, -42, 64, 200, 0, 0, 255, 1]], [[3, -42, 64, 200, 0, 0, 255, 1]], [[4, -42, 64, 200, 0, 0, 255, 1]], [[5, -42, 64, 200, 0, 0, 255, 1]], [[6, -42, 64, 200, 0, 0, 255, 1]], [[7, -42, 64, 200, 0, 0, 255, 1]], [[8, -42, 64, 200, 0, 0, 255, 1]], [[9, -42, 64, 200, 0, 0, 255, 1]], [[10, -42, 64, 200, 0, 0, 255, 1], [0, 2, -48, 200, 0, 1, 255, 1]], [[11, -42, 64, 200, 0, 0, 255, 1], [1, 2, -48, 200, 0, 1, 255, 1]], [[12, -42, 64, 200, 0, 0, 255, 1], [2, 2, -48, 200, 0, 1, 255, 1]], [[13, -42, 64, 200, 0, 0, 255, 1], [3, 2, -48, 200, 0, 1, 255, 1]], [[14, -42, 64, 200, 0, 0, 255, 1], [4, 2, -48, 200, 0, 1, 255, 1]], [[15, -42, 64, 200, 0, 0, 255, 1], [5, 2, -48, 200, 0, 1, 255, 1]], [[16, -42, 64, 200, 0, 0, 255, 1], [6, 2, -48, 200, 0, 1, 255, 1]], [[17, -42, 64, 200, 0, 0, 255, 1], [7, 2, -48, 200, 0, 1, 255, 1]], [[18, -42, 64, 200, 0, 0, 255, 1], [8, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [9, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [10, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [11, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [12, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [13, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [14, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [15, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [16, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [17, 2, -48, 200, 0, 1, 255, 1]], [[-1, 150, 100, 0, 0, 0, 0, 0], [18, 2, -48, 200, 0, 1, 255, 1]]], "name": "General Special 2", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder3", "pan": 0, "pitch": 90, "volume": 90}}, {"flashColor": [187, 238, 255, 170], "flashDuration": 2, "flashScope": 2, "frame": 1, "se": {"name": "Slash1", "pan": 0, "pitch": 110, "volume": 100}}, {"flashColor": [170, 238, 255, 221], "flashDuration": 5, "flashScope": 1, "frame": 1, "se": null}, {"flashColor": [170, 238, 255, 221], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [187, 238, 255, 170], "flashDuration": 2, "flashScope": 2, "frame": 5, "se": {"name": "Slash1", "pan": 0, "pitch": 110, "volume": 100}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 10, "se": {"name": "Thunder3", "pan": 0, "pitch": 90, "volume": 90}}, {"flashColor": [170, 238, 255, 221], "flashDuration": 5, "flashScope": 1, "frame": 11, "se": null}, {"flashColor": [187, 238, 255, 170], "flashDuration": 2, "flashScope": 2, "frame": 11, "se": {"name": "Powerup", "pan": 0, "pitch": 180, "volume": 100}}, {"flashColor": [170, 238, 255, 221], "flashDuration": 5, "flashScope": 1, "frame": 15, "se": null}, {"flashColor": [187, 238, 255, 170], "flashDuration": 2, "flashScope": 2, "frame": 15, "se": {"name": "Slash3", "pan": 0, "pitch": 100, "volume": 100}}]}, {"id": 32, "animation1Hue": 0, "animation1Name": "Breath", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -96, 300, 0, 0, 255, 1]], [[1, 0, -96, 300, 0, 0, 255, 1]], [[2, 0, -96, 300, 0, 0, 255, 1]], [[3, 0, -96, 300, 0, 0, 255, 1]], [[4, 0, -96, 375, 0, 0, 255, 1]], [[5, 0, -96, 450, 0, 0, 255, 1]], [[5, 0, -96, 675, 0, 0, 255, 1]], [[6, 0, -96, 525, 0, 0, 255, 1]], [[7, 0, -96, 525, 0, 0, 255, 1]], [[8, 12, -96, 525, 0, 0, 255, 1]], [[9, 0, -84, 525, 0, 0, 255, 1], [5, 0, -96, 525, 0, 0, 255, 1]], [[6, 0, -96, 600, 0, 0, 255, 1]], [[7, 0, -96, 675, 0, 0, 255, 1]], [[8, 0, -96, 675, 0, 0, 255, 1]], [[9, 0, -96, 675, 0, 0, 255, 1]], [[10, 0, -96, 600, 0, 0, 255, 1]], [[11, 0, -96, 600, 0, 0, 255, 1]], [[12, 0, -96, 525, 0, 0, 255, 1]], [[13, 0, -96, 495, 0, 0, 255, 1]], [[14, 0, -96, 450, 0, 0, 255, 1]]], "name": "Breath", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Fire3", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Thunder1", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 102, 0, 204], "flashDuration": 10, "flashScope": 2, "frame": 5, "se": null}]}, {"id": 33, "animation1Hue": 0, "animation1Name": "<PERSON><PERSON>", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -180, 150, 0, 0, 255, 1], [0, -288, -96, 150, 0, 0, 255, 1], [0, 288, -96, 150, 0, 0, 255, 1]], [[1, 0, -180, 150, 0, 0, 255, 1], [1, -288, -96, 150, 0, 0, 255, 1], [1, 288, -96, 150, 0, 0, 255, 1]], [[2, 0, -168, 195, 0, 0, 255, 1], [2, -276, -84, 195, 0, 0, 255, 1], [2, 288, -84, 195, 0, 0, 255, 1]], [[3, 0, -168, 195, 0, 0, 255, 1], [3, -276, -84, 195, 0, 0, 255, 1], [3, 288, -84, 195, 0, 0, 255, 1]], [[4, 0, -168, 195, 0, 0, 255, 1], [4, -276, -84, 195, 0, 0, 255, 1], [4, 288, -84, 195, 0, 0, 255, 1]], [[5, 0, -168, 195, 0, 0, 255, 1], [5, -276, -84, 195, 0, 0, 255, 1], [5, 288, -84, 195, 0, 0, 255, 1], [0, -168, -168, 150, 0, 0, 255, 1], [0, 168, -168, 150, 0, 0, 255, 1]], [[6, 0, -168, 195, 0, 0, 255, 1], [6, -276, -84, 195, 0, 0, 255, 1], [6, 288, -84, 195, 0, 0, 255, 1], [1, -168, -168, 150, 0, 0, 255, 1], [1, 168, -168, 150, 0, 0, 255, 1]], [[7, 0, -168, 195, 0, 0, 255, 1], [7, -276, -84, 195, 0, 0, 255, 1], [7, 288, -84, 195, 0, 0, 255, 1], [2, -168, -156, 195, 0, 0, 255, 1], [2, 168, -156, 195, 0, 0, 255, 1]], [[8, 0, -168, 195, 0, 0, 255, 1], [8, -276, -84, 195, 0, 0, 255, 1], [8, 288, -84, 195, 0, 0, 255, 1], [3, -168, -156, 195, 0, 0, 255, 1], [3, 168, -156, 195, 0, 0, 255, 1]], [[9, 0, -168, 210, 0, 0, 200, 1], [9, -276, -84, 210, 0, 0, 200, 1], [9, 288, -84, 210, 0, 0, 200, 1], [4, -168, -156, 195, 0, 0, 255, 1], [4, 168, -156, 195, 0, 0, 255, 1]], [[10, 0, -168, 213, 0, 0, 255, 1], [10, -276, -84, 213, 0, 0, 255, 1], [10, 288, -84, 213, 0, 0, 255, 1], [5, -168, -156, 195, 0, 0, 255, 1], [5, 168, -156, 195, 0, 0, 255, 1]], [[11, -276, -84, 213, 0, 0, 255, 1], [11, 288, -84, 213, 0, 0, 255, 1], [11, 0, -168, 213, 0, 0, 255, 1], [6, -168, -156, 195, 0, 0, 255, 1], [6, 168, -156, 195, 0, 0, 255, 1]], [[12, -276, -84, 213, 0, 0, 255, 1], [12, 288, -84, 213, 0, 0, 255, 1], [12, 0, -168, 213, 0, 0, 255, 1], [7, -168, -156, 195, 0, 0, 255, 1], [7, 168, -156, 195, 0, 0, 255, 1], [8, 0, -24, 195, 0, 0, 100, 1]], [[13, -276, -84, 213, 0, 0, 255, 1], [13, 288, -84, 213, 0, 0, 255, 1], [13, 0, -168, 213, 0, 0, 255, 1], [8, -168, -156, 195, 0, 0, 255, 1], [8, 168, -156, 195, 0, 0, 255, 1], [9, 0, -24, 195, 0, 0, 150, 1]], [[9, -168, -156, 195, 0, 0, 255, 1], [9, 168, -156, 195, 0, 0, 255, 1], [10, 0, -24, 195, 0, 0, 255, 1]], [[10, -168, -156, 195, 0, 0, 255, 1], [10, 168, -156, 195, 0, 0, 255, 1], [11, 0, -24, 195, 0, 0, 255, 1]], [[11, -168, -156, 195, 0, 0, 255, 1], [11, 168, -156, 195, 0, 0, 255, 1], [12, 0, -24, 195, 0, 0, 255, 1]], [[12, -168, -156, 195, 0, 0, 255, 1], [12, 168, -156, 195, 0, 0, 255, 1], [13, 0, -24, 195, 0, 0, 255, 1]], [[13, -168, -156, 195, 0, 0, 255, 1], [13, 168, -156, 195, 0, 0, 255, 1]]], "name": "<PERSON><PERSON>", "position": 3, "timings": [{"flashColor": [238, 136, 204, 187], "flashDuration": 25, "flashScope": 2, "frame": 0, "se": {"name": "Darkness4", "pan": 0, "pitch": 60, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Darkness3", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 85, 238, 204], "flashDuration": 2, "flashScope": 1, "frame": 3, "se": null}, {"flashColor": [255, 85, 238, 204], "flashDuration": 2, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [255, 85, 238, 187], "flashDuration": 2, "flashScope": 1, "frame": 7, "se": null}, {"flashColor": [255, 85, 238, 170], "flashDuration": 2, "flashScope": 1, "frame": 9, "se": null}, {"flashColor": [255, 85, 238, 153], "flashDuration": 2, "flashScope": 1, "frame": 11, "se": null}]}, {"id": 34, "animation1Hue": 0, "animation1Name": "Sonic", "animation2Hue": 0, "animation2Name": "", "frames": [[[5, -132, -156, 225, 0, 0, 150, 1]], [[4, -132, -156, 225, 0, 0, 200, 1]], [[3, -132, -156, 225, 0, 0, 255, 1], [5, 276, -72, 225, 0, 0, 150, 1]], [[5, -132, -156, 225, 0, 0, 255, 1], [5, -240, -48, 225, 0, 0, 150, 1], [4, 276, -72, 225, 0, 0, 200, 1]], [[4, -132, -156, 225, 0, 0, 255, 1], [4, -240, -48, 225, 0, 0, 200, 1], [3, 276, -72, 225, 0, 0, 255, 1]], [[3, -132, -156, 225, 0, 0, 255, 1], [3, -240, -48, 225, 0, 0, 255, 1], [5, 276, -72, 225, 0, 0, 255, 1]], [[5, -132, -156, 225, 0, 0, 255, 1], [5, -240, -48, 225, 0, 0, 255, 1], [4, 276, -72, 225, 0, 0, 255, 1], [5, 108, -132, 225, 0, 0, 150, 1]], [[4, -132, -156, 225, 0, 0, 255, 1], [4, -240, -48, 225, 0, 0, 255, 1], [3, 276, -72, 225, 0, 0, 255, 1], [4, 108, -132, 225, 0, 0, 200, 1]], [[3, -132, -156, 225, 0, 0, 255, 1], [3, -240, -48, 225, 0, 0, 255, 1], [5, 276, -72, 225, 0, 0, 255, 1], [3, 108, -132, 225, 0, 0, 255, 1]], [[2, -132, -156, 225, 0, 0, 255, 1], [5, -240, -48, 225, 0, 0, 255, 1], [4, 276, -72, 225, 0, 0, 255, 1], [5, 108, -132, 225, 0, 0, 255, 1]], [[1, -132, -156, 225, 0, 0, 255, 1], [4, -240, -48, 225, 0, 0, 255, 1], [3, 276, -72, 225, 0, 0, 255, 1], [4, 108, -132, 225, 0, 0, 255, 1]], [[0, -132, -156, 225, 0, 0, 255, 1], [3, -240, -48, 225, 0, 0, 255, 1], [2, 276, -72, 225, 0, 0, 255, 1], [3, 108, -132, 225, 0, 0, 255, 1]], [[0, -132, -156, 195, 0, 0, 150, 1], [2, -240, -48, 225, 0, 0, 255, 1], [1, 276, -72, 225, 0, 0, 255, 1], [5, 108, -132, 225, 0, 0, 255, 1]], [[1, -240, -48, 225, 0, 0, 255, 1], [0, 276, -72, 225, 0, 0, 255, 1], [4, 108, -132, 225, 0, 0, 255, 1]], [[0, -240, -48, 225, 0, 0, 255, 1], [0, 276, -72, 195, 0, 0, 150, 1], [3, 108, -132, 225, 0, 0, 255, 1]], [[0, -240, -48, 195, 0, 0, 150, 1], [2, 108, -132, 225, 0, 0, 255, 1]], [[1, 108, -132, 225, 0, 0, 255, 1]], [[0, 108, -132, 225, 0, 0, 255, 1]], [[0, 108, -132, 195, 0, 0, 150, 1]]], "name": "Sonic Wave", "position": 3, "timings": [{"flashColor": [255, 255, 119, 119], "flashDuration": 10, "flashScope": 2, "frame": 0, "se": {"name": "Sound3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 221, 170, 170], "flashDuration": 4, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [255, 221, 170, 170], "flashDuration": 4, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [255, 221, 170, 170], "flashDuration": 4, "flashScope": 1, "frame": 8, "se": null}, {"flashColor": [255, 221, 170, 170], "flashDuration": 5, "flashScope": 1, "frame": 11, "se": null}]}, {"id": 35, "animation1Hue": 0, "animation1Name": "Mist", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, -228, -36, 600, 0, 0, 50, 1], [1, 264, -36, 600, 0, 0, 50, 1]], [[0, -204, -36, 600, 0, 0, 100, 1], [1, 240, -36, 600, 0, 0, 100, 1]], [[0, -180, -36, 600, 0, 0, 125, 1], [1, 216, -36, 600, 0, 0, 125, 1]], [[0, -156, -36, 600, 0, 0, 125, 1], [1, 192, -36, 600, 0, 0, 125, 1]], [[0, -132, -36, 600, 0, 0, 125, 1], [1, 168, -36, 600, 0, 0, 125, 1]], [[0, -108, -36, 600, 0, 0, 125, 1], [1, 144, -36, 600, 0, 0, 125, 1]], [[0, -84, -36, 600, 0, 0, 125, 1], [1, 120, -36, 600, 0, 0, 125, 1]], [[0, -60, -36, 600, 0, 0, 125, 1], [1, 96, -36, 600, 0, 0, 125, 1]], [[0, -36, -36, 600, 0, 0, 125, 1], [1, 72, -36, 600, 0, 0, 125, 1]], [[0, -12, -36, 600, 0, 0, 125, 1], [1, 48, -36, 600, 0, 0, 125, 1]], [[0, 12, -36, 600, 0, 0, 125, 1], [1, 24, -36, 600, 0, 0, 125, 1]], [[0, 36, -36, 600, 0, 0, 125, 1], [1, 0, -36, 600, 0, 0, 125, 1]], [[0, 60, -36, 600, 0, 0, 125, 1], [1, -24, -36, 600, 0, 0, 125, 1]], [[0, 72, -36, 600, 0, 0, 100, 1], [1, -48, -36, 600, 0, 0, 100, 1]], [[0, 72, -36, 600, 0, 0, 50, 1], [1, -72, -36, 600, 0, 0, 50, 1]]], "name": "Fog", "position": 3, "timings": [{"flashColor": [136, 153, 170, 204], "flashDuration": 15, "flashScope": 1, "frame": 0, "se": {"name": "Blind", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [255, 255, 255, 170], "flashDuration": 15, "flashScope": 2, "frame": 0, "se": {"name": "Sand", "pan": 0, "pitch": 110, "volume": 90}}]}, {"id": 36, "animation1Hue": 0, "animation1Name": "Song", "animation2Hue": 0, "animation2Name": "", "frames": [[[3, -168, 0, 300, 0, 0, 100, 1], [3, -108, -84, 300, 0, 0, 100, 1], [0, -276, -24, 90, 0, 0, 100, 1]], [[4, -168, 0, 300, 0, 0, 150, 1], [4, -84, -84, 300, 0, 0, 150, 1], [0, -252, -48, 90, 0, 0, 150, 1]], [[5, -168, 0, 300, 0, 0, 200, 1], [3, 180, 0, 300, 0, 0, 100, 1], [5, -60, -84, 300, 0, 0, 200, 1], [0, -228, -60, 90, 0, 0, 200, 1]], [[6, -168, 0, 300, 0, 0, 255, 1], [4, 180, 0, 300, 0, 0, 150, 1], [6, -36, -84, 300, 0, 0, 255, 1], [0, -192, -60, 90, 0, 0, 255, 1], [1, -84, -168, 150, 0, 0, 50, 1]], [[7, -168, 0, 300, 0, 0, 255, 1], [5, 180, 0, 300, 0, 0, 200, 1], [7, -12, -84, 300, 0, 0, 255, 1], [0, -156, -48, 90, 0, 0, 255, 1], [1, -48, -156, 150, 0, 0, 100, 1]], [[8, -168, 0, 300, 0, 0, 200, 1], [6, 180, 0, 300, 0, 0, 255, 1], [8, 12, -84, 300, 0, 0, 200, 1], [0, -120, -24, 90, 0, 0, 255, 1], [1, -24, -144, 150, 0, 0, 150, 1]], [[9, -168, 0, 300, 0, 0, 150, 1], [7, 180, 0, 300, 0, 0, 255, 1], [9, 36, -84, 300, 0, 0, 150, 1], [0, -96, -12, 90, 0, 0, 255, 1], [1, 12, -144, 150, 0, 0, 150, 1]], [[10, -168, 0, 300, 0, 0, 100, 1], [8, 180, 0, 300, 0, 0, 200, 1], [10, 60, -84, 300, 0, 0, 100, 1], [0, -72, -12, 90, 0, 0, 255, 1], [1, 48, -156, 150, 0, 0, 100, 1], [2, -312, -144, 105, 0, 0, 100, 1], [2, 156, 24, 90, 0, 0, 100, 1]], [[9, 180, 0, 300, 0, 0, 150, 1], [0, -48, -24, 90, 0, 0, 255, 1], [1, 84, -168, 150, 0, 0, 100, 1], [2, -288, -132, 105, 0, 0, 150, 1], [2, 204, 0, 105, 0, 0, 150, 1]], [[3, -168, 0, 300, 0, 0, 100, 1], [3, -108, -84, 300, 0, 0, 100, 1], [10, 180, 0, 300, 0, 0, 100, 1], [0, -12, -36, 90, 0, 0, 255, 1], [2, -264, -84, 105, 0, 0, 200, 1], [2, 240, -24, 105, 0, 0, 200, 1], [1, 120, -180, 150, 0, 0, 100, 1]], [[4, -168, 0, 300, 0, 0, 180, 1], [4, -84, -84, 300, 0, 0, 180, 1], [0, 12, -48, 90, 0, 0, 180, 1], [2, -228, -24, 105, 0, 0, 180, 1], [2, 264, -60, 105, 0, 0, 180, 1], [1, 156, -192, 150, 0, 0, 180, 1]], [[5, -168, 0, 300, 0, 0, 150, 1], [-1, 408, 29.5, 300, 0, 0, 150, 1], [5, -60, -84, 300, 0, 0, 150, 1], [0, 36, -60, 90, 0, 0, 150, 1], [2, -204, 0, 105, 0, 0, 150, 1], [2, 300, -96, 105, 0, 0, 150, 1], [1, 192, -192, 150, 0, 0, 150, 1]], [[-1, -405.5, 45, 300, 0, 0, 80, 1], [-1, 180, -2.5, 300, 0, 0, 80, 1], [-1, -222.5, 63.5, 300, 0, 0, 80, 1], [0, 48, -80, 90, 0, 0, 180, 1], [2, -192, 16, 105, 0, 0, 180, 1], [2, 320, -112, 105, 0, 0, 180, 1], [1, 208, -176, 150, 0, 0, 180, 1]], [[-1, -405.5, 45, 300, 0, 0, 80, 1], [-1, 180, -2.5, 300, 0, 0, 80, 1], [-1, -222.5, 63.5, 300, 0, 0, 80, 1], [0, 64, -96, 90, 0, 0, 80, 1], [2, -176, 32, 105, 0, 0, 80, 1], [2, 336, -128, 105, 0, 0, 80, 1], [1, 224, -160, 150, 0, 0, 80, 1]]], "name": "Song", "position": 3, "timings": [{"flashColor": [221, 221, 255, 136], "flashDuration": 10, "flashScope": 2, "frame": 0, "se": {"name": "Ice5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 170], "flashDuration": 3, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [255, 170, 255, 170], "flashDuration": 3, "flashScope": 1, "frame": 5, "se": null}]}, {"id": 37, "animation1Hue": 0, "animation1Name": "Howl", "animation2Hue": 40, "animation2Name": "Recovery4", "frames": [[[6, 0, 0, 225, 0, 0, 255, 1]], [[7, 0, 0, 225, 0, 0, 255, 1], [116, 0, 0, 225, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [6, 0, 0, 150, 0, 0, 255, 1], [117, 0, 0, 300, 0, 0, 255, 1]], [[7, 0, 0, 225, 0, 0, 255, 1], [7, 0, 0, 900, 0, 0, 150, 1], [117, 0, 0, 315, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [6, 0, 0, 150, 0, 0, 255, 1], [117, 0, 0, 330, 0, 0, 255, 1]], [[7, 0, 0, 225, 0, 0, 255, 1], [7, 0, 0, 900, 0, 0, 150, 1], [117, 0, 0, 345, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [6, 0, 0, 150, 0, 0, 255, 1], [117, 0, 0, 360, 0, 0, 255, 1]], [[7, 0, 0, 225, 0, 0, 255, 1], [7, 0, 0, 900, 0, 0, 150, 1], [117, 0, 0, 375, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [6, 0, 0, 150, 0, 0, 255, 1], [117, 0, 0, 390, 0, 0, 255, 1]], [[7, 0, 0, 225, 0, 0, 255, 1], [7, 0, 0, 900, 0, 0, 150, 1], [117, 0, 0, 405, 0, 0, 200, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [117, 0, 0, 420, 0, 0, 150, 1]], [[7, 0, 0, 900, 0, 0, 150, 1], [117, 0, 0, 420, 0, 0, 100, 1]]], "name": "Shout", "position": 3, "timings": [{"flashColor": [238, 255, 238, 136], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": null}, {"flashColor": [170, 255, 255, 153], "flashDuration": 2, "flashScope": 1, "frame": 0, "se": {"name": "Monster1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [170, 255, 255, 136], "flashDuration": 2, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [170, 255, 255, 136], "flashDuration": 2, "flashScope": 1, "frame": 4, "se": null}, {"flashColor": [170, 255, 255, 136], "flashDuration": 2, "flashScope": 1, "frame": 6, "se": null}, {"flashColor": [170, 255, 255, 136], "flashDuration": 2, "flashScope": 1, "frame": 8, "se": null}]}, {"id": 38, "animation1Hue": 0, "animation1Name": "<PERSON><PERSON>", "animation2Hue": 0, "animation2Name": "Special2", "frames": [[[0, 0, -96, 225, 0, 0, 255, 1]], [[1, 0, -96, 225, 0, 0, 255, 1]], [[2, 0, -96, 225, 0, 0, 255, 1], [124, 0, -48, 180, 0, 0, 255, 1]], [[3, 0, -96, 225, 0, 0, 255, 1], [125, 0, -48, 180, 0, 0, 255, 1]], [[4, 0, -96, 225, 0, 0, 255, 1], [126, 0, -48, 180, 0, 0, 255, 1]], [[5, 0, -96, 225, 0, 0, 255, 1], [127, 0, -48, 180, 0, 0, 255, 1]], [[128, 0, -48, 180, 0, 0, 255, 1]]], "name": "Sweep", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind7", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 136, 102, 255], "flashDuration": 3, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [255, 255, 255, 119], "flashDuration": 2, "flashScope": 2, "frame": 2, "se": {"name": "Blow2", "pan": 0, "pitch": 70, "volume": 100}}]}, {"id": 39, "animation1Hue": 0, "animation1Name": "Howl", "animation2Hue": 200, "animation2Name": "Cure4", "frames": [[[9, 0, 0, 300, 0, 0, 150, 1]], [[8, 0, 0, 300, 0, 0, 100, 1], [7, 0, 0, 225, 0, 0, 150, 1]], [[9, 0, 0, 300, 0, 0, 150, 1], [6, 0, 0, 195, 0, 0, 200, 1], [0, 0, 0, 180, 0, 0, 255, 1]], [[8, 0, 0, 300, 0, 0, 100, 1], [1, 0, 0, 225, 0, 0, 255, 1], [112, 0, 0, 75, 0, 0, 255, 1]], [[2, 0, 0, 225, 0, 0, 255, 1], [1, 0, 0, 225, 0, 0, 150, 1], [112, 0, 0, 180, 0, 0, 150, 1], [9, 0, 0, 300, 0, 0, 50, 1]], [[3, 0, 0, 225, 0, 0, 255, 1], [4, 0, 0, 195, 0, 1, 200, 1], [112, 0, 0, 180, 0, 0, 100, 1]], [[3, 0, 0, 240, 0, 0, 150, 1], [4, 0, 0, 240, 0, 1, 150, 1]], [[3, 0, 0, 247, 0, 0, 100, 1], [5, 0, 0, 240, 0, 1, 150, 1]], [], []], "name": "Bodyslam", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder2", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Thunder8", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 119, 102, 221], "flashDuration": 5, "flashScope": 1, "frame": 2, "se": {"name": "Blow3", "pan": 0, "pitch": 50, "volume": 100}}, {"flashColor": [255, 238, 170, 136], "flashDuration": 2, "flashScope": 2, "frame": 3, "se": null}]}, {"id": 40, "animation1Hue": 0, "animation1Name": "Flash", "animation2Hue": 0, "animation2Name": "", "frames": [[[1, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[2, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[3, 0, -10, 260, 0, 0, 255, 1], [6, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[4, 0, -10, 260, 0, 0, 255, 1], [7, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[5, 0, -10, 260, 0, 0, 255, 1], [8, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[9, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -10, 0, 0, 0, 0, 0], [11, 0, -10, 280, 0, 0, 255, 1]], [[10, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -10, 0, 0, 0, 0, 0], [12, 0, -10, 280, 0, 0, 255, 1]], [[10, 0, -10, 300, -20, 0, 255, 1], [-1, 0, -10, 0, 0, 0, 0, 0], [13, 0, -10, 280, 0, 0, 255, 1]], [[-1, 0, -10, 300, -20, 0, 255, 1], [-1, 0, -10, 0, 0, 0, 0, 0], [14, 0, -10, 330, 0, 0, 200, 1]], [[-1, 0, -10, 300, -20, 0, 255, 1], [-1, 0, -10, 0, 0, 0, 0, 0], [14, 0, -10, 430, 0, 0, 200, 1]]], "name": "Flash", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": null}, {"flashColor": [255, 255, 255, 255], "flashDuration": 3, "flashScope": 1, "frame": 0, "se": {"name": "Flash2", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Sword1", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 255, 255, 170], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": null}, {"flashColor": [255, 255, 255, 68], "flashDuration": 5, "flashScope": 1, "frame": 7, "se": null}, {"flashColor": [255, 255, 255, 119], "flashDuration": 5, "flashScope": 1, "frame": 8, "se": null}, {"flashColor": [255, 255, 255, 187], "flashDuration": 5, "flashScope": 1, "frame": 9, "se": null}]}, {"id": 41, "animation1Hue": 0, "animation1Name": "Recovery1", "animation2Hue": 0, "animation2Name": "", "frames": [[[20, 0, -210, 280, 0, 0, 255, 1]], [[21, 0, -210, 280, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1]], [[22, 0, -210, 280, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1]], [[23, 0, -210, 280, 0, 0, 255, 1], [20, 0, -200, 220, 0, 1, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1]], [[24, 0, -210, 280, 0, 0, 255, 1], [21, 0, -200, 220, 0, 1, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1]], [[25, 0, -210, 280, 0, 0, 255, 1], [22, 0, -200, 220, 0, 1, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1]], [[26, 0, -210, 280, 0, 0, 255, 1], [23, 0, -200, 220, 0, 1, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [0, 0, -212, 250, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1]], [[15, 0, -180, 230, 0, 0, 255, 1], [27, 0, -212, 280, 0, 0, 255, 1], [24, 0, -200, 220, 0, 1, 255, 1], [1, 0, -212, 250, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1]], [[16, 0, -180, 230, 0, 0, 255, 1], [28, 0, -212, 280, 0, 0, 255, 1], [25, 0, -200, 220, 0, 1, 255, 1], [2, 0, -212, 250, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1], [-1, -23, -201, 100, 0, 0, 255, 1]], [[29, 0, -212, 280, 0, 0, 255, 1], [17, 0, -180, 230, 0, 0, 255, 1], [26, 0, -200, 220, 0, 1, 255, 1], [3, 0, -212, 250, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1]], [[27, 0, -212, 280, 0, 0, 255, 1], [18, 0, -180, 230, 0, 0, 255, 1], [27, 0, -200, 220, 0, 1, 255, 1], [4, 0, -212, 250, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1]], [[28, 0, -212, 280, 0, 0, 255, 1], [19, 0, -180, 230, 0, 0, 255, 1], [5, 0, -212, 250, 0, 0, 255, 1], [28, 0, -200, 220, 0, 1, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1]], [[29, 0, -212, 280, 0, 0, 255, 1], [17, 0, -180, 230, 0, 0, 255, 1], [6, 0, -212, 250, 0, 0, 255, 1], [29, 0, -200, 220, 0, 1, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1]], [[27, 0, -212, 280, 0, 0, 255, 1], [18, 0, -180, 230, 0, 0, 255, 1], [27, 0, -200, 220, 0, 1, 255, 1], [7, 0, -212, 250, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1], [-1, 0, -180, 100, 0, 0, 255, 1]], [[28, 0, -212, 280, 0, 0, 255, 1], [19, 0, -180, 230, 0, 0, 255, 1], [28, 0, -200, 220, 0, 1, 255, 1], [8, 0, -212, 250, 0, 0, 255, 1]], [[29, 0, -212, 280, 0, 0, 255, 1], [17, 0, -180, 230, 0, 0, 255, 1], [29, 0, -200, 220, 0, 1, 255, 1], [9, 0, -212, 250, 0, 0, 255, 1]], [[27, 0, -212, 280, 0, 0, 255, 1], [18, 0, -180, 230, 0, 0, 255, 1], [27, 0, -200, 220, 0, 1, 255, 1], [10, 0, -212, 250, 0, 0, 255, 1]], [[28, 0, -212, 280, 0, 0, 200, 1], [19, 0, -180, 230, 0, 0, 200, 1], [28, 0, -200, 220, 0, 1, 200, 1], [11, 0, -212, 250, 0, 0, 255, 1]], [[29, 0, -212, 280, 0, 0, 127, 1], [17, 0, -180, 230, 0, 0, 127, 1], [29, 0, -200, 220, 0, 1, 127, 1], [12, 0, -212, 250, 0, 0, 255, 1], [-1, 0, -180, 0, 0, 0, 0, 0]], [[13, 0, -212, 250, 0, 0, 255, 1], [-1, 0, -180, 0, 0, 0, 0, 0], [-1, 0, -180, 0, 0, 0, 0, 0], [-1, 0, -180, 0, 0, 0, 0, 0]], [[14, 0, -212, 250, 0, 0, 255, 1], [-1, 0, -180, 0, 0, 0, 0, 0], [-1, 0, -180, 0, 0, 0, 0, 0], [-1, 0, -180, 0, 0, 0, 0, 0]]], "name": "Heal One 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Heal3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [187, 255, 255, 204], "flashDuration": 8, "flashScope": 1, "frame": 0, "se": {"name": "Saint2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [187, 255, 255, 204], "flashDuration": 10, "flashScope": 1, "frame": 6, "se": null}, {"flashColor": [187, 255, 255, 102], "flashDuration": 10, "flashScope": 2, "frame": 6, "se": null}]}, {"id": 42, "animation1Hue": 0, "animation1Name": "Recovery3", "animation2Hue": 230, "animation2Name": "Recovery4", "frames": [[[0, 0, 0, 150, 0, 0, 200, 1]], [[1, 0, 0, 150, 0, 0, 200, 1], [111, 0, 0, 150, 0, 0, 255, 1]], [[2, 0, 0, 150, 0, 0, 200, 1], [112, 0, 0, 150, 0, 0, 255, 1]], [[3, 0, 0, 150, 0, 0, 200, 1], [112, 0, 0, 165, 0, 0, 255, 1]], [[4, 0, 0, 150, 0, 0, 200, 1], [104, -108, -72, 150, 0, 0, 255, 1], [112, 0, 0, 150, 0, 0, 255, 1]], [[5, 0, 0, 153, 0, 0, 200, 1], [106, 108, 72, 150, 0, 0, 255, 1], [117, 0, 0, 285, 0, 0, 100, 1], [104, -108, -72, 75, 0, 0, 255, 1], [112, 0, 0, 150, 0, 0, 255, 1]], [[6, 0, 0, 156, 0, 0, 200, 1], [107, -60, 48, 105, 0, 0, 255, 1], [111, 0, 0, 150, 0, 0, 255, 1], [117, 0, 0, 270, 0, 0, 150, 1], [106, 108, 72, 90, 0, 0, 255, 1]], [[7, 0, 0, 159, 0, 0, 200, 1], [112, 0, 0, 150, 0, 0, 255, 1], [106, 84, -72, 150, 0, 0, 255, 1], [107, -60, 48, 60, 0, 0, 255, 1], [117, 0, 0, 255, 0, 0, 200, 1]], [[8, 0, 0, 162, 0, 0, 200, 1], [113, 0, 0, 225, 0, 0, 200, 1], [106, 84, -72, 90, 0, 0, 255, 1], [106, -12, 132, 150, 0, 0, 255, 1], [117, 0, 0, 240, 0, 0, 255, 1]], [[9, 0, 0, 165, 0, 0, 200, 1], [113, 0, 0, 300, 0, 0, 150, 1], [106, -12, 132, 90, 0, 0, 255, 1], [106, 156, 12, 90, 0, 0, 255, 1], [106, -132, -96, 90, 0, 0, 255, 1], [116, 0, 0, 255, 0, 0, 255, 1]], [[10, 0, 0, 171, 0, 0, 200, 1], [113, 0, 0, 375, 0, 0, 100, 1], [115, 0, 0, 255, 0, 0, 200, 1], [106, -36, -108, 90, 0, 0, 255, 1], [106, 156, 12, 45, 0, 0, 255, 1], [106, -132, -96, 45, 0, 0, 255, 1]], [[11, 0, 0, 174, 0, 0, 200, 1], [113, 0, 0, 390, 0, 0, 60, 1], [106, 48, -168, 90, 0, 0, 255, 1], [106, -36, -108, 45, 0, 0, 255, 1], [114, 0, 0, 255, 0, 0, 150, 1]], [[12, 0, 0, 177, 0, 0, 200, 1], [113, 0, 0, 405, 0, 0, 30, 1], [106, 72, 144, 90, 0, 0, 255, 1], [106, 48, -168, 45, 0, 0, 255, 1]], [[13, 0, 0, 180, 0, 0, 200, 1], [-1, -300, 216, 165, 0, 0, 255, 1], [106, 72, 144, 60, 0, 0, 255, 1]], [[14, 0, 0, 183, 0, 0, 200, 1]], [[15, 0, 0, 186, 0, 0, 200, 1]], [[16, 0, 0, 189, 0, 0, 200, 1]], [[17, 0, 0, 192, 0, 0, 200, 1]], [[18, 0, 0, 195, 0, 0, 200, 1]], [[19, 0, 0, 198, 0, 0, 200, 1]], [[20, 0, 0, 201, 0, 0, 200, 1]], [[21, 0, 0, 204, 0, 0, 200, 1]], [], [], []], "name": "Heal One 2", "position": 1, "timings": [{"flashColor": [187, 238, 255, 136], "flashDuration": 2, "flashScope": 1, "frame": 0, "se": {"name": "Starlight", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [187, 238, 255, 204], "flashDuration": 8, "flashScope": 2, "frame": 0, "se": {"name": "Saint2", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [187, 238, 255, 153], "flashDuration": 2, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [187, 238, 255, 136], "flashDuration": 2, "flashScope": 1, "frame": 4, "se": null}, {"flashColor": [187, 238, 255, 136], "flashDuration": 15, "flashScope": 1, "frame": 9, "se": null}]}, {"id": 43, "animation1Hue": 0, "animation1Name": "Recovery2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 400, 0, 0, 255, 1]], [[1, 0, 0, 400, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 150, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[2, 0, 0, 400, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 150, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, 0, 400, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 150, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[6, 0, -82, 500, 0, 0, 60, 1], [-1, -103, 312, 420, 0, 0, 80, 1], [4, 0, 0, 400, 0, 0, 255, 1], [-1, 408, 248, 100, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 150, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, -77, 510, 0, 0, 120, 1], [-1, -77.5, 312, 420, 0, 0, 128, 1], [-1, -200, 0, 300, 0, 0, 255, 1], [-1, 200, 0, 300, 0, 0, 255, 1], [5, 0, 0, 400, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 150, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[8, 0, -80, 520, 0, 0, 180, 1], [-1, 0, -10, 420, 0, 0, 128, 1], [-1, -200, 0, 300, 0, 0, 255, 1], [-1, 200, 0, 300, 0, 0, 255, 1], [5, 2, 0, 350, -80, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 150, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[6, 2, -78, 530, 0, 0, 180, 1], [-1, 200, 0, 300, 0, 0, 255, 1], [-1, -87.5, 312, 420, 0, 0, 128, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [18, 0, 144, 80, 0, 0, 100, 1], [-1, 0, 150, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, -78, 540, 0, 0, 180, 1], [9, -200, 0, 300, 0, 0, 255, 1], [-1, 0, -10, 420, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [18, 0, 101, 90, 0, 0, 200, 1], [-1, 0, 150, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[8, 0, -80, 550, 0, 0, 180, 1], [10, -200, 0, 300, 0, 0, 255, 1], [-1, -85, 255, 420, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 0, 59, 100, 0, 0, 255, 1], [-1, 0, 150, 0, 0, 0, 0, 0], [18, -300, 220, 60, 0, 0, 100, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[6, 0, -80, 560, 0, 0, 180, 1], [11, -200, 0, 300, 0, 0, 255, 1], [9, 200, 0, 300, 0, 0, 255, 1], [-1, -72.5, 230, 420, 0, 0, 128, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 0, 16, 110, 0, 0, 255, 1], [-1, 0, 150, 0, 0, 0, 0, 0], [18, -300, 181, 70, 0, 0, 200, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, -80, 570, 0, 0, 120, 1], [12, -200, 0, 300, 0, 0, 255, 1], [10, 200, 0, 300, 0, 0, 255, 1], [-1, -130, 285, 420, 0, 0, 128, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 0, -27, 100, 0, 0, 255, 1], [-1, 0, 150, 0, 0, 0, 0, 0], [18, -300, 141, 80, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[8, 0, -80, 580, 0, 0, 60, 1], [13, -200, 0, 300, 0, 0, 255, 1], [11, 200, 0, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 0, -69, 90, 0, 0, 200, 1], [18, 300, 228, 70, 0, 0, 100, 1], [18, -300, 102, 90, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[14, -200, 0, 300, 0, 0, 255, 1], [12, 200, 0, 300, 0, 0, 255, 1], [17, -200, 192, 300, 0, 0, 100, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 0, -112, 80, 0, 0, 100, 1], [18, 300, 193, 80, 0, 0, 200, 1], [18, -300, 63, 80, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[15, -200, 0, 300, 0, 0, 255, 1], [13, 200, 0, 300, 0, 0, 255, 1], [17, -200, 176, 300, 0, 0, 170, 1], [-1, 408, 223, 100, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 300, 157, 90, 0, 0, 255, 1], [18, -300, 23, 70, 0, 0, 200, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[16, -200, 0, 300, 0, 0, 255, 1], [14, 200, 0, 300, 0, 0, 255, 1], [17, -200, 160, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 300, 122, 100, 0, 0, 255, 1], [18, -300, -16, 60, 0, 0, 100, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[17, -200, 0, 300, 0, 0, 255, 1], [15, 200, 0, 300, 0, 0, 255, 1], [17, -200, 144, 300, 0, 0, 170, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 300, 87, 90, 0, 0, 255, 1], [-1, -297.5, -137.5, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[17, -200, 128, 300, 0, 0, 100, 1], [16, 200, 0, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 300, 51, 80, 0, 0, 200, 1], [-1, -300, -192, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[17, 200, 0, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [18, 300, 16, 70, 0, 0, 100, 1]]], "name": "Heal All 1", "position": 3, "timings": [{"flashColor": [187, 255, 255, 204], "flashDuration": 8, "flashScope": 1, "frame": 0, "se": {"name": "Saint4", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Ice4", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 3, "se": {"name": "Heal1", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 4, "se": {"name": "Ice5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [187, 255, 255, 136], "flashDuration": 10, "flashScope": 2, "frame": 5, "se": null}, {"flashColor": [187, 255, 255, 255], "flashDuration": 10, "flashScope": 1, "frame": 5, "se": null}]}, {"id": 44, "animation1Hue": 0, "animation1Name": "Recovery4", "animation2Hue": 80, "animation2Name": "Recovery5", "frames": [[[11, 0, -30, 150, 0, 0, 255, 1]], [[11, 0, -30, 405, 0, 0, 255, 1]], [[11, 0, -30, 486, 0, 0, 255, 1], [0, 0, -30, 135, 0, 0, 255, 1]], [[12, 0, -30, 405, 0, 0, 255, 1], [1, 0, -30, 162, 0, 0, 255, 1], [8, 0, -30, 567, 0, 0, 255, 1]], [[12, 0, -30, 432, 0, 0, 255, 1], [2, 0, -30, 216, 0, 0, 255, 1], [8, 0, -30, 531, 0, 0, 255, 1]], [[13, 0, -30, 432, 0, 0, 255, 1], [3, 0, -30, 216, 0, 0, 255, 1], [9, 0, -30, 531, 0, 0, 255, 1]], [[13, 0, -30, 439, 0, 0, 255, 1], [0, 0, -30, 216, 0, 0, 255, 1], [9, 0, -30, 513, 0, 0, 255, 1]], [[13, 0, -30, 432, 0, 0, 255, 1], [1, 0, -30, 216, 0, 0, 255, 1], [10, 0, -30, 513, 0, 0, 255, 1]], [[13, 0, -30, 439, 0, 0, 255, 1], [2, 0, -30, 216, 0, 0, 255, 1], [10, 0, -30, 486, 0, 0, 255, 1]], [[13, 0, -30, 270, 0, 0, 255, 1], [14, 0, -30, 405, 0, 0, 255, 1]], [[12, 0, -30, 270, 0, 0, 255, 1], [15, 0, -30, 486, 0, 0, 255, 1]], [[11, 0, -30, 270, 0, 0, 255, 1], [16, 0, -30, 540, 0, 0, 255, 1], [4, 144, -126, 135, 0, 0, 255, 1], [4, -144, 78, 135, 0, 0, 255, 1], [114, 0, -30, 405, 0, 0, 255, 1]], [[11, 0, -30, 135, 0, 0, 255, 1], [17, 0, -30, 540, 0, 0, 255, 1], [4, -144, -114, 135, 0, 0, 255, 1], [4, 144, 54, 135, 0, 0, 255, 1], [5, 144, -126, 162, 0, 0, 255, 1], [5, -144, 78, 162, 0, 0, 255, 1], [115, 0, -30, 405, 0, 0, 255, 1]], [[17, 0, -30, 553, 0, 0, 255, 1], [4, 12, -162, 135, 0, 0, 255, 1], [4, 12, 90, 135, 0, 0, 255, 1], [5, 144, 54, 216, 0, 0, 255, 1], [5, -144, -114, 216, 0, 0, 255, 1], [6, 144, -126, 162, 0, 0, 255, 1], [6, -144, 78, 162, 0, 0, 255, 1], [116, 0, -30, 405, 0, 0, 255, 1]], [[17, 0, -30, 567, 0, 0, 255, 1], [5, 12, -162, 270, 0, 0, 255, 1], [5, 12, 90, 270, 0, 0, 255, 1], [6, -144, -114, 216, 0, 0, 255, 1], [6, 144, 54, 216, 0, 0, 255, 1], [7, 144, -126, 162, 0, 0, 255, 1], [7, -144, 78, 162, 0, 0, 255, 1], [117, 0, -30, 405, 0, 0, 255, 1]], [[17, 0, -30, 574, 0, 0, 100, 1], [6, 12, -162, 270, 0, 0, 255, 1], [6, 12, 90, 270, 0, 0, 255, 1], [7, 144, 54, 216, 0, 0, 255, 1], [7, -144, -114, 216, 0, 0, 255, 1], [118, 0, -30, 405, 0, 0, 255, 1]], [[7, 12, -162, 270, 0, 0, 255, 1], [7, 12, 90, 270, 0, 0, 255, 1], [119, 0, -30, 405, 0, 0, 255, 1]]], "name": "Heal All 2", "position": 3, "timings": [{"flashColor": [170, 204, 255, 221], "flashDuration": 8, "flashScope": 1, "frame": 0, "se": {"name": "Ice1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 8, "se": {"name": "Evasion2", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 255, 255, 136], "flashDuration": 10, "flashScope": 2, "frame": 9, "se": {"name": "Flash2", "pan": 0, "pitch": 90, "volume": 50}}, {"conditions": 0, "flashColor": [255, 255, 255, 204], "flashDuration": 10, "flashScope": 1, "frame": 9, "se": {"name": "Ice4", "pan": 0, "pitch": 100, "volume": 80}}]}, {"id": 45, "animation1Hue": 0, "animation1Name": "Cure1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -150, 280, 0, 0, 128, 1]], [[1, 0, -150, 280, 0, 0, 200, 1], [17, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[2, 0, -150, 280, 0, 0, 255, 1], [17, 2, -18, 200, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[3, 0, -150, 280, 0, 0, 255, 1], [17, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[4, 0, -150, 280, 0, 0, 255, 1], [18, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[5, 0, -150, 280, 0, 0, 255, 1], [19, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[6, 0, -150, 280, 0, 0, 255, 1], [20, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[7, 0, -150, 280, 0, 0, 255, 1], [22, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[8, 0, -150, 280, 0, 0, 255, 1], [23, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[9, 0, -150, 280, 0, 0, 255, 1], [24, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[10, 0, -150, 280, 0, 0, 255, 1], [25, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[11, 0, -150, 280, 0, 0, 255, 1], [26, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[12, 0, -150, 280, 0, 0, 255, 1], [27, 0, -20, 300, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[13, 0, -150, 280, 0, 0, 255, 1], [27, 0, -20, 330, 0, 0, 128, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[14, 0, -150, 280, 0, 0, 255, 1], [27, 0, -20, 360, 0, 0, 60, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[15, 0, -150, 280, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1], [-1, 0, -20, 100, 0, 0, 255, 1]], [[16, 0, -150, 280, 0, 0, 255, 1]]], "name": "Cure One 1", "position": 2, "timings": [{"flashColor": [170, 255, 255, 85], "flashDuration": 10, "flashScope": 1, "frame": 1, "se": {"name": "Ice1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [170, 255, 255, 102], "flashDuration": 10, "flashScope": 1, "frame": 2, "se": {"name": "Recovery", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [170, 255, 255, 204], "flashDuration": 4, "flashScope": 1, "frame": 3, "se": null}, {"flashColor": [170, 255, 255, 68], "flashDuration": 5, "flashScope": 2, "frame": 4, "se": null}, {"flashColor": [170, 255, 255, 204], "flashDuration": 4, "flashScope": 1, "frame": 6, "se": {"name": "Ice5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [170, 255, 255, 119], "flashDuration": 10, "flashScope": 2, "frame": 6, "se": null}, {"flashColor": [170, 255, 255, 204], "flashDuration": 4, "flashScope": 1, "frame": 10, "se": null}]}, {"id": 46, "animation1Hue": 250, "animation1Name": "Cure3", "animation2Hue": 0, "animation2Name": "Recovery5", "frames": [[[4, 0, -144, 225, 0, 0, 255, 1]], [[5, 0, -144, 225, 0, 0, 255, 1], [1, 0, -144, 225, 0, 0, 100, 1]], [[4, 0, -144, 225, 0, 0, 255, 1], [2, 0, -144, 225, 0, 0, 100, 1], [100, 0, 0, 195, 0, 0, 255, 1]], [[5, 0, -144, 225, 0, 0, 255, 1], [1, 0, -144, 225, 0, 0, 100, 1], [101, 0, 0, 225, 0, 0, 255, 1]], [[4, 0, -144, 225, 0, 0, 255, 1], [5, 0, -156, 240, 0, 0, 100, 1], [2, 0, -144, 225, 0, 0, 150, 1], [102, 0, 0, 225, 0, 0, 255, 1]], [[6, 0, -156, 240, 0, 0, 255, 1], [3, 0, -144, 225, 0, 0, 150, 1], [103, 0, 0, 225, 0, 0, 255, 1]], [[5, 0, -156, 240, 0, 0, 255, 1], [2, 0, -144, 225, 0, 0, 150, 1], [104, 0, 0, 225, 0, 0, 255, 1]], [[6, 0, -156, 240, 0, 0, 255, 1], [3, 0, -144, 225, 0, 0, 150, 1], [105, 0, 0, 225, 0, 0, 255, 1], [120, 0, -168, 150, 180, 0, 255, 1]], [[5, 0, -156, 240, 0, 0, 255, 1], [2, 0, -144, 225, 0, 0, 150, 1], [106, 0, 0, 225, 0, 0, 255, 1], [121, 0, -180, 150, 180, 0, 255, 1]], [[6, 0, -156, 240, 0, 0, 255, 1], [3, 0, -144, 225, 0, 0, 150, 1], [107, 0, 0, 225, 0, 0, 255, 1], [122, 0, -180, 150, 180, 0, 255, 1]], [[5, 0, -156, 240, 0, 0, 255, 1], [2, 0, -144, 225, 0, 0, 150, 1], [108, 0, 0, 225, 0, 0, 255, 1], [123, 0, -180, 150, 180, 0, 255, 1]], [[4, 0, -144, 225, 0, 0, 255, 1], [4, 0, -156, 240, 0, 0, 100, 1], [1, 0, -144, 225, 0, 0, 150, 1], [109, 0, 0, 225, 0, 0, 255, 1], [124, 0, -180, 150, 180, 0, 255, 1]], [[4, 0, -132, 210, 0, 0, 150, 1], [1, 0, -144, 225, 0, 0, 100, 1], [125, 0, -180, 150, 180, 0, 255, 1]], [[126, 0, -180, 150, 180, 0, 255, 1]], [[127, 0, -180, 150, 180, 0, 255, 1]], [[128, 0, -180, 150, 180, 0, 255, 1]], [[129, 0, -180, 150, 180, 0, 255, 1]]], "name": "Cure One 2", "position": 2, "timings": [{"flashColor": [238, 255, 187, 102], "flashDuration": 10, "flashScope": 2, "frame": 0, "se": {"name": "Ice1", "pan": 0, "pitch": 90, "volume": 90}}, {"flashColor": [204, 255, 85, 170], "flashDuration": 8, "flashScope": 1, "frame": 2, "se": {"name": "Ice5", "pan": 0, "pitch": 100, "volume": 90}}]}, {"id": 47, "animation1Hue": 0, "animation1Name": "Cure2", "animation2Hue": 0, "animation2Name": "", "frames": [[], [[0, 0, -200, 200, 0, 0, 255, 1], [-1, 300, -190, 100, 0, 0, 255, 1], [-1, 300, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[1, 0, -200, 200, 0, 0, 255, 1], [-1, 300, -190, 100, 0, 0, 255, 1], [-1, 300, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[2, 0, -200, 200, 0, 0, 255, 1], [0, 300, -190, 200, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, 0, -200, 30, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[3, 0, -200, 200, 0, 0, 255, 1], [1, 300, -190, 200, 0, 0, 255, 1], [0, -300, -190, 200, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, 0, -200, 54, 0, 0, 235, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[4, 0, -200, 200, 0, 0, 255, 1], [2, 300, -190, 200, 0, 0, 255, 1], [1, -300, -190, 200, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, 300, -190, 30, 0, 0, 255, 1], [25, 0, -200, 78, 0, 0, 214, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[5, 0, -200, 200, 0, 0, 255, 1], [3, 300, -190, 200, 0, 0, 255, 1], [2, -300, -190, 200, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 30, 0, 0, 255, 1], [25, 300, -190, 47, 0, 0, 235, 1], [25, 0, -200, 102, 0, 0, 194, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[6, 0, -200, 200, 0, 0, 255, 1], [4, 300, -190, 200, 0, 0, 255, 1], [3, -300, -190, 200, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 47, 0, 0, 235, 1], [25, 300, -190, 64, 0, 0, 214, 1], [25, 0, -200, 126, 0, 0, 173, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[7, 0, -200, 200, 0, 0, 255, 1], [5, 300, -190, 200, 0, 0, 255, 1], [4, -300, -190, 200, 0, 0, 255, 1], [13, -150, 0, 300, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 64, 0, 0, 214, 1], [25, 300, -190, 81, 0, 0, 194, 1], [25, 0, -200, 150, 0, 0, 153, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[8, 0, -200, 200, 0, 0, 255, 1], [6, 300, -190, 200, 0, 0, 255, 1], [5, -300, -190, 200, 0, 0, 255, 1], [14, -150, 0, 300, 0, 0, 255, 1], [13, 200, 0, 300, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 81, 0, 0, 194, 1], [25, 300, -190, 98, 0, 0, 173, 1], [25, 0, -200, 174, 0, 0, 132, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[9, 0, -200, 200, 0, 0, 255, 1], [7, 300, -190, 200, 0, 0, 255, 1], [6, -300, -190, 200, 0, 0, 255, 1], [15, -150, 0, 300, 0, 0, 255, 1], [14, 200, 0, 300, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 98, 0, 0, 173, 1], [25, 300, -190, 115, 0, 0, 153, 1], [25, 0, -200, 198, 0, 0, 112, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[10, 0, -200, 200, 0, 0, 255, 1], [8, 300, -190, 200, 0, 0, 255, 1], [7, -300, -190, 200, 0, 0, 255, 1], [16, -150, 0, 300, 0, 0, 255, 1], [15, 200, 0, 300, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 115, 0, 0, 153, 1], [25, 300, -190, 132, 0, 0, 132, 1], [25, 0, -200, 222, 0, 0, 91, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[11, 0, -200, 200, 0, 0, 255, 1], [9, 300, -190, 200, 0, 0, 255, 1], [8, -300, -190, 200, 0, 0, 255, 1], [17, -150, 0, 300, 0, 0, 255, 1], [16, 200, 0, 300, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 132, 0, 0, 132, 1], [25, 300, -190, 149, 0, 0, 112, 1], [25, 0, -200, 246, 0, 0, 71, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[12, 0, -200, 200, 0, 0, 255, 1], [10, 300, -190, 200, 0, 0, 255, 1], [9, -300, -190, 200, 0, 0, 255, 1], [18, -150, 0, 300, 0, 0, 255, 1], [17, 200, 0, 300, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 149, 0, 0, 112, 1], [25, 300, -190, 166, 0, 0, 91, 1], [25, 0, -200, 270, 0, 0, 50, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[11, 300, -190, 200, 0, 0, 255, 1], [10, -300, -190, 200, 0, 0, 255, 1], [19, -150, 0, 300, 0, 0, 255, 1], [18, 200, 0, 300, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [25, -300, -190, 166, 0, 0, 91, 1], [25, 300, -190, 183, 0, 0, 71, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1], [-1, 150, -190, 100, 0, 0, 255, 1]], [[12, 300, -190, 200, 0, 0, 255, 1], [11, -300, -190, 200, 0, 0, 255, 1], [20, -150, 0, 300, 0, 0, 255, 1], [19, 200, 0, 300, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [25, -300, -190, 183, 0, 0, 71, 1], [25, 300, -190, 200, 0, 0, 50, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1], [-1, -150, 0, 100, 0, 0, 255, 1]], [[12, -300, -190, 200, 0, 0, 255, 1], [21, -150, 0, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [20, 200, 0, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [25, -300, -190, 200, 0, 0, 50, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [22, -150, 0, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [21, 200, 0, 300, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [23, -150, 0, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [22, 200, 0, 300, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [24, -150, 0, 300, 0, 0, 200, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [23, 200, 0, 300, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, -152.5, 0, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [24, 200, 0, 300, 0, 0, 200, 1]]], "name": "Cure All 1", "position": 3, "timings": [{"flashColor": [170, 255, 255, 170], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": {"name": "Starlight", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [170, 255, 255, 221], "flashDuration": 20, "flashScope": 1, "frame": 1, "se": {"name": "Ice4", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [170, 255, 255, 153], "flashDuration": 5, "flashScope": 1, "frame": 6, "se": {"name": "Ice1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [170, 255, 255, 153], "flashDuration": 5, "flashScope": 1, "frame": 9, "se": {"name": "Heal3", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [170, 255, 255, 153], "flashDuration": 5, "flashScope": 1, "frame": 12, "se": null}, {"flashColor": [170, 255, 255, 153], "flashDuration": 5, "flashScope": 1, "frame": 15, "se": null}]}, {"id": 48, "animation1Hue": 0, "animation1Name": "Cure4", "animation2Hue": 0, "animation2Name": "Recovery5", "frames": [[[12, 0, -50, 75, 0, 0, 255, 1]], [[0, 0, -50, 48, 0, 0, 255, 1], [12, 0, -50, 192, 0, 0, 200, 1]], [[12, 0, -50, 240, 0, 0, 200, 1], [1, 0, -50, 288, 0, 0, 255, 1]], [[12, 0, -50, 288, 0, 0, 200, 1], [2, 0, -50, 288, 0, 0, 255, 1]], [[12, 0, -50, 299, 0, 0, 170, 1], [3, 0, -50, 288, 0, 0, 255, 1], [110, 0, -50, 336, 0, 0, 255, 1]], [[12, 0, -50, 312, 0, 0, 150, 1], [4, 0, -50, 288, 0, 0, 255, 1], [111, 0, -50, 432, 0, 0, 255, 1]], [[12, 0, -50, 299, 0, 0, 170, 1], [5, 0, -50, 288, 0, 0, 255, 1], [112, 0, -50, 480, 0, 0, 255, 1]], [[12, 0, -50, 288, 0, 0, 200, 1], [6, 0, -50, 288, 0, 0, 255, 1], [113, 0, -50, 480, 0, 0, 255, 1]], [[12, 0, -50, 299, 0, 0, 170, 1], [7, 0, -50, 288, 0, 0, 255, 1], [114, 0, -50, 480, 0, 0, 255, 1]], [[12, 0, -50, 312, 0, 0, 150, 1], [8, 0, -50, 288, 0, 0, 255, 1], [115, 0, -50, 480, 0, 0, 255, 1]], [[12, 0, -50, 299, 0, 0, 170, 1], [9, 0, -50, 288, 0, 0, 255, 1], [116, 0, -50, 480, 0, 0, 255, 1]], [[12, 0, -50, 264, 0, 0, 200, 1], [10, 0, -50, 288, 0, 0, 255, 1], [117, 0, -50, 480, 0, 0, 255, 1]], [[12, 0, -50, 192, 0, 0, 180, 1], [11, 0, -50, 288, 0, 0, 255, 1], [118, 0, -50, 480, 0, 0, 255, 1]], [[12, 0, -50, 144, 0, 0, 180, 1], [119, 0, -50, 480, 0, 0, 255, 1]], [[12, 0, -50, 120, 0, 0, 150, 1]], [[12, 0, -50, 120, 0, 0, 100, 1]], [[12, 0, -50, 120, 0, 0, 50, 1]]], "name": "Cure All 2", "position": 3, "timings": [{"flashColor": [204, 238, 255, 102], "flashDuration": 10, "flashScope": 2, "frame": 0, "se": {"name": "Ice5", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [85, 187, 221, 170], "flashDuration": 10, "flashScope": 1, "frame": 1, "se": {"name": "Saint4", "pan": 0, "pitch": 100, "volume": 80}}]}, {"id": 49, "animation1Hue": 0, "animation1Name": "Revival1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -185, 280, 0, 0, 255, 1]], [[1, 0, -185, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[2, 0, -185, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[3, 0, -185, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[4, 0, -185, 280, 0, 0, 255, 1], [7, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[5, 0, -185, 280, 0, 0, 255, 1], [8, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[6, 0, -185, 280, 0, 0, 255, 1], [9, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1]], [[4, 0, -185, 280, 0, 0, 255, 1], [10, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[5, 0, -185, 280, 0, 0, 255, 1], [11, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[6, 0, -185, 280, 0, 0, 255, 1], [12, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1]], [[4, 0, -185, 280, 0, 0, 255, 1], [13, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[5, 0, -185, 280, 0, 0, 255, 1], [14, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[6, 0, -185, 280, 0, 0, 255, 1], [15, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1]], [[4, 0, -185, 280, 0, 0, 255, 1], [16, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[5, 0, -185, 280, 0, 0, 255, 1], [17, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[6, 0, -185, 280, 0, 0, 255, 1], [18, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1]], [[4, 0, -185, 280, 0, 0, 255, 1], [19, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[5, 0, -185, 280, 0, 0, 255, 1], [20, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[6, 0, -185, 280, 0, 0, 255, 1], [21, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1]], [[4, 0, -185, 280, 0, 0, 255, 1], [22, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[5, 0, -185, 280, 0, 0, 255, 1], [23, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[6, 0, -185, 280, 0, 0, 255, 1], [24, 0, -187, 280, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1]], [[4, 0, -185, 280, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[3, 0, -185, 280, 0, 0, 200, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1], [-1, 0, -185, 100, 0, 0, 255, 1]], [[2, 2, -185, 280, 0, 0, 127, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1], [-1, 0, -145, 100, 0, 0, 255, 1]]], "name": "Revive 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 221, 17], "flashDuration": 10, "flashScope": 1, "frame": 3, "se": {"name": "Starlight", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 221, 51], "flashDuration": 10, "flashScope": 1, "frame": 4, "se": {"name": "Skill1", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [255, 255, 221, 68], "flashDuration": 10, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [255, 255, 221, 102], "flashDuration": 10, "flashScope": 1, "frame": 6, "se": null}, {"flashColor": [255, 255, 221, 153], "flashDuration": 10, "flashScope": 1, "frame": 13, "se": null}, {"flashColor": [255, 255, 221, 17], "flashDuration": 10, "flashScope": 2, "frame": 3, "se": null}, {"flashColor": [255, 255, 221, 51], "flashDuration": 10, "flashScope": 2, "frame": 4, "se": null}, {"flashColor": [255, 255, 221, 68], "flashDuration": 10, "flashScope": 2, "frame": 5, "se": null}, {"flashColor": [255, 255, 221, 85], "flashDuration": 20, "flashScope": 2, "frame": 6, "se": null}]}, {"id": 50, "animation1Hue": 0, "animation1Name": "Revival2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 192, 225, 0, 0, 255, 1]], [[1, 0, 192, 225, 0, 0, 255, 1]], [[2, 0, 192, 225, 0, 0, 255, 1]], [[3, 0, 192, 225, 0, 0, 255, 1]], [[4, 0, 192, 225, 0, 0, 255, 1]], [[5, 0, 192, 225, 0, 0, 255, 1], [25, 0, 312, 150, 0, 0, 255, 1], [11, 0, 120, 150, 0, 0, 255, 1], [18, 0, 312, 450, 0, 0, 255, 1]], [[6, 0, 192, 225, 0, 0, 255, 1], [26, 0, 312, 150, 0, 0, 255, 1], [11, 0, 156, 195, 0, 0, 255, 1], [18, 0, 312, 375, 0, 0, 255, 1]], [[7, 0, 192, 225, 0, 0, 255, 1], [27, 0, 312, 150, 0, 0, 255, 1], [12, 0, 156, 195, 0, 0, 255, 1], [18, 0, 312, 345, 0, 0, 255, 1]], [[8, 0, 192, 225, 0, 0, 255, 1], [28, 0, 312, 150, 0, 0, 255, 1], [13, 0, 168, 210, 0, 0, 255, 1], [18, 0, 312, 315, 0, 0, 255, 1]], [[9, 0, 192, 225, 0, 0, 255, 1], [29, 0, 312, 150, 0, 0, 255, 1], [14, 0, 168, 210, 0, 0, 255, 1], [15, 12, 204, 225, 0, 0, 255, 1], [18, 0, 312, 285, 0, 0, 255, 1]], [[10, 0, 192, 225, 0, 0, 255, 1], [29, 0, 312, 165, 0, 0, 255, 1], [14, 0, 168, 210, 0, 0, 150, 1], [16, 12, 216, 225, 0, 0, 255, 1], [18, 0, 312, 255, 0, 0, 255, 1]], [[10, 0, 192, 225, 0, 0, 180, 1], [29, 0, 312, 150, 0, 0, 255, 1], [14, 0, 168, 210, 0, 0, 80, 1], [17, 12, 228, 225, 0, 0, 255, 1], [18, 0, 312, 240, 0, 0, 255, 1]], [[29, 0, 312, 165, 0, 0, 255, 1], [15, 12, 240, 225, 0, 0, 255, 1], [18, 0, 312, 225, 0, 0, 255, 1]], [[19, 0, 216, 225, 0, 0, 255, 1], [16, 12, 252, 225, 0, 0, 255, 1], [18, 0, 312, 210, 0, 0, 200, 1]], [[20, 0, 192, 270, 0, 0, 255, 1], [17, 12, 264, 225, 0, 0, 255, 1], [18, 0, 312, 195, 0, 0, 150, 1]], [[21, 0, 192, 270, 0, 0, 255, 1], [15, 12, 276, 225, 0, 0, 200, 1], [18, 0, 312, 180, 0, 0, 100, 1]], [[22, 0, 192, 270, 0, 0, 255, 1]], [[23, 0, 180, 285, 0, 0, 255, 1]], [[24, 0, 180, 300, 0, 0, 255, 1]], [[24, 0, 168, 315, 0, 0, 100, 1]]], "name": "Revive 2", "position": 0, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 9, "se": {"name": "Ice5", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [255, 255, 221, 238], "flashDuration": 8, "flashScope": 1, "frame": 13, "se": {"name": "Up3", "pan": 0, "pitch": 130, "volume": 80}}]}, {"id": 51, "animation1Hue": 149, "animation1Name": "StateDown1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -53, 260, 180, 0, 255, 1]], [[1, 0, -53, 260, 180, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[2, 0, -5, 260, 180, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[3, 0, 107, 260, 180, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [11, -32, -123, 100, 180, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[4, 0, -133, 260, 0, 0, 255, 1], [10, 0, -201, 200, 180, 0, 127, 1], [11, -120, -123, 100, 180, 0, 255, 1], [11, -32, -179, 100, 180, 0, 255, 1], [11, 50, -183, 100, 180, 0, 255, 1], [-1, 107.5, -205, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[5, 0, -133, 260, 0, 0, 255, 1], [10, 0, -217, 200, 180, 0, 255, 1], [11, -120, -183, 100, 180, 0, 255, 1], [11, -32, -247, 100, 180, 0, 255, 1], [11, 50, -247, 100, 180, 0, 255, 1], [-1, 112.5, -263, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[6, 0, -133, 260, 0, 0, 255, 1], [10, 0, -233, 200, 180, 0, 127, 1], [11, -120, -247, 100, 180, 0, 255, 1], [-1, 368, -123, 100, 0, 0, 255, 1], [11, 50, -297, 100, 180, 0, 255, 1], [11, 110, -123, 100, 180, 0, 255, 1], [11, -64, -123, 100, 180, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[7, 0, -133, 260, 0, 0, 255, 1], [-1, -408, -216, 100, 0, 0, 255, 1], [-1, -408, -123, 100, 0, 0, 255, 1], [-1, -408, -123, 100, 0, 0, 255, 1], [-1, -408, -123, 100, 0, 0, 255, 1], [11, 110, -135, 100, 180, 0, 255, 1], [11, -64, -183, 100, 180, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[8, 0, -133, 260, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [11, 110, -199, 100, 180, 0, 255, 1], [11, -64, -247, 100, 180, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[9, 0, -133, 260, 0, 0, 255, 1]], [[4, 0, -133, 260, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[4, 0, -133, 260, 0, 0, 200, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]], [[4, 0, -133, 260, 0, 0, 127, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1], [-1, 0, -135, 100, 0, 0, 255, 1]]], "name": "Powerup 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Up4", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [238, 187, 204, 187], "flashDuration": 10, "flashScope": 1, "frame": 5, "se": null}]}, {"id": 52, "animation1Hue": 0, "animation1Name": "StateUp1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 260, 0, 0, 127, 1]], [[1, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 240, 0, 0, 200, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[2, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[3, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[4, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[5, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[6, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[7, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[8, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[9, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[10, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[11, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[12, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[13, -3, -175, 240, 0, 0, 255, 1], [18, 0, -35, 220, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[14, -3, -175, 240, 0, 0, 255, 1], [18, 0, -29, 203, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[15, -3, -175, 240, 0, 0, 255, 1], [18, 0, -24, 185, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[16, -3, -175, 240, 0, 0, 255, 1], [18, 0, -18, 168, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]], [[17, -3, -175, 240, 0, 0, 255, 1], [18, 0, -13, 150, 0, 0, 100, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1], [-1, -3, -125, 100, 0, 0, 255, 1]]], "name": "Powerup 2", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Powerup", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 221, 136, 119], "flashDuration": 5, "flashScope": 1, "frame": 4, "se": {"name": "Magic4", "pan": 0, "pitch": 120, "volume": 80}}, {"flashColor": [255, 221, 136, 221], "flashDuration": 5, "flashScope": 1, "frame": 15, "se": null}, {"flashColor": [255, 221, 136, 102], "flashDuration": 3, "flashScope": 2, "frame": 15, "se": null}]}, {"id": 53, "animation1Hue": 0, "animation1Name": "StateUp2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -60, 180, 0, 0, 255, 1]], [[1, 0, -60, 180, 0, 0, 255, 1]], [[2, 0, -60, 180, 0, 0, 255, 1]], [[3, 0, -60, 180, 0, 0, 255, 1]], [[4, 0, -60, 180, 0, 0, 255, 1]], [[5, 0, -60, 180, 0, 0, 255, 1]], [[6, 0, -60, 180, 0, 0, 255, 1]], [[7, 0, -60, 180, 0, 0, 255, 1]], [[8, 0, -60, 180, 0, 0, 255, 1]], [[9, 0, -60, 180, 0, 0, 255, 1]], [[10, 0, -60, 180, 0, 0, 255, 1]], [[11, 0, -60, 180, 0, 0, 255, 1]], [[12, 0, -60, 180, 0, 0, 255, 1]], [[13, 0, -60, 180, 0, 0, 255, 1], [23, 0, -60, 180, 0, 0, 50, 0]], [[23, 0, -60, 180, 0, 0, 100, 0], [14, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 150, 0], [15, 0, -60, 180, 0, 0, 255, 1], [-1, 0, 0, 30, 0, 0, 0, 0]], [[23, 0, -60, 180, 0, 0, 200, 0], [16, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 255, 0], [17, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 255, 0], [18, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 255, 0], [19, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 255, 0], [20, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 255, 0], [21, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 255, 0], [22, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 255, 0], [22, 0, -60, 180, 0, 0, 255, 1]], [[23, 0, -60, 180, 0, 0, 200, 0], [22, 0, -60, 180, 0, 0, 200, 1]], [[23, 0, -60, 180, 0, 0, 150, 0], [22, 0, -60, 180, 0, 0, 150, 1]], [[23, 0, -60, 180, 0, 0, 100, 0], [22, 0, -60, 180, 0, 0, 100, 1]], [[23, 0, -60, 180, 0, 0, 50, 0], [22, 0, -60, 180, 0, 0, 50, 1]]], "name": "Powerup 3", "position": 1, "timings": [{"flashColor": [204, 238, 255, 204], "flashDuration": 20, "flashScope": 1, "frame": 0, "se": {"name": "Up1", "pan": 0, "pitch": 80, "volume": 80}}, {"flashColor": [238, 255, 255, 136], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": null}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 9, "se": {"name": "Ice4", "pan": 0, "pitch": 90, "volume": 80}}]}, {"id": 54, "animation1Hue": 0, "animation1Name": "StateDown1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -64, 260, 0, 0, 255, 1]], [[1, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[2, 0, -10, 260, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, -41.5, -312, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[3, 0, -130, 260, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [11, -32, -272, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[4, 0, -130, 260, 0, 0, 255, 1], [10, 0, -206, 200, 0, 0, 127, 1], [11, -120, -280, 100, 0, 0, 255, 1], [11, -32, -204, 100, 0, 0, 255, 1], [11, 50, -312, 100, 0, 0, 255, 1], [-1, 107.5, -230, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[5, 0, -130, 260, 0, 0, 255, 1], [10, 0, -190, 200, 0, 0, 255, 1], [11, -120, -208, 100, 0, 0, 255, 1], [11, -32, -144, 100, 0, 0, 255, 1], [11, 50, -272, 100, 0, 0, 255, 1], [-1, 112.5, -288, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[6, 0, -130, 260, 0, 0, 255, 1], [10, 0, -176, 200, 0, 0, 127, 1], [11, -120, -144, 100, 0, 0, 255, 1], [-1, 368, -120, 100, 0, 0, 255, 1], [11, 50, -208, 100, 0, 0, 255, 1], [11, 110, -224, 100, 0, 0, 255, 1], [11, -64, -272, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[7, 0, -130, 260, 0, 0, 255, 1], [-1, -408, -241, 100, 0, 0, 255, 1], [-1, -408, -95, 100, 0, 0, 255, 1], [-1, -408, -87, 100, 0, 0, 255, 1], [-1, -408, -95, 100, 0, 0, 255, 1], [11, 110, -160, 100, 0, 0, 255, 1], [11, -64, -208, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[8, 0, -130, 260, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [11, 110, -96, 100, 0, 0, 255, 1], [11, -64, -144, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[9, 0, -130, 260, 0, 0, 255, 1]], [[4, 0, -130, 260, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[4, 0, -130, 260, 0, 0, 200, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]], [[4, 0, -130, 260, 0, 0, 127, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1], [-1, 0, -160, 100, 0, 0, 255, 1]]], "name": "Powerdown 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Down2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Magic2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [153, 204, 255, 187], "flashDuration": 10, "flashScope": 1, "frame": 5, "se": null}]}, {"id": 55, "animation1Hue": 0, "animation1Name": "StateDown2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -168, 195, 0, 0, 100, 0]], [[2, 0, -168, 195, 0, 0, 150, 0]], [[3, 0, -168, 195, 0, 0, 255, 0]], [[2, 0, -168, 195, 0, 0, 255, 0]], [[1, 0, -168, 195, 0, 0, 255, 0]], [[4, 0, -168, 195, 0, 0, 255, 0]], [[5, 0, -168, 195, 0, 0, 255, 0]], [[6, 0, -168, 195, 0, 0, 255, 0]], [[7, 0, -168, 195, 0, 0, 255, 0]], [[8, 0, -168, 195, 0, 0, 255, 0]], [[9, 0, -168, 195, 0, 0, 255, 0]], [[10, 0, -168, 195, 0, 0, 255, 0]], [[11, 0, -168, 195, 0, 0, 255, 0]], [[12, 0, -168, 195, 0, 0, 255, 0]], [[13, 0, -168, 195, 0, 0, 255, 0]], [[14, 0, -168, 195, 0, 0, 255, 0]], [[15, 0, -168, 195, 0, 0, 255, 0]], [[16, 0, -168, 195, 0, 0, 255, 0]], [[17, 0, -168, 195, 0, 0, 255, 0]], [[18, 0, -168, 195, 0, 0, 255, 0]], [[19, 0, -168, 195, 0, 0, 255, 0]], [[20, 0, -168, 195, 0, 0, 255, 0]], [[21, 0, -168, 195, 0, 0, 255, 0]], [[22, 0, -168, 195, 0, 0, 255, 0]], [[23, 0, -168, 195, 0, 0, 255, 0]], [[24, 0, -168, 195, 0, 0, 255, 0]], [[20, 0, -168, 195, 0, 0, 200, 0]], [[21, 0, -168, 195, 0, 0, 100, 0]], [[22, 0, -168, 195, 0, 0, 50, 0]]], "name": "Powerdown 2", "position": 2, "timings": [{"flashColor": [0, 0, 0, 204], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": {"name": "Stare", "pan": 0, "pitch": 110, "volume": 70}}, {"flashColor": [0, 0, 0, 204], "flashDuration": 18, "flashScope": 1, "frame": 6, "se": {"name": "Earth2", "pan": 0, "pitch": 70, "volume": 80}}]}, {"id": 56, "animation1Hue": 0, "animation1Name": "StateDown3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -12, 150, 0, 0, 150, 1]], [[1, 0, -12, 150, 0, 0, 200, 1]], [[2, 0, -12, 150, 0, 0, 255, 1]], [[5, 0, -12, 150, 0, 0, 100, 0], [3, 0, -12, 150, 0, 0, 255, 1]], [[5, 0, -12, 150, 0, 0, 150, 0], [4, 0, -12, 150, 0, 0, 255, 1]], [[5, 0, -12, 150, 0, 0, 200, 0], [4, 0, -12, 150, 0, 0, 255, 1]], [[5, 0, -12, 150, 0, 0, 255, 0], [4, 0, -12, 150, 0, 0, 255, 1]], [[5, 0, -12, 150, 0, 0, 255, 0], [4, 0, -12, 150, 0, 0, 200, 1]], [[5, 0, -12, 150, 0, 0, 255, 0], [4, 0, -12, 150, 0, 0, 150, 1]], [[5, 0, -12, 150, 0, 0, 255, 0], [4, 0, -12, 150, 0, 0, 100, 1]], [[5, 0, -12, 150, 0, 0, 255, 0], [4, 0, -12, 150, 0, 0, 50, 1]], [[5, 0, -12, 150, 0, 0, 255, 0]], [[5, 0, -12, 150, 0, 0, 255, 0]], [[5, 0, -12, 150, 0, 0, 255, 0]], [[6, 0, -12, 247, 0, 0, 255, 0], [16, 0, -12, 247, 0, 0, 255, 1]], [[7, 0, -12, 247, 0, 0, 255, 0], [17, 0, -12, 247, 0, 0, 255, 1]], [[8, 0, -12, 247, 0, 0, 255, 0], [18, 0, -12, 247, 0, 0, 255, 1]], [[9, 0, -12, 247, 0, 0, 255, 0], [19, 0, -12, 247, 0, 0, 255, 1]], [[10, 0, -12, 247, 0, 0, 255, 0], [20, 0, -12, 247, 0, 0, 255, 1]], [[11, 0, -12, 247, 0, 0, 255, 0], [21, 0, -12, 247, 0, 0, 255, 1]], [[12, 0, -12, 247, 0, 0, 255, 0], [22, 0, -12, 247, 0, 0, 255, 1]], [[13, 0, -12, 247, 0, 0, 200, 0], [23, 0, -12, 247, 0, 0, 255, 1]], [[14, 0, -12, 247, 0, 0, 150, 0], [24, 0, -12, 247, 0, 0, 255, 1]], [[15, 0, -12, 247, 0, 0, 100, 0], [25, 0, -12, 247, 0, 0, 255, 1]]], "name": "Powerdown 3", "position": 1, "timings": [{"flashColor": [255, 51, 221, 204], "flashDuration": 15, "flashScope": 0, "frame": 0, "se": {"name": "Blind", "pan": 0, "pitch": 120, "volume": 80}}, {"flashColor": [255, 68, 221, 187], "flashDuration": 5, "flashScope": 2, "frame": 14, "se": {"name": "Crash", "pan": 0, "pitch": 120, "volume": 80}}, {"flashColor": [255, 51, 221, 204], "flashDuration": 15, "flashScope": 1, "frame": 14, "se": null}]}, {"id": 57, "animation1Hue": 0, "animation1Name": "Curse", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -228, 270, 0, 0, 255, 1]], [[1, 0, -228, 270, 0, 0, 255, 1]], [[2, 0, -228, 270, 0, 0, 255, 1]], [[3, 0, -228, 270, 0, 0, 255, 1]], [[4, 0, -228, 270, 0, 0, 255, 1]], [[5, 0, -228, 270, 0, 0, 255, 1], [10, 60, -228, 270, 340, 0, 255, 1], [10, -96, -228, 270, 10, 0, 255, 1]], [[6, 0, -228, 270, 0, 0, 255, 1], [11, 60, -228, 270, 340, 0, 255, 1], [11, -96, -228, 270, 10, 0, 255, 1]], [[7, 0, -228, 270, 0, 0, 255, 1], [12, 60, -228, 270, 340, 0, 255, 1], [12, -96, -228, 270, 10, 0, 255, 1], [10, 120, -228, 270, 350, 0, 255, 1], [10, -144, -228, 270, 15, 0, 255, 1]], [[8, 0, -228, 270, 0, 0, 255, 1], [13, 60, -228, 270, 340, 0, 255, 1], [13, -96, -228, 270, 10, 0, 255, 1], [11, 120, -228, 270, 350, 0, 255, 1], [11, -144, -228, 270, 15, 0, 255, 1]], [[9, 0, -228, 270, 0, 0, 255, 1], [15, 60, -228, 270, 340, 0, 255, 1], [15, -96, -228, 270, 10, 0, 255, 1], [12, 120, -228, 270, 350, 0, 255, 1], [12, -144, -228, 270, 15, 0, 255, 1], [10, -132, -192, 270, 175, 0, 255, 1], [10, 120, -192, 270, 190, 0, 255, 1]], [[8, 0, -228, 270, 0, 0, 255, 1], [14, 60, -228, 270, 340, 0, 255, 1], [14, -96, -228, 270, 10, 0, 255, 1], [13, 120, -228, 270, 350, 0, 255, 1], [13, -144, -228, 270, 15, 0, 255, 1], [11, -132, -192, 270, 175, 0, 255, 1], [11, 120, -192, 270, 190, 0, 255, 1]], [[9, 0, -228, 270, 0, 0, 255, 1], [15, 60, -228, 270, 340, 0, 255, 1], [15, -96, -228, 270, 10, 0, 255, 1], [15, 120, -228, 270, 350, 0, 255, 1], [15, -144, -228, 270, 15, 0, 255, 1], [12, -132, -192, 270, 175, 0, 255, 1], [12, 120, -192, 270, 190, 0, 255, 1]], [[8, 0, -228, 270, 0, 0, 255, 1], [14, 60, -228, 270, 340, 0, 255, 1], [14, -96, -228, 270, 10, 0, 255, 1], [14, 120, -228, 270, 350, 0, 255, 1], [14, -144, -228, 270, 15, 0, 255, 1], [13, -132, -192, 270, 175, 0, 255, 1], [13, 120, -192, 270, 190, 0, 255, 1]], [[9, 0, -228, 270, 0, 0, 255, 1], [15, 60, -228, 270, 340, 0, 255, 1], [15, -96, -228, 270, 10, 0, 255, 1], [15, 120, -228, 270, 350, 0, 255, 1], [15, -144, -228, 270, 15, 0, 255, 1], [15, -132, -192, 270, 175, 0, 255, 1], [15, 120, -192, 270, 190, 0, 255, 1]], [[8, 0, -228, 270, 0, 0, 255, 1], [14, 60, -228, 270, 340, 0, 255, 1], [14, -96, -228, 270, 10, 0, 255, 1], [14, 120, -228, 270, 350, 0, 255, 1], [14, -144, -228, 270, 15, 0, 255, 1], [14, -132, -192, 270, 175, 0, 255, 1], [14, 120, -192, 270, 190, 0, 255, 1]], [[9, 0, -228, 270, 0, 0, 255, 1], [15, 60, -228, 270, 340, 0, 255, 1], [15, -96, -228, 270, 10, 0, 255, 1], [15, 120, -228, 270, 350, 0, 255, 1], [15, -144, -228, 270, 15, 0, 255, 1], [15, -132, -192, 270, 175, 0, 255, 1], [15, 120, -192, 270, 190, 0, 255, 1]], [[8, 0, -228, 270, 0, 0, 255, 1], [14, 60, -228, 270, 340, 0, 255, 1], [14, -96, -228, 270, 10, 0, 255, 1], [14, 120, -228, 270, 350, 0, 255, 1], [14, -144, -228, 270, 15, 0, 255, 1], [14, -132, -192, 270, 175, 0, 255, 1], [14, 120, -192, 270, 190, 0, 255, 1]], [[8, 0, -228, 270, 0, 0, 150, 1], [14, 60, -228, 270, 340, 0, 150, 1], [14, -96, -228, 270, 10, 0, 150, 1], [14, 120, -228, 270, 350, 0, 150, 1], [14, -144, -228, 270, 15, 0, 150, 1], [14, -132, -192, 270, 175, 0, 150, 1], [14, 120, -192, 270, 190, 0, 150, 1]], [[8, 0, -228, 270, 0, 0, 100, 1], [14, 60, -228, 270, 340, 0, 100, 1], [14, -96, -228, 270, 10, 0, 100, 1], [14, 120, -228, 270, 350, 0, 100, 1], [14, -144, -228, 270, 15, 0, 100, 1], [14, -132, -192, 270, 175, 0, 100, 1], [14, 120, -192, 270, 190, 0, 100, 1]]], "name": "Bind", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind5", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Darkness4", "pan": 0, "pitch": 60, "volume": 100}}, {"flashColor": [255, 204, 136, 187], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [255, 255, 170, 170], "flashDuration": 5, "flashScope": 2, "frame": 5, "se": {"name": "Darkness1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 238, 136, 136], "flashDuration": 2, "flashScope": 1, "frame": 8, "se": null}, {"flashColor": [255, 238, 136, 136], "flashDuration": 2, "flashScope": 1, "frame": 10, "se": null}, {"flashColor": [255, 238, 136, 136], "flashDuration": 2, "flashScope": 1, "frame": 12, "se": null}, {"flashColor": [255, 238, 136, 136], "flashDuration": 2, "flashScope": 1, "frame": 14, "se": null}]}, {"id": 58, "animation1Hue": 0, "animation1Name": "Absorb", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 300, 0, 0, 150, 1]], [[0, 0, 0, 270, 0, 0, 200, 1], [20, 0, 0, 300, 60, 0, 100, 1]], [[1, 0, 0, 270, 0, 0, 180, 1], [20, 0, 0, 300, 40, 0, 180, 1]], [[20, 0, 0, 300, 20, 0, 180, 1], [2, 0, 0, 240, 0, 0, 255, 1]], [[4, 0, 0, 225, 0, 0, 255, 1], [20, 0, 0, 270, 0, 0, 180, 1]], [[6, 0, 0, 225, 0, 0, 255, 1], [21, 0, 0, 240, 340, 0, 180, 1]], [[7, 0, 0, 225, 0, 0, 255, 1], [22, 0, 0, 195, 320, 0, 180, 1], [8, 0, 0, 150, 0, 0, 255, 1]], [[9, 0, 0, 195, 0, 0, 255, 1], [23, 0, 0, 150, 300, 0, 180, 1]], [[10, 0, 0, 225, 0, 0, 255, 1]], [[11, 0, 0, 255, 0, 0, 255, 1]], [[12, 0, 0, 255, 0, 0, 255, 1]], [[13, 0, 0, 255, 0, 0, 255, 1]], [[14, 0, 0, 255, 0, 0, 255, 1]], [[15, 0, 0, 255, 0, 0, 255, 1]], [[16, 0, 0, 255, 0, 0, 255, 1]], [[17, 0, 0, 255, 0, 0, 255, 1]], [[18, 0, 0, 255, 0, 0, 255, 1]], [[19, 0, 0, 255, 0, 0, 255, 1]]], "name": "Absorb", "position": 1, "timings": [{"flashColor": [255, 102, 68, 153], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": {"name": "Darkness1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "<PERSON>e", "pan": 0, "pitch": 150, "volume": 70}}, {"flashColor": [255, 0, 0, 204], "flashDuration": 6, "flashScope": 1, "frame": 6, "se": null}]}, {"id": 59, "animation1Hue": 0, "animation1Name": "StatePoison", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -126.5, 200, 0, 0, 255, 1]], [[1, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[2, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[3, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[4, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[5, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[6, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[7, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[8, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[9, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[10, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[11, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[12, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[13, 0, -126, 200, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1], [-1, 0, -130, 100, 0, 0, 255, 1]], [[14, 0, -126, 200, 0, 0, 255, 1]]], "name": "Poison", "position": 2, "timings": [{"flashColor": [102, 255, 153, 102], "flashDuration": 3, "flashScope": 1, "frame": 1, "se": {"name": "Poison", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [102, 255, 153, 221], "flashDuration": 3, "flashScope": 1, "frame": 3, "se": null}, {"flashColor": [102, 255, 153, 136], "flashDuration": 8, "flashScope": 1, "frame": 5, "se": null}]}, {"id": 60, "animation1Hue": 0, "animation1Name": "StateDark", "animation2Hue": 0, "animation2Name": "", "frames": [[[19, 0, 0, 280, 0, 0, 255, 0]], [[18, 0, 0, 265, 0, 0, 255, 0], [-1, 0, 0, 250, -5, 0, 145, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[17, 0, 0, 260, 0, 0, 255, 0], [0, 0, 0, 250, 0, 0, 100, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[16, 0, 0, 260, 0, 0, 255, 0], [1, 0, 0, 250, 0, 0, 60, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[15, 0, 0, 260, 0, 0, 255, 0], [2, 0, 0, 250, 0, 0, 95, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[14, 0, 0, 260, 0, 0, 255, 0], [3, 0, 0, 250, 0, 0, 130, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[13, 0, 0, 260, 0, 0, 255, 0], [4, 0, 0, 250, 0, 0, 165, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 2, 0, 260, 0, 0, 255, 0], [5, 0, 0, 250, 0, 0, 200, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 300, 0, 0, 127, 0], [6, 0, 0, 250, 0, 0, 237, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 260, 0, 0, 200, 0], [7, 0, 0, 250, 0, 0, 237, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 100, 0, 0, 255, 1], [8, 0, 0, 250, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 100, 0, 0, 255, 1], [9, 0, 0, 250, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 100, 0, 0, 255, 1], [10, 0, 0, 250, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, -2.5, 0, 260, 0, 0, 60, 0], [11, 0, 0, 250, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 280, 0, 0, 127, 0], [12, 0, 0, 250, 5, 0, 255, 0], [-1, 245, -74, 100, 0, 0, 255, 1], [-1, 408, -76, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 280, 0, 0, 127, 0], [12, 0, 0, 260, 8, 0, 200, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 280, 0, 0, 127, 0], [12, 0, 0, 270, 11, 0, 127, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]]], "name": "Blind", "position": 1, "timings": [{"flashColor": [0, 0, 0, 68], "flashDuration": 20, "flashScope": 1, "frame": 2, "se": {"name": "Blind", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [0, 0, 0, 34], "flashDuration": 20, "flashScope": 2, "frame": 2, "se": null}, {"flashColor": [0, 0, 0, 170], "flashDuration": 20, "flashScope": 1, "frame": 3, "se": {"name": "Darkness2", "pan": 0, "pitch": 80, "volume": 50}}, {"flashColor": [0, 0, 0, 68], "flashDuration": 20, "flashScope": 2, "frame": 3, "se": null}, {"flashColor": [0, 0, 0, 255], "flashDuration": 10, "flashScope": 1, "frame": 4, "se": null}, {"flashColor": [0, 0, 0, 85], "flashDuration": 10, "flashScope": 2, "frame": 4, "se": null}, {"flashColor": [0, 0, 0, 119], "flashDuration": 10, "flashScope": 2, "frame": 6, "se": null}, {"flashColor": [0, 0, 0, 255], "flashDuration": 10, "flashScope": 1, "frame": 13, "se": null}]}, {"id": 61, "animation1Hue": 0, "animation1Name": "StateSilent", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -2, 280, 0, 0, 255, 1]], [[1, 0, -2, 280, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[2, 0, -2, 280, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[3, 0, -2, 280, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[4, 0, -2, 280, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[5, 0, -2, 280, 0, 0, 255, 1], [6, 0, -2, 180, 0, 0, 255, 0], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[-1, 0, -2, 280, 0, 0, 255, 1], [7, 0, -2, 180, 0, 0, 255, 0], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[-1, -2.5, -2, 280, 0, 0, 255, 1], [8, 0, -2, 180, 0, 0, 255, 0], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[-1, 0, -2, 280, 0, 0, 255, 1], [9, 0, -2, 180, 0, 0, 255, 0], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[-1, 0, -2, 280, 0, 0, 255, 1], [10, 0, -2, 180, 0, 0, 255, 0], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1], [-1, 0, -2, 100, 0, 0, 255, 1]], [[12, 40, -217.5, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[13, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[14, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[15, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[16, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[17, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[18, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[18, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[18, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[18, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]], [[18, 40, -218, 130, 0, 0, 255, 0], [11, 0, -2, 180, 0, 0, 255, 0]]], "name": "Silence", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Silence", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 153, 255], "flashDuration": 6, "flashScope": 1, "frame": 5, "se": {"name": "Sound2", "pan": 0, "pitch": 80, "volume": 70}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 3, "flashScope": 1, "frame": 15, "se": {"name": "Flash1", "pan": 0, "pitch": 70, "volume": 70}}]}, {"id": 62, "animation1Hue": 0, "animation1Name": "StateSleep", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 300, 0, 0, 255, 1]], [[1, 0, 0, 300, 0, 0, 255, 1], [-1, 365, -39, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[2, 0, 0, 300, 0, 0, 255, 1], [9, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, 0, 300, 0, 0, 255, 1], [10, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 300, 0, 0, 255, 1], [11, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 300, 0, 0, 255, 1], [12, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[6, 0, 0, 300, 0, 0, 255, 1], [13, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, 0, 300, 0, 0, 255, 1], [14, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[8, 0, 0, 300, 0, 0, 255, 1], [15, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[8, 0, 0, 300, 0, 0, 127, 1], [16, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 300, 0, 0, 255, 1], [17, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 2.5, 0, 300, 0, 0, 196, 1], [18, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 300, 0, 0, 138, 1], [19, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, -2.5, 300, 0, 0, 79, 1], [20, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 300, 0, 0, 20, 1], [21, 0, 0, 280, 0, 0, 255, 1]]], "name": "Sleep", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Up1", "pan": 0, "pitch": 50, "volume": 70}}, {"flashColor": [170, 221, 255, 255], "flashDuration": 6, "flashScope": 1, "frame": 5, "se": {"name": "Sleep", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 3, "flashScope": 1, "frame": 14, "se": null}]}, {"id": 63, "animation1Hue": 0, "animation1Name": "StateChaos", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -30, 300, 0, 0, 255, 0]], [[1, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[2, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[3, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[4, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[5, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[6, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[7, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[8, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[9, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[10, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[11, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[12, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -80, 130, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[13, 0, -30, 300, 0, 0, 255, 0], [-1, 0, -80, 130, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[14, 0, -30, 300, 0, 0, 255, 0], [17, 0, -32, 130, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[15, 0, -30, 300, 0, 0, 100, 0], [18, 0, -32, 130, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1]], [[16, 0, -30, 300, 0, 0, 50, 0], [19, 0, -32, 130, 0, 0, 255, 1]], [[-1, 0, -30, 300, 0, 0, 255, 1], [20, 0, -32, 130, 0, 0, 255, 1]], [[-1, 0, -30, 300, 0, 0, 255, 1], [21, 0, -32, 130, 0, 0, 255, 1]], [[-1, 0, -30, 300, 0, 0, 255, 1], [22, 0, -32, 130, 0, 0, 255, 1]], [[-1, 0, -30, 300, 0, 0, 255, 1], [23, 0, -32, 130, 0, 0, 255, 1]], [[-1, 0, -30, 300, 0, 0, 255, 1], [23, 0, -32, 130, 0, 0, 255, 1]], [[-1, 0, -30, 300, 0, 0, 255, 1], [23, 0, -32, 130, 0, 0, 255, 1]]], "name": "Confusion", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Absorb2", "pan": 0, "pitch": 50, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 4, "se": {"name": "Raise2", "pan": 0, "pitch": 70, "volume": 80}}, {"flashColor": [255, 255, 187, 187], "flashDuration": 5, "flashScope": 1, "frame": 15, "se": null}]}, {"id": 64, "animation1Hue": 0, "animation1Name": "StateParalys", "animation2Hue": 0, "animation2Name": "", "frames": [[[18, 0, 0, 350, 0, 0, 180, 1]], [[19, 0, 0, 300, 0, 0, 218, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -40, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[20, 0, 0, 250, 0, 0, 255, 1], [-1, 2.5, -10, 280, 0, 0, 255, 1], [-1, 0, -40, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[18, 0, 0, 200, 0, 0, 203, 1], [6, 0, -10, 240, 0, 0, 255, 1], [-1, 0, -40, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[19, 0, 0, 150, 0, 0, 152, 1], [7, 0, -10, 240, 0, 0, 255, 1], [-1, 0, -40, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1], [-1, 0, -10, 100, 0, 0, 255, 1]], [[20, 0, 0, 100, 0, 0, 100, 1], [8, 0, -10, 240, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [9, 0, -10, 240, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [10, 0, -10, 240, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [11, 0, -10, 240, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [12, 0, -10, 240, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [13, 0, -10, 240, 0, 0, 255, 1]], [[-1, 408, -242, 100, 0, 0, 255, 1], [14, 0, -10, 240, 0, 0, 255, 1], [0, 0, -40, 250, 0, 0, 255, 1]], [[-1, 407, -194.5, 100, 0, 0, 255, 1], [15, 0, -10, 240, 0, 0, 255, 1], [1, 0, -40, 250, 0, 0, 255, 1]], [[-1, 375, -211, 100, 0, 0, 255, 1], [16, 0, -10, 240, 0, 0, 255, 1], [2, 0, -40, 250, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [-1, 0, -10, 0, 0, 0, 0, 0], [3, 0, -40, 250, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [-1, 0, -10, 0, 0, 0, 0, 0], [4, 0, -40, 250, 0, 0, 255, 1]], [[-1, 0, -10, 0, 0, 0, 0, 0], [-1, 0, -10, 0, 0, 0, 0, 0], [5, 0, -40, 250, 0, 0, 200, 1]]], "name": "Paralyze", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Darkness1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 3, "se": {"name": "Paralyze3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 136, 102], "flashDuration": 5, "flashScope": 1, "frame": 4, "se": null}, {"flashColor": [255, 255, 136, 102], "flashDuration": 5, "flashScope": 1, "frame": 7, "se": null}, {"flashColor": [255, 255, 136, 102], "flashDuration": 5, "flashScope": 1, "frame": 10, "se": null}, {"flashColor": [255, 255, 136, 102], "flashDuration": 6, "flashScope": 1, "frame": 13, "se": null}]}, {"id": 65, "animation1Hue": 0, "animation1Name": "StateDeath", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 180, 0, 0, 255, 0]], [[2, 0, 0, 180, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, 0, 180, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 255, 0]], [[5, 0, 0, 180, 0, 0, 255, 0]], [[5, 0, 0, 180, 0, 0, 255, 0]], [[5, 0, 0, 180, 0, 0, 200, 0], [6, 0, 0, 150, 0, 0, 100, 0]], [[5, 0, 0, 180, 0, 0, 100, 0], [7, 0, 0, 150, 0, 0, 200, 0]], [[5, 0, 0, 180, 0, 0, 50, 0], [8, 0, 0, 150, 0, 0, 255, 0], [12, 0, 0, 250, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [9, 0, 0, 150, 0, 0, 255, 0], [13, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 255, 0], [14, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 255, 0], [15, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 255, 0], [16, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 255, 0], [17, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 255, 0], [18, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 255, 0], [19, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 255, 0], [-1, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 255, 0], [-1, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 200, 0], [-1, 0.5, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 180, 0, 0, 0, 0], [10, 0, 0, 150, 0, 0, 100, 0], [-1, -2.5, -2.5, 250, 0, 0, 255, 1], [-1, 408, 240.5, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]]], "name": "Death", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Stare", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [0, 0, 0, 68], "flashDuration": 5, "flashScope": 2, "frame": 8, "se": {"name": "Darkness5", "pan": 0, "pitch": 90, "volume": 80}}, {"flashColor": [0, 0, 0, 119], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": null}, {"flashColor": [0, 0, 0, 153], "flashDuration": 15, "flashScope": 1, "frame": 10, "se": null}, {"flashColor": [0, 0, 0, 153], "flashDuration": 10, "flashScope": 2, "frame": 10, "se": null}, {"flashColor": [153, 0, 0, 187], "flashDuration": 5, "flashScope": 2, "frame": 10, "se": null}, {"flashColor": [51, 0, 0, 255], "flashDuration": 15, "flashScope": 1, "frame": 11, "se": null}, {"flashColor": [51, 0, 0, 221], "flashDuration": 15, "flashScope": 1, "frame": 12, "se": null}]}, {"id": 66, "animation1Hue": 0, "animation1Name": "Fire1", "animation2Hue": 0, "animation2Name": "", "frames": [[[23, 0, -160, 180, 0, 0, 200, 1], [23, 0, -176, 200, 0, 0, 255, 0], [0, 0, -230, 300, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 255, 1], [14, 0, -200, 300, 0, 0, 255, 0]], [[25, 0, -200, 250, 0, 0, 200, 1], [25, 0, -273, 353, 0, 0, 152, 0], [2, 0, -230, 300, 0, 0, 255, 0], [-1, 207.5, -45, 300, 0, 0, 255, 0], [18, 0, -208, 300, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[26, 0, -200, 250, 0, 0, 200, 1], [26, 0, -304, 400, 0, 0, 100, 0], [3, 0, -230, 300, 0, 0, 255, 0], [-1, 105, -52.5, 300, 0, 0, 255, 0], [19, 0, -200, 300, 0, 0, 255, 0]], [[27, 0, -200, 250, 0, 0, 200, 1], [-1, 408, 312, 100, 0, 0, 255, 0], [4, 0, -230, 300, 0, 0, 255, 0], [-1, 112.5, -35, 300, 0, 0, 255, 0], [20, 0, -200, 300, 0, 0, 255, 0]], [[28, 0, -200, 250, 0, 0, 200, 1], [-1, 408, 309.5, 100, 0, 0, 255, 0], [5, 0, -230, 300, 0, 0, 255, 0], [-1, 82.5, -97.5, 300, 0, 0, 255, 0], [21, 0, -200, 300, 0, 0, 255, 0]], [[26, 0, -200, 250, 0, 0, 200, 1], [22, 0, -200, 300, 0, 0, 255, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [3, 0, -230, 300, 0, 0, 255, 0]], [[27, 0, -200, 250, 0, 0, 200, 1], [-1, 408, 312, 100, 0, 0, 255, 0], [4, 0, -230, 300, 0, 0, 255, 0]], [[28, 0, -200, 250, 0, 0, 200, 1], [-1, 408, 312, 100, 0, 0, 255, 0], [5, 0, -230, 300, 0, 0, 255, 0]], [[26, 0, -200, 250, 0, 0, 200, 1], [-1, 408, 312, 100, 0, 0, 255, 1], [3, 0, -230, 300, 0, 0, 255, 0]], [[27, 0, -200, 250, 0, 0, 200, 1], [-1, 405.5, 312, 100, 0, 0, 255, 0], [6, 0, -230, 300, 0, 0, 255, 0]], [[28, 0, -200, 250, 0, 0, 200, 1], [-1, 408, 312, 100, 0, 0, 255, 0], [7, 0, -230, 300, 0, 0, 255, 0]]], "name": "Fire One 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Fire3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 204, 119, 255], "flashDuration": 15, "flashScope": 1, "frame": 1, "se": null}, {"flashColor": [255, 204, 119, 170], "flashDuration": 5, "flashScope": 2, "frame": 2, "se": null}, {"flashColor": [255, 204, 119, 255], "flashDuration": 5, "flashScope": 1, "frame": 4, "se": null}, {"flashColor": [255, 204, 119, 255], "flashDuration": 5, "flashScope": 1, "frame": 7, "se": null}]}, {"id": 67, "animation1Hue": 0, "animation1Name": "Fire2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -120, 195, 0, 0, 255, 1]], [[1, 0, -96, 150, 0, 0, 255, 1]], [[1, 0, -132, 210, 0, 0, 255, 1]], [[2, 0, -132, 225, 0, 0, 255, 1]], [[3, 0, -132, 225, 0, 0, 255, 1]], [[4, 0, -132, 225, 0, 0, 255, 1]], [[5, 0, -120, 210, 0, 0, 255, 1]], [[5, 0, -120, 210, 0, 0, 150, 1]]], "name": "Fire One 2", "position": 2, "timings": [{"flashColor": [255, 102, 34, 221], "flashDuration": 3, "flashScope": 1, "frame": 0, "se": {"name": "Fire2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 102, 34, 204], "flashDuration": 5, "flashScope": 1, "frame": 2, "se": null}]}, {"id": 68, "animation1Hue": 0, "animation1Name": "Fire1", "animation2Hue": 0, "animation2Name": "", "frames": [[[29, -276, 115.5, 200, 0, 0, 128, 0], [29, -301, 86.5, 200, 0, 0, 0, 0], [29, -308, 21, 330, 0, 0, 0, 0], [29, -26, -71, 200, 0, 1, 0, 0], [29, -96.5, -93.5, 200, 0, 0, 0, 0], [29, -91.5, -93.5, 200, 0, 0, 0, 0], [-1, 30, 0, 0, 0, 0, 0, 0], [-1, 30, 0, 0, 0, 0, 0, 0], [-1, 30, -10, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [0, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [29, -308, 84.5, 200, 0, 0, 0, 0], [29, -308, 29, 330, 0, 0, 0, 0], [29, -63.5, -63.5, 200, 0, 1, 0, 0], [29, -136, -86, 200, 0, 0, 0, 0], [29, -156, -101, 200, 0, 0, 0, 0], [29, 310, 130, 200, 0, 0, 128, 0], [29, 320, 122, 200, 0, 0, 0, 0], [29, 320, -18, 320, 0, 0, 0, 0], [0, -130, -90, 250, 0, 1, 255, 1], [1, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [8, -275, 102, 200, 0, 0, 255, 1], [0, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 128, 0], [29, 0, 50, 200, 0, 0, 0, 0], [29, -20, 32.5, 200, 0, 0, 0, 0], [29, 310, 130, 200, 0, 0, 255, 0], [29, 320, 122, 200, 0, 0, 0, 0], [29, 320, -18, 320, 0, 0, 0, 0], [1, -130, -90, 250, 0, 1, 255, 1], [2, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [9, -276, 102, 200, 0, 0, 255, 1], [1, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [29, 0, 50, 200, 0, 0, 0, 0], [29, 10, -85, 200, 0, 0, 0, 0], [29, 310, 130, 200, 0, 0, 255, 0], [8, 320, 122, 200, 0, 0, 255, 1], [0, 320, -18, 320, 0, 0, 255, 1], [2, -130, -90, 250, 0, 1, 255, 1], [3, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [10, -276, 102, 200, 0, 0, 255, 1], [2, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [8, 0, 50, 200, 0, 0, 255, 1], [0, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [9, 320, 122, 200, 0, 0, 255, 1], [1, 320, -18, 320, 0, 0, 255, 1], [3, -130, -90, 250, 0, 1, 255, 1], [4, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [11, -276, 102, 200, 0, 0, 255, 1], [3, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [9, 0, 50, 200, 0, 0, 255, 1], [1, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [10, 320, 122, 200, 0, 0, 255, 1], [2, 320, -18, 320, 0, 0, 255, 1], [4, -130, -90, 250, 0, 1, 255, 1], [5, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [12, -276, 102, 200, 0, 0, 255, 1], [4, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [10, 0, 50, 200, 0, 0, 255, 1], [2, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [11, 320, 122, 200, 0, 0, 255, 1], [3, 320, -18, 320, 0, 0, 255, 1], [5, -130, -90, 250, 0, 1, 255, 1], [3, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [13, -276, 102, 200, 0, 0, 255, 1], [5, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [11, 0, 50, 200, 0, 0, 255, 1], [3, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [12, 320, 122, 200, 0, 0, 255, 1], [4, 320, -18, 320, 0, 0, 255, 1], [3, -130, -90, 250, 0, 1, 255, 1], [4, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [13, -276, 102, 200, 0, 0, 128, 1], [3, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [12, 0, 50, 200, 0, 0, 255, 1], [4, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [13, 320, 122, 200, 0, 0, 255, 1], [5, 320, -18, 320, 0, 0, 255, 1], [4, -130, -90, 250, 0, 1, 255, 1], [5, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [13, -276, 102, 200, 0, 0, 0, 1], [4, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [13, 0, 50, 200, 0, 0, 255, 1], [5, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [13, 320, 122, 200, 0, 0, 128, 1], [3, 320, -18, 320, 0, 0, 255, 1], [5, -130, -90, 250, 0, 1, 255, 1], [6, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [13, -276, 102, 200, 0, 0, 0, 1], [5, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [13, 0, 50, 200, 0, 0, 128, 1], [3, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [13, 320, 122, 200, 0, 0, 0, 1], [4, 320, -18, 320, 0, 0, 255, 1], [6, -130, -90, 250, 0, 1, 255, 1], [7, 200, -80, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 255, 0], [13, -276, 102, 200, 0, 0, 0, 1], [6, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [13, 0, 50, 200, 0, 0, 0, 1], [4, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [13, 320, 122, 200, 0, 0, 0, 1], [5, 320, -18, 320, 0, 0, 255, 1], [7, -130, -90, 250, 0, 1, 255, 1]], [[29, -276, 116, 200, 0, 0, 128, 0], [13, -276, 102, 200, 0, 0, 0, 1], [7, -280, -50, 330, 0, 0, 255, 1], [29, 30, 63, 200, 0, 1, 255, 0], [13, 0, 50, 200, 0, 0, 0, 1], [5, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 255, 0], [13, 320, 122, 200, 0, 0, 0, 1], [6, 320, -18, 320, 0, 0, 255, 1]], [[29, -276, 116, 200, 0, 0, 0, 0], [13, -276, 102, 200, 0, 0, 0, 1], [7, -280, -50, 330, 0, 0, 0, 1], [29, 30, 63, 200, 0, 1, 255, 0], [13, 0, 50, 200, 0, 0, 0, 1], [6, 10, -85, 300, 0, 0, 255, 1], [29, 310, 130, 200, 0, 0, 128, 0], [13, 320, 122, 200, 0, 0, 0, 1], [7, 320, -18, 320, 0, 0, 255, 1]], [[29, -276, 116, 200, 0, 0, 0, 0], [13, -276, 102, 200, 0, 0, 0, 1], [7, -280, -50, 330, 0, 0, 0, 1], [29, 30, 63, 200, 0, 1, 128, 0], [13, 0, 50, 200, 0, 0, 0, 1], [7, 10, -85, 300, 0, 0, 255, 1], [13, 408, 155, 200, 0, 0, 0, 1], [13, 320, 122, 200, 0, 0, 0, 1], [7, 320, -18, 320, 0, 0, 0, 1]]], "name": "Fire All 1", "position": 3, "timings": [{"flashColor": [255, 153, 0, 153], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": {"name": "Fire3", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [255, 221, 0, 221], "flashDuration": 5, "flashScope": 1, "frame": 1, "se": null}, {"flashColor": [255, 153, 0, 153], "flashDuration": 5, "flashScope": 2, "frame": 4, "se": {"name": "Explosion1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 221, 0, 221], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [255, 153, 0, 153], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": {"name": "Fire2", "pan": 0, "pitch": 80, "volume": 90}}]}, {"id": 69, "animation1Hue": 0, "animation1Name": "Fire2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -24, 255, 0, 0, 255, 1]], [[1, 0, -24, 255, 0, 0, 255, 1]], [[2, 0, -24, 255, 0, 0, 255, 1], [0, -300, -48, 255, 0, 0, 255, 1], [7, 0, -108, 240, 0, 0, 200, 1]], [[3, 0, -24, 255, 0, 0, 255, 1], [1, -300, -48, 255, 0, 0, 255, 1], [8, 0, -108, 240, 0, 0, 150, 1]], [[4, 0, -24, 255, 0, 0, 255, 1], [2, -300, -48, 255, 0, 0, 255, 1], [7, -300, -108, 240, 0, 0, 200, 1]], [[3, -300, -48, 255, 0, 0, 255, 1], [5, 0, -24, 255, 0, 0, 255, 1], [0, 288, -48, 255, 0, 0, 255, 1], [0, -132, 12, 150, 0, 0, 255, 1], [8, -300, -108, 240, 0, 0, 150, 1]], [[5, 0, -24, 255, 0, 0, 180, 1], [4, -300, -48, 255, 0, 0, 255, 1], [1, 288, -48, 255, 0, 0, 255, 1], [1, -132, -12, 195, 0, 0, 255, 1]], [[5, -300, -48, 255, 0, 0, 255, 1], [2, 288, -48, 255, 0, 0, 255, 1], [2, -132, -12, 180, 0, 0, 255, 1], [0, 120, 12, 150, 0, 0, 255, 1], [7, 288, -108, 240, 0, 0, 200, 1]], [[5, -300, -48, 255, 0, 0, 150, 1], [3, 288, -48, 255, 0, 0, 255, 1], [3, -132, -12, 180, 0, 0, 255, 1], [1, 120, -12, 180, 0, 0, 255, 1], [8, 288, -108, 240, 0, 0, 150, 1]], [[4, 288, -48, 255, 0, 0, 255, 1], [4, -132, -12, 180, 0, 0, 255, 1], [-1, 96, 0, 180, 0, 0, 255, 1], [2, 120, -12, 180, 0, 0, 255, 1]], [[5, 288, -48, 255, 0, 0, 255, 1], [5, -132, -12, 180, 0, 0, 255, 1], [3, 120, -12, 180, 0, 0, 200, 1]], [[5, 288, -48, 255, 0, 0, 150, 1], [5, -132, -12, 180, 0, 0, 150, 1], [4, 120, -12, 180, 0, 0, 200, 1]], [[5, 120, -12, 180, 0, 0, 200, 1]], [[5, 120, -12, 180, 0, 0, 100, 1]]], "name": "Fire All 2", "position": 3, "timings": [{"flashColor": [255, 187, 68, 153], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": {"name": "Fire3", "pan": 0, "pitch": 90, "volume": 100}}, {"flashColor": [255, 102, 0, 187], "flashDuration": 5, "flashScope": 1, "frame": 0, "se": null}, {"flashColor": [255, 153, 0, 204], "flashDuration": 3, "flashScope": 1, "frame": 3, "se": null}, {"flashColor": [255, 187, 68, 153], "flashDuration": 5, "flashScope": 2, "frame": 5, "se": null}, {"flashColor": [255, 119, 0, 187], "flashDuration": 3, "flashScope": 1, "frame": 6, "se": null}]}, {"id": 70, "animation1Hue": 330, "animation1Name": "PreSpecial1", "animation2Hue": 0, "animation2Name": "Fire3", "frames": [[[0, 0, -72, 330, 0, 0, 255, 1]], [[1, 0, -72, 330, 0, 0, 255, 1]], [[2, 0, -72, 330, 0, 0, 255, 1]], [[3, 0, -72, 330, 0, 0, 255, 1]], [[4, 0, -72, 330, 0, 0, 255, 1], [17, 0, -72, 390, 0, 0, 150, 1]], [[5, 0, -72, 330, 0, 0, 255, 1], [18, 0, -72, 345, 0, 0, 255, 1]], [[6, 0, -72, 330, 0, 0, 255, 1], [19, 0, -72, 345, 0, 0, 255, 1]], [[20, 0, -72, 345, 0, 0, 255, 1], [7, 0, -72, 330, 0, 0, 255, 1]], [[8, 0, -72, 330, 0, 0, 255, 1], [22, 0, -84, 420, 0, 0, 255, 1]], [[9, 0, -72, 330, 0, 0, 255, 1], [23, 0, -84, 420, 0, 0, 255, 1]], [[10, 0, -72, 330, 0, 0, 255, 1], [24, 0, -84, 420, 0, 0, 255, 1]], [[11, 0, -72, 330, 0, 0, 255, 1], [25, 0, -84, 450, 0, 0, 255, 1], [100, 0, -96, 240, 0, 0, 50, 0]], [[12, 0, -72, 330, 0, 0, 255, 1], [20, 0, -84, 300, 0, 0, 200, 1], [22, 0, -84, 375, 0, 0, 255, 1], [26, 0, -84, 450, 0, 0, 255, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[13, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 465, 0, 0, 200, 1], [125, 0, 72, 525, 0, 0, 50, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[14, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 480, 0, 0, 180, 1], [125, 0, 72, 525, 0, 0, 100, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[15, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 487, 0, 0, 150, 1], [125, 0, 72, 525, 0, 0, 150, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[16, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 495, 0, 0, 120, 1], [125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[26, 0, -84, 502, 0, 0, 100, 1], [16, 0, -72, 330, 0, 0, 200, 1], [125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[16, 0, -72, 330, 0, 0, 150, 1], [26, 0, -84, 502, 0, 0, 70, 1], [125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[16, 0, -72, 330, 0, 0, 100, 1], [26, 0, -84, 502, 0, 0, 40, 1], [125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[16, 0, -72, 330, 0, 0, 50, 1], [125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 60, 0], [102, 0, -120, 300, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 40, 0], [103, 0, -132, 330, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 20, 0], [104, 0, -132, 337, 0, 0, 255, 0], [114, -228, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [105, 0, -132, 345, 0, 0, 255, 0], [115, -228, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [106, 0, -132, 345, 0, 0, 255, 0], [116, -228, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [107, 0, -132, 345, 0, 0, 255, 0], [117, -228, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [114, 228, -84, 240, 0, 0, 255, 0], [108, 0, -132, 345, 0, 0, 255, 0], [118, -228, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [115, 228, -84, 240, 0, 0, 255, 0], [109, 0, -132, 345, 0, 0, 255, 0], [119, -228, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [116, 228, -84, 240, 0, 0, 255, 0], [110, 0, -132, 345, 0, 0, 255, 0], [120, -228, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [117, 228, -84, 240, 0, 0, 255, 0], [111, 0, -132, 345, 0, 0, 255, 0], [121, -228, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [118, 228, -84, 240, 0, 0, 255, 0], [112, 0, -132, 345, 0, 0, 255, 0], [122, -228, -84, 240, 0, 0, 255, 0], [114, -312, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [119, 228, -84, 240, 0, 0, 255, 0], [113, 0, -132, 345, 0, 0, 255, 0], [123, -228, -84, 240, 0, 0, 255, 0], [115, -312, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [120, 228, -84, 240, 0, 0, 255, 0], [108, 0, -132, 345, 0, 0, 255, 0], [124, -228, -84, 240, 0, 0, 255, 0], [116, -312, -84, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 1], [121, 228, -84, 240, 0, 0, 255, 0], [109, 0, -132, 345, 0, 0, 255, 0], [114, 300, -80, 240, 0, 0, 255, 1], [117, -312, -84, 240, 0, 0, 255, 1]], [[125, 2.5, 69.5, 525, 0, 0, 180, 1], [122, 228, -84, 240, 0, 0, 255, 0], [110, 0, -132, 345, 0, 0, 255, 0], [115, 300, -80, 240, 0, 0, 255, 1], [119, -312, -84, 240, 0, 0, 255, 1]], [[125, 0, 72, 525, 0, 0, 180, 1], [123, 228, -84, 240, 0, 0, 255, 0], [111, 0, -132, 345, 0, 0, 255, 0], [116, 300, -80, 240, 0, 0, 255, 1], [120, -312, -84, 240, 0, 0, 255, 1]], [[125, 0, 72, 525, 0, 0, 180, 1], [124, 228, -84, 240, 0, 0, 255, 0], [112, 0, -132, 345, 0, 0, 255, 0], [117, 300, -80, 240, 0, 0, 255, 1], [121, -312, -84, 240, 0, 0, 255, 1]], [[125, 0, 72, 525, 0, 0, 180, 1], [113, 0, -132, 345, 0, 0, 255, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [118, 300, -80, 240, 0, 0, 255, 1], [122, -312, -84, 240, 0, 0, 255, 1]], [[125, 0, 72, 525, 0, 0, 180, 1], [108, 0, -132, 345, 0, 0, 255, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [119, 300, -80, 240, 0, 0, 255, 1], [123, -312, -84, 240, 0, 0, 255, 1]], [[125, 0, 72, 525, 0, 0, 180, 1], [109, 0, -132, 345, 0, 0, 255, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [120, 300, -80, 240, 0, 0, 255, 1], [124, -312, -84, 240, 0, 0, 255, 1]], [[125, 0, 72, 525, 0, 0, 180, 1], [110, 0, -132, 345, 0, 0, 255, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [121, 300, -80, 240, 0, 0, 255, 1]], [[126, 0, 72, 525, 0, 0, 180, 1], [111, 0, -132, 345, 0, 0, 255, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [122, 300, -80, 240, 0, 0, 255, 1]], [[126, 0, 72, 525, 0, 0, 180, 1], [112, 0, -132, 345, 0, 0, 255, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [123, 300, -80, 240, 0, 0, 255, 1]], [[126, 0, 72, 525, 0, 0, 180, 1], [113, 0, -132, 345, 0, 0, 255, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [124, 300, -80, 240, 0, 0, 255, 1]], [[126, 0, 72, 525, 0, 0, 180, 1], [108, 0, -132, 345, 0, 0, 255, 0], [-1, -198, 274, 240, 0, 0, 255, 0]], [[126, 0, 72, 525, 0, 0, 180, 1], [109, 0, -132, 345, 0, 0, 197, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[126, 0, 72, 525, 0, 0, 180, 1], [110, 0, -132, 345, 0, 0, 138, 0], [-1, -198, 274, 240, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[126, 0, 72, 525, 0, 0, 180, 1], [111, 0, -132, 345, 0, 0, 80, 0], [-1, -198, 274, 240, 0, 0, 255, 0]]], "name": "Fire All 3", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 70, "volume": 65}}, {"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 85, 0, 153], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": {"name": "Fire2", "pan": 0, "pitch": 110, "volume": 80}}, {"flashColor": [255, 187, 51, 204], "flashDuration": 5, "flashScope": 2, "frame": 22, "se": {"name": "Explosion2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 221, 51, 255], "flashDuration": 5, "flashScope": 1, "frame": 23, "se": null}, {"flashColor": [255, 221, 51, 255], "flashDuration": 5, "flashScope": 1, "frame": 29, "se": null}]}, {"id": 71, "animation1Hue": 0, "animation1Name": "Ice1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 200, 0, 0, 255, 1]], [[1, 0, 0, 200, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[2, 0, 0, 200, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, 0, 200, 0, 0, 255, 1], [14, 0, 0, 130, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 255, 1], [14, 0, 0, 168, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 205, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 243, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 280, 0, 0, 128, 1], [5, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 280, 0, 0, 0, 1], [6, 0, 0, 250, 0, 0, 255, 1], [13, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 280, 0, 0, 0, 1], [7, 0, 0, 250, 0, 0, 255, 1], [13, 0, 0, 143, 15, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 280, 0, 0, 0, 1], [8, 0, 0, 250, 0, 0, 255, 1], [13, 0, 0, 185, 30, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 280, 0, 0, 0, 1], [9, 0, 0, 250, 0, 0, 255, 1], [13, 0, 0, 228, 45, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 280, 0, 0, 0, 1], [10, 0, 0, 250, 0, 0, 255, 1], [13, 0, 0, 270, 60, 0, 255, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 280, 0, 0, 0, 1], [11, 0, 0, 250, 0, 0, 255, 1], [13, 0, 0, 270, 80, 0, 128, 1]], [[4, 0, 0, 200, 0, 0, 0, 1], [14, 0, 0, 280, 0, 0, 0, 1], [12, 0, 0, 250, 0, 0, 255, 1]]], "name": "Ice One 1", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Starlight", "pan": 0, "pitch": 150, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Wind1", "pan": 0, "pitch": 80, "volume": 70}}, {"flashColor": [136, 255, 255, 238], "flashDuration": 5, "flashScope": 1, "frame": 3, "se": {"name": "Crash", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [136, 255, 255, 68], "flashDuration": 5, "flashScope": 2, "frame": 3, "se": null}, {"flashColor": [136, 255, 255, 136], "flashDuration": 5, "flashScope": 2, "frame": 4, "se": null}, {"flashColor": [136, 255, 255, 238], "flashDuration": 5, "flashScope": 1, "frame": 9, "se": {"name": "Ice1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 13, "se": {"name": "Sword2", "pan": 0, "pitch": 150, "volume": 90}}]}, {"id": 72, "animation1Hue": 60, "animation1Name": "Ice3", "animation2Hue": 0, "animation2Name": "HitIce", "frames": [[[-1, 0, -132, 195, 0, 0, 255, 1], [5, -12, -108, 120, 0, 0, 150, 1]], [[-1, 0, -132, 195, 0, 0, 255, 1], [5, -12, -108, 120, 0, 0, 255, 1]], [[0, 0, -132, 195, 0, 0, 255, 1], [5, -12, -120, 135, 0, 0, 255, 1]], [[6, -12, -132, 165, 0, 0, 255, 1], [1, 0, -144, 225, 0, 0, 255, 1]], [[7, -12, -132, 165, 0, 0, 255, 1], [2, 0, -144, 225, 0, 0, 255, 1]], [[8, -12, -132, 165, 0, 0, 255, 0], [3, 0, -144, 229, 0, 0, 230, 1], [105, 0, -144, 225, 0, 0, 255, 1]], [[8, -12, -132, 165, 0, 0, 255, 0], [4, 0, -144, 232, 0, 0, 230, 1], [106, 0, -144, 165, 0, 0, 255, 1]], [[8, -12, -132, 165, 0, 0, 255, 0], [4, 0, -144, 236, 0, 0, 200, 1], [107, 0, -144, 172, 0, 0, 255, 1]], [[8, -12, -132, 165, 0, 0, 255, 0], [4, 0, -144, 239, 0, 0, 180, 1], [106, 0, -144, 180, 0, 0, 255, 1]], [[8, -12, -132, 165, 0, 0, 255, 0], [4, 0, -144, 243, 0, 0, 150, 1], [107, 0, -144, 193, 0, 0, 200, 1]], [[8, -12, -132, 165, 0, 0, 180, 0], [4, 0, -144, 246, 0, 0, 120, 1], [106, 0, -144, 207, 0, 0, 180, 1]], [[8, -12, -132, 165, 0, 0, 100, 0], [4, 0, -144, 250, 0, 0, 80, 1], [107, 0, -144, 220, 0, 0, 100, 1]]], "name": "Ice One 2", "position": 2, "timings": [{"flashColor": [187, 238, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Ice4", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [187, 238, 255, 187], "flashDuration": 5, "flashScope": 1, "frame": 3, "se": null}]}, {"id": 73, "animation1Hue": 0, "animation1Name": "Ice1", "animation2Hue": 0, "animation2Name": "Ice2", "frames": [[[100, -240, 80, 350, 0, 0, 255, 1], [-1, -140, 92.5, 250, 0, 0, 0, 0], [-1, -315, -61.5, 100, 0, 0, 0, 1], [-1, -275, -18, 100, 0, 0, 0, 1], [-1, -169.5, 50, 250, 0, 0, 0, 0], [-1, -137.5, 30, 250, 0, 0, 0, 0], [100, 320, 64, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [100, -80, 176, 350, 0, 1, 255, 1]], [[101, -240, 80, 350, 0, 0, 255, 1], [-1, -80, 107.5, 250, 0, 0, 0, 0], [-1, -265, 116, 100, 0, 0, 0, 1], [-1, -240, 107, 100, 0, 0, 0, 1], [-1, -194.5, 127.5, 250, 0, 0, 0, 0], [-1, -245, 22.5, 250, 0, 0, 0, 0], [101, 320, 64, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [101, -80, 176, 350, 0, 1, 255, 1]], [[102, -240, 80, 350, 0, 0, 255, 1], [-1, 0, -200, 250, 0, 0, 0, 0], [-1, -285.5, -234.5, 100, 0, 0, 0, 1], [-1, -260.5, -218.5, 100, 0, 0, 0, 1], [-1, -279.5, -187.5, 250, 0, 0, 0, 0], [-1, -282.5, 30, 250, 0, 0, 0, 0], [102, 320, 64, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [102, -80, 176, 350, 0, 1, 255, 1]], [[103, -240, 80, 350, 0, 0, 255, 1], [-1, -2.5, -200, 250, 0, 0, 0, 0], [-1, -318, -239, 100, 0, 0, 0, 1], [-1, -338, -234, 100, 0, 0, 0, 1], [-1, -280, -192.5, 250, 0, 0, 0, 0], [-1, -222.5, 82.5, 250, 0, 0, 0, 0], [103, 320, 64, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [103, -80, 176, 350, 0, 1, 255, 1]], [[104, -240, 80, 350, 0, 0, 255, 1], [-1, 17.5, 177.5, 250, 0, 0, 0, 0], [-1, -408, 274.5, 100, 0, 0, 0, 1], [-1, -408, 246.5, 100, 0, 0, 0, 1], [109, -280, -190, 250, 0, 0, 100, 0], [109, 272, -200, 250, 0, 0, 100, 0], [104, 320, 64, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [104, -80, 176, 350, 0, 1, 255, 1]], [[105, -240, 80, 350, 0, 0, 255, 1], [109, 0, -200, 250, 0, 0, 100, 0], [-1, -370.5, 164.5, 100, 0, 0, 0, 1], [-1, -402.5, 224, 100, 0, 0, 0, 1], [110, -280, -192, 250, 0, 0, 188, 0], [110, 272, -200, 250, 0, 0, 188, 0], [105, 320, 64, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [105, -80, 176, 350, 0, 1, 255, 1]], [[106, -240, 80, 350, 0, 0, 255, 1], [110, 0, -200, 250, 0, 0, 188, 0], [-1, -388, 52, 100, 0, 0, 0, 1], [-1, -408, 187, 100, 0, 0, 0, 1], [111, -280, -190, 250, 0, 0, 255, 0], [111, 272, -200, 250, 0, 0, 255, 0], [106, 320, 64, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [106, -80, 176, 350, 0, 1, 255, 1]], [[107, -240, 80, 350, 0, 0, 255, 1], [111, 0, -200, 250, 0, 0, 255, 0], [-1, -390, 145.5, 100, 0, 0, 0, 1], [-1, -408, 155, 100, 0, 0, 0, 1], [112, -280, -190, 250, 0, 0, 255, 0], [112, 272, -200, 250, 0, 0, 255, 0], [107, 320, 64, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [107, -80, 176, 350, 0, 1, 255, 1]], [[108, -226.5, 0, 350, 0, 0, 255, 1], [112, 0, -200, 250, 0, 0, 255, 0], [-1, -385, 236.5, 100, 0, 0, 0, 1], [-1, -405, 179, 100, 0, 0, 0, 1], [113, -280, -190, 250, 0, 0, 255, 0], [113, 272, -200, 250, 0, 0, 255, 0], [108, 336, -20, 350, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [108, -60, 100, 350, 0, 1, 255, 1]], [[107, -224, 0, 350, 0, 0, 0, 1], [113, 0, -200, 250, 0, 0, 255, 0], [-1, -405.5, 230, 100, 0, 0, 0, 1], [-1, -408, 212.5, 100, 0, 0, 0, 1], [114, -280, -190, 250, 0, 0, 255, 0], [114, 272, -200, 250, 0, 0, 255, 0]], [[108, 0, 0, 350, 0, 0, 0, 1], [114, 0, -200, 250, 0, 0, 255, 0], [-1, -360.5, 218.5, 100, 0, 0, 0, 1], [-1, -408, 233.5, 100, 0, 0, 0, 1], [115, -280, -190, 250, 0, 0, 255, 0], [115, 272, -200, 250, 0, 0, 255, 0]], [[108, 0, 0, 350, 0, 0, 0, 1], [115, 0, -200, 250, 0, 0, 255, 0], [-1, -358, 149, 100, 0, 0, 0, 1], [-1, -408, 236.5, 100, 0, 0, 0, 1], [116, -280, -190, 250, 0, 0, 255, 0], [116, 272, -200, 250, 0, 0, 255, 0]], [[-1, -55, 37.5, 350, 0, 0, 0, 1], [116, 0, -200, 250, 0, 0, 255, 0], [-1, -365.5, 203.5, 100, 0, 0, 0, 1], [-1, -396, 161, 100, 0, 0, 0, 1], [117, -280, -190, 250, 0, 0, 255, 0], [117, 272, -200, 250, 0, 0, 255, 0]], [[-1, 0, 0, 350, 0, 0, 0, 1], [117, 0, -200, 250, 0, 0, 255, 0], [-1, -315, 220.5, 100, 0, 0, 0, 1], [-1, -360, 180, 100, 0, 0, 0, 1], [118, -280, -190, 250, 0, 0, 255, 0], [118, 272, -200, 250, 0, 0, 255, 0]], [[-1, 0, 0, 350, 0, 0, 0, 1], [118, 0, -200, 250, 0, 0, 255, 0], [-1, -315.5, 209.5, 100, 0, 0, 0, 1], [-1, -338.5, 196.5, 100, 0, 0, 0, 1], [118, -280, -190, 250, 0, 0, 255, 0], [118, 272, -200, 250, 0, 0, 255, 0]], [[-1, -340, 57.5, 350, 0, 0, 0, 1], [118, 0, -200, 250, 0, 0, 255, 0], [-1, -400.5, 86, 100, 0, 0, 0, 1], [-1, -322.5, 250.5, 100, 0, 0, 0, 1], [118, -280, -80, 250, 0, 0, 255, 0], [118, 272, -200, 250, 0, 0, 255, 0]], [[-1, 17.5, 95, 350, 0, 0, 0, 1], [118, 0, -112, 250, 0, 0, 255, 0], [-1, -375, -227.5, 100, 0, 0, 0, 1], [-1, -340, -154.5, 100, 0, 0, 0, 1], [118, -280, 20, 250, 0, 0, 255, 0], [118, 272, -200, 250, 0, 0, 255, 0], [119, -280, 60, 220, 0, 0, 255, 1], [8, -270, 32, 200, 0, 0, 255, 1]], [[-1, -60, 37.5, 350, 0, 0, 0, 1], [118, 0, 16, 250, 0, 0, 255, 0], [119, 2, 50, 220, 0, 0, 255, 1], [8, 0, 0, 200, 0, 0, 255, 1], [118, -280, 20, 250, 0, 0, 255, 0], [118, 272, -112, 250, 0, 0, 255, 0], [120, -280, 60, 220, 0, 0, 255, 1], [10, -270, 32, 275, 0, 0, 255, 1]], [[-1, 32.5, 180, 350, 0, 0, 0, 1], [118, 0, 16, 250, 0, 0, 255, 0], [120, 0, 50, 220, 0, 0, 255, 1], [10, 0, 0, 275, 0, 0, 255, 1], [118, -280, 20, 250, 0, 0, 255, 0], [118, 272, 16, 250, 0, 0, 255, 0], [120, -280, 60, 220, 0, 0, 255, 1], [11, -270, 32, 350, 0, 0, 255, 1], [8, 280, 50, 200, 0, 0, 255, 1], [119, 280, 50, 220, 0, 0, 255, 1]], [[-1, 175, 200, 350, 0, 0, 0, 1], [118, 0, 16, 250, 0, 0, 255, 0], [120, 0, 50, 220, 0, 0, 255, 1], [11, 0, 2, 350, 0, 0, 255, 1], [118, -280, 20, 250, 0, 0, 255, 0], [118, 272, 16, 250, 0, 0, 255, 0], [120, -280, 60, 220, 0, 0, 255, 1], [12, -270, 32, 350, 0, 0, 255, 1], [10, 280, 50, 275, 0, 0, 255, 1], [120, 280, 50, 220, 0, 0, 255, 1]], [[-1, -167.5, 182.5, 350, 0, 0, 0, 1], [118, 0, 16, 250, 0, 0, 255, 0], [120, 0, 50, 220, 0, 0, 255, 1], [12, 0, 0, 350, 0, 0, 255, 1], [118, -280, 20, 250, 0, 0, 255, 0], [118, 272, 16, 250, 0, 0, 255, 0], [120, -280, 60, 220, 0, 0, 255, 1], [-1, -402.5, -225, 100, 0, 0, 0, 1], [11, 280, 50, 350, 0, 0, 255, 1], [120, 280, 50, 220, 0, 0, 255, 1]], [[-1, -15, 285, 350, 0, 0, 0, 1], [118, 0, 16, 250, 0, 0, 188, 0], [120, 0, 50, 220, 0, 0, 188, 1], [12, 2.5, 312, 350, 0, 0, 0, 1], [118, -280, 20, 250, 0, 0, 188, 0], [118, 272, 16, 250, 0, 0, 188, 0], [120, -280, 60, 220, 0, 0, 188, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [12, 280, 50, 350, 0, 0, 255, 1], [120, 280, 50, 220, 0, 0, 188, 1]], [[-1, -160, 312, 350, 0, 0, 0, 1], [118, 0, 16, 250, 0, 0, 180, 0], [120, 0, 50, 220, 0, 0, 180, 1], [12, 2.5, 312, 350, 0, 0, 180, 1], [118, -280, 20, 250, 0, 0, 180, 0], [118, 272, 16, 250, 0, 0, 180, 0], [120, -280, 60, 220, 0, 0, 180, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [120, 280, 50, 220, 0, 0, 180, 1]], [[-1, -160, 312, 350, 0, 0, 0, 1], [118, 0, 16, 250, 0, 0, 140, 0], [120, 0, 50, 220, 0, 0, 140, 1], [12, 2.5, 312, 350, 0, 0, 140, 1], [118, -280, 20, 250, 0, 0, 140, 0], [118, 272, 16, 250, 0, 0, 140, 0], [120, -280, 60, 220, 0, 0, 140, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [120, 280, 50, 220, 0, 0, 140, 1]], [[-1, -160, 312, 350, 0, 0, 0, 1], [118, 0, 16, 250, 0, 0, 60, 0], [120, 0, 50, 220, 0, 0, 60, 1], [12, 2.5, 312, 350, 0, 0, 60, 1], [118, -280, 20, 250, 0, 0, 60, 0], [118, 272, 16, 250, 0, 0, 60, 0], [120, -280, 60, 220, 0, 0, 60, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [120, 280, 50, 220, 0, 0, 60, 1]]], "name": "Ice All 1", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder4", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 5, "se": {"name": "Flash2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [153, 255, 255, 153], "flashDuration": 5, "flashScope": 1, "frame": 16, "se": {"name": "Earth1", "pan": 0, "pitch": 130, "volume": 70}}, {"flashColor": [153, 255, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 17, "se": {"name": "Ice5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [153, 255, 255, 153], "flashDuration": 5, "flashScope": 1, "frame": 20, "se": {"name": "Sword2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [153, 255, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 20, "se": null}]}, {"id": 74, "animation1Hue": 0, "animation1Name": "Ice4", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, -192, -96, 255, 0, 0, 100, 1], [1, 0, -96, 255, 0, 0, 100, 1], [2, 204, -96, 255, 0, 0, 100, 1]], [[1, -216, -96, 255, 0, 0, 150, 1], [2, 0, -96, 255, 0, 0, 150, 1], [3, 204, -96, 255, 0, 0, 150, 1]], [[2, -216, -96, 255, 0, 0, 200, 1], [3, 0, -96, 255, 0, 0, 200, 1], [0, 204, -96, 255, 0, 0, 200, 1]], [[3, -216, -96, 255, 0, 0, 255, 1], [0, 0, -96, 255, 0, 0, 255, 1], [1, 204, -96, 255, 0, 0, 255, 1]], [[0, -216, -96, 255, 0, 0, 255, 1], [1, 0, -96, 255, 0, 0, 255, 1], [2, 204, -96, 255, 0, 0, 255, 1]], [[1, -216, -96, 255, 0, 0, 255, 1], [2, 0, -96, 255, 0, 0, 255, 1], [3, 204, -96, 255, 0, 0, 255, 1]], [[2, -216, -96, 255, 0, 0, 255, 1], [3, 0, -96, 255, 0, 0, 255, 1], [0, 204, -96, 255, 0, 0, 255, 1]], [[3, -216, -96, 255, 0, 0, 255, 1], [0, 0, -96, 255, 0, 0, 255, 1], [1, 204, -96, 255, 0, 0, 255, 1]], [[0, -216, -96, 255, 0, 0, 255, 1], [1, 0, -96, 255, 0, 0, 255, 1], [2, 204, -96, 255, 0, 0, 255, 1]], [[1, -216, -96, 255, 0, 0, 255, 1], [2, 0, -96, 255, 0, 0, 255, 1], [3, 204, -96, 255, 0, 0, 255, 1]], [[2, -216, -96, 255, 0, 0, 255, 1], [3, 0, -96, 255, 0, 0, 255, 1], [0, 204, -96, 255, 0, 0, 255, 1]], [[3, -216, -96, 255, 0, 0, 255, 1], [0, 0, -96, 255, 0, 0, 255, 1], [1, 204, -96, 255, 0, 0, 255, 1]], [[0, -216, -96, 255, 0, 0, 200, 1], [1, 0, -96, 255, 0, 0, 255, 1], [2, 204, -96, 255, 0, 0, 200, 1]], [[1, -216, -96, 255, 0, 0, 170, 1], [2, 0, -96, 255, 0, 0, 200, 1], [3, 204, -96, 255, 0, 0, 170, 1]], [[2, -216, -96, 255, 0, 0, 100, 1], [3, 0, -96, 255, 0, 0, 120, 1], [0, 204, -96, 255, 0, 0, 100, 1]], [[3, -216, -96, 255, 0, 0, 50, 1], [0, 0, -96, 255, 0, 0, 80, 1], [1, 204, -96, 255, 0, 0, 50, 1]]], "name": "Ice All 2", "position": 3, "timings": [{"flashColor": [255, 255, 255, 136], "flashDuration": 15, "flashScope": 2, "frame": 0, "se": {"name": "Wind6", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [221, 255, 255, 102], "flashDuration": 8, "flashScope": 1, "frame": 2, "se": {"name": "Thunder6", "pan": 0, "pitch": 90, "volume": 80}}, {"conditions": 0, "flashColor": [221, 255, 255, 102], "flashDuration": 8, "flashScope": 1, "frame": 7, "se": null}]}, {"id": 75, "animation1Hue": 150, "animation1Name": "PreSpecial1", "animation2Hue": 0, "animation2Name": "Ice5", "frames": [[[0, 0, -72, 330, 0, 0, 255, 1]], [[1, 0, -72, 330, 0, 0, 255, 1]], [[2, 0, -72, 330, 0, 0, 255, 1]], [[3, 0, -72, 330, 0, 0, 255, 1]], [[4, 0, -72, 330, 0, 0, 255, 1], [17, 0, -72, 390, 0, 0, 150, 1]], [[5, 0, -72, 330, 0, 0, 255, 1], [18, 0, -72, 345, 0, 0, 255, 1]], [[6, 0, -72, 330, 0, 0, 255, 1], [19, 0, -72, 345, 0, 0, 255, 1]], [[20, 0, -72, 345, 0, 0, 255, 1], [7, 0, -72, 330, 0, 0, 255, 1]], [[8, 0, -72, 330, 0, 0, 255, 1], [22, 0, -84, 420, 0, 0, 255, 1]], [[9, 0, -72, 330, 0, 0, 255, 1], [23, 0, -84, 420, 0, 0, 255, 1]], [[10, 0, -72, 330, 0, 0, 255, 1], [24, 0, -84, 420, 0, 0, 255, 1]], [[11, 0, -72, 330, 0, 0, 255, 1], [25, 0, -84, 450, 0, 0, 255, 1], [100, 0, -108, 240, 0, 0, 50, 0]], [[12, 0, -72, 330, 0, 0, 255, 1], [20, 0, -84, 300, 0, 0, 200, 1], [22, 0, -84, 375, 0, 0, 255, 1], [26, 0, -84, 450, 0, 0, 255, 1], [100, 0, -108, 240, 0, 0, 100, 0]], [[13, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 465, 0, 0, 200, 1], [100, 0, -108, 240, 0, 0, 150, 0]], [[14, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 480, 0, 0, 180, 1], [100, 0, -108, 240, 0, 0, 150, 0]], [[15, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 487, 0, 0, 150, 1], [100, 0, -108, 240, 0, 0, 150, 0], [123, -288, -96, 225, 0, 0, 50, 1], [123, 264, -96, 225, 270, 1, 50, 1]], [[16, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 495, 0, 0, 120, 1], [100, 0, -108, 240, 0, 0, 150, 0], [123, -288, -96, 225, 0, 0, 100, 1], [123, 264, -96, 225, 270, 1, 100, 1]], [[26, 0, -84, 502, 0, 0, 100, 1], [16, 0, -72, 330, 0, 0, 200, 1], [100, 0, -108, 240, 0, 0, 150, 0], [123, -288, -96, 225, 0, 0, 150, 1], [123, 264, -96, 225, 270, 1, 150, 1]], [[16, 0, -72, 330, 0, 0, 150, 1], [26, 0, -84, 502, 0, 0, 70, 1], [100, 0, -108, 240, 0, 0, 140, 0], [123, -288, -96, 225, 0, 0, 200, 1], [123, 264, -96, 225, 270, 1, 200, 1]], [[16, 0, -72, 330, 0, 0, 100, 1], [26, 0, -84, 502, 0, 0, 40, 1], [100, 0, -108, 240, 0, 0, 130, 0], [123, 264, -96, 225, 270, 1, 200, 1], [123, -288, -96, 225, 0, 0, 200, 1]], [[16, 0, -72, 330, 0, 0, 50, 1], [100, 0, -108, 240, 0, 0, 120, 0], [123, -288, -96, 225, 0, 0, 200, 1], [123, 264, -96, 225, 270, 1, 200, 1]], [[100, 0, -108, 240, 0, 0, 100, 0], [123, -288, -96, 225, 0, 0, 200, 1], [123, 264, -96, 225, 270, 1, 200, 1]], [[100, 0, -108, 240, 0, 0, 80, 0], [123, -288, -96, 225, 0, 0, 200, 1], [123, 264, -96, 225, 270, 1, 200, 1]], [[100, 0, -108, 240, 0, 0, 60, 0], [123, -288, -96, 225, 0, 0, 150, 1], [123, 264, -96, 225, 270, 1, 150, 1], [101, -192, 12, 336, 0, 0, 255, 0], [101, 192, 12, 336, 0, 0, 255, 0]], [[100, 0, -108, 240, 0, 0, 40, 0], [123, -288, -96, 225, 0, 0, 100, 1], [123, 264, -96, 225, 270, 1, 100, 1], [102, -192, 12, 337, 0, 0, 255, 0], [102, 192, 12, 337, 0, 0, 255, 0]], [[100, 0, -108, 240, 0, 0, 20, 0], [123, -288, -96, 225, 0, 0, 50, 1], [123, 264, -96, 225, 270, 1, 50, 1], [103, -192, 12, 337, 0, 0, 255, 0], [103, 192, 12, 337, 0, 0, 255, 0]], [[104, -192, 12, 337, 0, 0, 255, 0], [104, 192, 12, 337, 0, 0, 255, 0]], [[105, -192, 12, 337, 0, 0, 255, 0], [105, 192, 12, 337, 0, 0, 255, 0]], [[106, -192, 12, 337, 0, 0, 255, 0], [106, 192, 12, 337, 0, 0, 255, 0]], [[107, -192, 12, 337, 0, 0, 255, 0], [107, 192, 12, 337, 0, 0, 255, 0]], [[108, -192, 12, 337, 0, 0, 255, 0], [108, 192, 12, 337, 0, 0, 255, 0]], [[109, -192, 12, 337, 0, 0, 255, 0], [109, 192, 12, 337, 0, 0, 255, 0]], [[110, -192, 12, 337, 0, 0, 255, 0], [110, 192, 12, 337, 0, 0, 255, 0]], [[111, -192, 12, 337, 0, 0, 255, 0], [111, 192, 12, 337, 0, 0, 255, 0]], [[112, -192, 12, 337, 0, 0, 255, 0], [112, 192, 12, 337, 0, 0, 255, 0]], [[113, -192, 12, 337, 0, 0, 255, 0], [113, 192, 12, 337, 0, 0, 255, 0]], [[114, -192, 12, 337, 0, 0, 255, 0], [114, 192, 12, 337, 0, 0, 255, 0]], [[115, -192, 12, 337, 0, 0, 255, 0], [115, 192, 12, 337, 0, 0, 255, 0]], [[116, -192, 12, 337, 0, 0, 255, 0], [116, 192, 12, 337, 0, 0, 255, 0]], [[117, -192, 12, 337, 0, 0, 255, 0], [117, 192, 12, 337, 0, 0, 255, 0]], [[118, -192, 12, 337, 0, 0, 255, 0], [118, 192, 12, 337, 0, 0, 255, 0]], [[119, -192, 12, 337, 0, 0, 255, 0], [119, 192, 12, 337, 0, 0, 255, 0]], [[120, -192, 12, 337, 0, 0, 255, 0], [120, 192, 12, 337, 0, 0, 255, 0]], [[120, -192, 12, 337, 0, 0, 255, 0], [120, 192, 12, 337, 0, 0, 255, 0]], [[121, -192, 12, 337, 0, 0, 255, 0], [121, 192, 12, 337, 0, 0, 255, 0]], [[122, -192, 12, 337, 0, 0, 255, 0], [122, 192, 12, 337, 0, 0, 255, 0]], [[122, -192, 12, 337, 0, 0, 150, 0], [122, 192, 12, 337, 0, 0, 150, 0]]], "name": "Ice All 3", "position": 3, "timings": [{"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 70, "volume": 65}}, {"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [136, 221, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": {"name": "Ice4", "pan": 0, "pitch": 100, "volume": 70}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 19, "se": {"name": "Crash", "pan": 0, "pitch": 150, "volume": 70}}, {"flashColor": [153, 221, 255, 255], "flashDuration": 5, "flashScope": 1, "frame": 23, "se": {"name": "Sand", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 187, 51, 204], "flashDuration": 5, "flashScope": 0, "frame": 23, "se": {"name": "Ice5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [136, 221, 255, 204], "flashDuration": 5, "flashScope": 2, "frame": 26, "se": {"name": "Ice1", "pan": 0, "pitch": 100, "volume": 75}}, {"flashColor": [153, 221, 255, 255], "flashDuration": 5, "flashScope": 1, "frame": 29, "se": null}, {"flashColor": [136, 221, 255, 170], "flashDuration": 5, "flashScope": 2, "frame": 32, "se": null}, {"flashColor": [153, 221, 255, 255], "flashDuration": 10, "flashScope": 1, "frame": 32, "se": null}]}, {"id": 76, "animation1Hue": 0, "animation1Name": "Thunder1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -312, 300, 0, 0, 255, 0]], [], [[1, 0, -220, 300, 0, 0, 255, 0], [4, 0, -220, 300, 0, 0, 255, 0]], [[2, 0, -220, 300, 0, 0, 255, 0], [5, 0, -220, 300, 0, 0, 255, 0]], [[3, 0, -220, 300, 0, 0, 255, 0], [6, 0, -220, 300, 0, 0, 255, 0]], [[1, 0, -220, 300, 0, 0, 255, 0], [7, 0, -220, 300, 0, 0, 255, 0]], [[-1, 0, -20, 0, 0, 0, 0, 0], [7, 0, -212, 320, 0, 0, 100, 0]]], "name": "Thunder One 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder6", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 187, 221], "flashDuration": 5, "flashScope": 2, "frame": 1, "se": null}, {"flashColor": [255, 255, 187, 221], "flashDuration": 5, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [255, 255, 187, 221], "flashDuration": 5, "flashScope": 1, "frame": 4, "se": null}]}, {"id": 77, "animation1Hue": 0, "animation1Name": "Thunder2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 300, 0, 0, 255, 1]], [[1, 0, 0, 225, 0, 0, 255, 1], [1, 0, 0, 300, 90, 0, 255, 1]], [[2, 0, 0, 300, 0, 0, 255, 1], [2, 0, 0, 150, 45, 1, 255, 1]], [[-1, 0, 0, 225, 90, 0, 255, 1], [3, 0, 0, 195, 0, 0, 255, 1]], [[4, 0, 0, 195, 0, 0, 255, 1]], [[5, 0, 0, 195, 0, 0, 255, 1]], [[3, 0, 0, 240, 0, 0, 255, 1]], [[4, 0, 0, 240, 0, 0, 255, 1]], [[5, 0, 0, 240, 0, 1, 255, 1]], [[3, 0, 0, 255, 0, 0, 150, 1]], [[4, 0, 0, 270, 0, 0, 100, 1]], [[3, 0, 0, 285, 0, 0, 60, 1]], [], [], []], "name": "Thunder One 2", "position": 1, "timings": [{"flashColor": [255, 255, 187, 153], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": {"name": "Thunder3", "pan": 0, "pitch": 85, "volume": 100}}, {"flashColor": [255, 255, 51, 170], "flashDuration": 3, "flashScope": 1, "frame": 3, "se": {"name": "Thunder8", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 51, 187], "flashDuration": 3, "flashScope": 1, "frame": 7, "se": null}]}, {"id": 78, "animation1Hue": 0, "animation1Name": "Thunder1", "animation2Hue": 0, "animation2Name": "", "frames": [[[8, 0, -20, 400, 0, 0, 128, 0]], [[9, 0, -21, 387, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[10, 0, -22, 373, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[11, 0, -24, 360, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[8, 0, -25, 347, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[9, 0, -26, 333, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[10, 0, -27, 320, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[11, 0, -28, 307, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[8, 0, -30, 293, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[9, 0, -31, 280, 0, 0, 182, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[10, 0, -32, 275, 0, 0, 100, 0], [0, -16.5, -92.5, 260, 0, 0, 180, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 20, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1], [-1, -75, 50, 100, 0, 0, 255, 1]], [[12, 0, -50, 280, 0, 1, 255, 0], [0, -280, -90.5, 260, 0, 0, 180, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [4, 0, -64, 380, 0, 0, 255, 0]], [[13, 0, -50, 280, 0, 1, 255, 0], [1, -256, -68, 260, 0, 0, 255, 0], [0, 289, -114.5, 260, 0, 0, 180, 0], [5, 0, -50, 380, 0, 0, 255, 0]], [[14, 0, -50, 280, 0, 1, 255, 0], [2, -256, -68, 260, 0, 0, 255, 0], [1, 248, -94, 230, 0, 1, 255, 0], [6, 0, -50, 380, 0, 0, 255, 0], [4, -275, 0, 200, 0, 0, 255, 0]], [[12, 0, -50, 280, 0, 1, 255, 0], [3, -256, -68, 260, 0, 0, 255, 0], [2, 248, -94, 230, 0, 1, 255, 0], [7, 0, -50, 380, 0, 0, 255, 0], [5, -275, 16, 200, 0, 0, 255, 0]], [[13, 0, -50, 280, 0, 1, 255, 0], [1, -256, -68, 260, 0, 0, 255, 0], [3, 248, -94, 230, 0, 1, 255, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [6, -275, 16, 200, 0, 0, 255, 0], [4, 260, -48, 200, 0, 0, 255, 0]], [[14, 0, -50, 280, 0, 1, 255, 0], [2, -256, -68, 260, 0, 0, 255, 0], [1, 248, -94, 230, 0, 1, 255, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [7, -275, 16, 200, 0, 0, 255, 0], [5, 260, -20, 200, 0, 0, 255, 0]], [[12, 0, -50, 280, 0, 1, 255, 0], [3, -256, -68, 260, 0, 0, 255, 0], [2, 248, -94, 230, 0, 1, 255, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [6, 260, -20, 200, 0, 0, 255, 0]], [[13, 0, -50, 280, 0, 1, 255, 0], [1, -256, -68, 260, 0, 0, 255, 0], [3, 248, -94, 230, 0, 1, 255, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [7, 260, -20, 200, 0, 0, 255, 0]], [[14, 0, -50, 280, 0, 1, 255, 0], [2, -256, -68, 260, 0, 0, 255, 0], [1, 248, -94, 230, 0, 1, 255, 0]], [[12, 0, -50, 280, 0, 1, 180, 0], [3, -256, -68, 260, 0, 0, 180, 0], [2, 248, -94, 230, 0, 1, 180, 0]], [[13, 0, -50, 280, 0, 1, 100, 0], [1, -256, -68, 260, 0, 0, 100, 0], [3, 248, -94, 230, 0, 1, 100, 0]]], "name": "Thunder All 1", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Paralyze1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 153, 255], "flashDuration": 10, "flashScope": 1, "frame": 9, "se": {"name": "Thunder6", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 153, 255], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": null}, {"flashColor": [255, 255, 153, 170], "flashDuration": 5, "flashScope": 2, "frame": 10, "se": null}, {"flashColor": [255, 255, 153, 255], "flashDuration": 5, "flashScope": 2, "frame": 14, "se": {"name": "Thunder5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 153, 255], "flashDuration": 5, "flashScope": 1, "frame": 15, "se": null}, {"flashColor": [255, 255, 153, 68], "flashDuration": 5, "flashScope": 2, "frame": 18, "se": null}]}, {"id": 79, "animation1Hue": 0, "animation1Name": "Thunder3", "animation2Hue": 0, "animation2Name": "Thunder4", "frames": [[], [[1, 0, -132, 315, 0, 0, 255, 1]], [[2, 0, -132, 315, 0, 0, 255, 1]], [[3, 0, -132, 315, 0, 0, 255, 1], [103, 288, -108, 225, 0, 0, 255, 1]], [[4, 0, -132, 315, 0, 0, 255, 1], [0, 0, -156, 375, 0, 0, 255, 1], [104, 288, -108, 225, 0, 0, 255, 1], [107, 288, -60, 225, 0, 0, 255, 1]], [[1, 0, -132, 315, 0, 0, 255, 1], [105, 288, -108, 225, 0, 0, 255, 1], [108, 288, -60, 225, 0, 0, 200, 1]], [[2, 0, -132, 315, 0, 0, 255, 1], [103, -252, -108, 225, 0, 0, 255, 1], [105, 288, -108, 225, 0, 0, 100, 1]], [[3, 0, -132, 315, 0, 0, 255, 1], [104, -252, -108, 225, 0, 0, 255, 1], [103, 192, -84, 150, 0, 0, 255, 1], [107, -264, -60, 225, 0, 0, 255, 1]], [[4, 0, -132, 315, 0, 0, 255, 1], [105, -252, -108, 225, 0, 0, 255, 1], [104, 192, -84, 150, 0, 0, 255, 1], [108, -264, -60, 225, 0, 0, 200, 1]], [[1, 0, -132, 315, 0, 0, 255, 1], [105, -252, -108, 225, 0, 0, 100, 1], [105, 192, -84, 150, 0, 0, 255, 1], [103, -180, -84, 150, 0, 0, 255, 1]], [[2, 0, -132, 315, 0, 0, 255, 1], [105, 192, -84, 150, 0, 0, 100, 1], [104, -180, -84, 150, 0, 0, 255, 1]], [[0, 0, -84, 375, 180, 1, 255, 1], [3, 0, -132, 315, 0, 0, 255, 1], [105, -180, -84, 150, 0, 0, 255, 1]], [[1, 0, -132, 315, 0, 0, 255, 1], [105, -180, -84, 150, 0, 0, 100, 1]], [[1, 0, -132, 315, 0, 0, 150, 1]]], "name": "Thunder All 2", "position": 3, "timings": [{"flashColor": [255, 255, 255, 238], "flashDuration": 3, "flashScope": 2, "frame": 0, "se": {"name": "Thunder10", "pan": 0, "pitch": 120, "volume": 100}}, {"flashColor": [255, 255, 204, 170], "flashDuration": 1, "flashScope": 1, "frame": 1, "se": {"name": "Thunder9", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 170, 170], "flashDuration": 3, "flashScope": 2, "frame": 4, "se": null}, {"flashColor": [255, 255, 221, 170], "flashDuration": 3, "flashScope": 1, "frame": 9, "se": null}, {"flashColor": [255, 255, 170, 170], "flashDuration": 3, "flashScope": 2, "frame": 9, "se": null}]}, {"id": 80, "animation1Hue": 230, "animation1Name": "PreSpecial1", "animation2Hue": 0, "animation2Name": "Thunder5", "frames": [[[0, 0, -72, 330, 0, 0, 255, 1]], [[1, 0, -72, 330, 0, 0, 255, 1]], [[2, 0, -72, 330, 0, 0, 255, 1]], [[3, 0, -72, 330, 0, 0, 255, 1]], [[4, 0, -72, 330, 0, 0, 255, 1], [17, 0, -72, 390, 0, 0, 150, 1]], [[5, 0, -72, 330, 0, 0, 255, 1], [18, 0, -72, 345, 0, 0, 255, 1]], [[6, 0, -72, 330, 0, 0, 255, 1], [19, 0, -72, 345, 0, 0, 255, 1]], [[20, 0, -72, 345, 0, 0, 255, 1], [7, 0, -72, 330, 0, 0, 255, 1]], [[8, 0, -72, 330, 0, 0, 255, 1], [22, 0, -84, 420, 0, 0, 255, 1]], [[9, 0, -72, 330, 0, 0, 255, 1], [23, 0, -84, 420, 0, 0, 255, 1]], [[10, 0, -72, 330, 0, 0, 255, 1], [24, 0, -84, 420, 0, 0, 255, 1]], [[11, 0, -72, 330, 0, 0, 255, 1], [25, 0, -84, 450, 0, 0, 255, 1], [100, 0, -96, 240, 0, 0, 50, 0]], [[12, 0, -72, 330, 0, 0, 255, 1], [20, 0, -84, 300, 0, 0, 200, 1], [22, 0, -84, 375, 0, 0, 255, 1], [26, 0, -84, 450, 0, 0, 255, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[13, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 465, 0, 0, 200, 1], [100, 0, -96, 240, 0, 0, 150, 0]], [[14, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 480, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 150, 0]], [[15, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 487, 0, 0, 150, 1], [100, 0, -96, 240, 0, 0, 150, 0], [120, 0, -216, 180, 0, 0, 255, 1]], [[16, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 495, 0, 0, 120, 1], [100, 0, -96, 240, 0, 0, 150, 0], [116, 0, -216, 210, 0, 0, 255, 1]], [[26, 0, -84, 502, 0, 0, 100, 1], [16, 0, -72, 330, 0, 0, 200, 1], [100, 0, -96, 240, 0, 0, 150, 0], [117, 0, -216, 225, 0, 0, 255, 1]], [[16, 0, -72, 330, 0, 0, 150, 1], [26, 0, -84, 502, 0, 0, 70, 1], [100, 0, -96, 240, 0, 0, 140, 0], [118, 0, -216, 300, 0, 0, 255, 1]], [[16, 0, -72, 330, 0, 0, 100, 1], [26, 0, -84, 502, 0, 0, 40, 1], [100, 0, -96, 240, 0, 0, 130, 0], [119, 0, -216, 330, 0, 0, 255, 1]], [[16, 0, -72, 330, 0, 0, 50, 1], [100, 0, -96, 240, 0, 0, 120, 0], [116, 0, -216, 330, 45, 0, 255, 1]], [[100, 0, -96, 240, 0, 0, 100, 0], [120, 0, -216, 330, 45, 0, 255, 1]], [[100, 0, -96, 240, 0, 0, 80, 0], [101, 0, 12, 337, 0, 0, 255, 1], [116, -264, -180, 255, 0, 0, 255, 1]], [[100, 0, -96, 240, 0, 0, 60, 0], [102, 0, 12, 337, 0, 0, 255, 1], [117, -264, -180, 270, 0, 0, 255, 1]], [[100, 0, -96, 240, 0, 0, 40, 0], [103, 0, 12, 337, 0, 0, 255, 1], [118, -264, -180, 270, 0, 0, 255, 1], [116, 276, -24, 255, 270, 0, 255, 1]], [[100, 0, -96, 240, 0, 0, 20, 0], [104, 0, 12, 337, 0, 0, 255, 1], [106, -228, 12, 337, 0, 0, 255, 1], [119, -264, -180, 270, 0, 0, 255, 1], [117, 276, -24, 285, 270, 0, 255, 1]], [[105, 0, 12, 337, 0, 0, 255, 1], [107, -228, 12, 337, 0, 0, 255, 1], [120, -264, -180, 270, 0, 0, 255, 1], [118, 276, -24, 285, 270, 0, 255, 1]], [[108, -228, 12, 337, 0, 0, 255, 1], [111, -96, 12, 337, 0, 1, 255, 1], [119, 276, -24, 285, 270, 0, 255, 1]], [[109, -228, 12, 337, 0, 0, 255, 1], [112, -96, 12, 337, 0, 1, 255, 1], [120, 276, -24, 285, 270, 0, 255, 1], [116, 144, -204, 255, 0, 0, 255, 1]], [[110, -228, 12, 337, 0, 0, 255, 1], [113, -96, 12, 337, 0, 1, 255, 1], [111, 96, 12, 337, 0, 0, 255, 1], [117, 144, -204, 255, 0, 0, 255, 1]], [[114, -96, 12, 337, 0, 1, 255, 1], [112, 96, 12, 337, 0, 0, 255, 1], [118, 144, -204, 255, 0, 0, 255, 1], [116, -300, -36, 255, 110, 0, 255, 1]], [[115, -96, 12, 337, 0, 1, 255, 1], [113, 96, 12, 337, 0, 0, 255, 1], [101, -156, 12, 337, 0, 1, 255, 1], [119, 144, -204, 255, 0, 0, 255, 1], [116, -300, -36, 285, 110, 0, 255, 1]], [[114, 96, 12, 337, 0, 0, 255, 1], [102, -156, 12, 337, 0, 1, 255, 1], [120, 144, -204, 255, 0, 0, 255, 1], [118, -300, -36, 285, 110, 0, 255, 1]], [[115, 96, 12, 337, 0, 0, 255, 1], [103, -156, 12, 337, 0, 1, 255, 1], [106, 240, 12, 337, 0, 1, 255, 1], [119, -300, -36, 285, 110, 0, 255, 1]], [[104, -156, 12, 337, 0, 1, 255, 1], [107, 240, 12, 337, 0, 1, 255, 1], [120, -300, -36, 285, 110, 0, 255, 1], [116, 0, 24, 255, 125, 0, 255, 1]], [[105, -156, 12, 337, 0, 1, 255, 1], [108, 240, 12, 337, 0, 1, 255, 1], [106, -204, 12, 337, 0, 0, 255, 1], [117, 0, 24, 270, 125, 0, 255, 1]], [[109, 240, 12, 337, 0, 1, 255, 1], [107, -204, 12, 337, 0, 0, 255, 1], [118, 0, 24, 270, 125, 0, 255, 1], [116, -252, -204, 255, 0, 0, 255, 1]], [[110, 240, 12, 337, 0, 1, 255, 1], [108, -204, 12, 337, 0, 0, 255, 1], [111, -204, 12, 337, 0, 1, 255, 1], [119, 0, 24, 270, 125, 0, 255, 1], [117, -252, -204, 270, 0, 0, 255, 1]], [[109, -204, 12, 337, 0, 0, 255, 1], [112, -204, 12, 337, 0, 1, 255, 1], [106, 252, 12, 337, 0, 0, 255, 1], [120, 0, 24, 270, 125, 0, 255, 1], [118, -252, -204, 270, 0, 0, 255, 1], [116, 204, -228, 255, 90, 0, 255, 1]], [[110, -204, 12, 337, 0, 0, 255, 1], [113, -204, 12, 337, 0, 1, 255, 1], [107, 252, 12, 337, 0, 0, 255, 1], [101, 0, 12, 337, 0, 0, 255, 1], [119, -252, -204, 270, 0, 0, 255, 1], [117, 204, -228, 255, 90, 0, 255, 1]], [[114, -204, 12, 337, 0, 1, 255, 1], [108, 252, 12, 337, 0, 0, 255, 1], [102, 0, 12, 337, 0, 0, 255, 1], [120, -252, -204, 270, 0, 0, 255, 1], [118, 204, -228, 255, 90, 0, 255, 1]], [[115, -204, 12, 337, 0, 1, 255, 1], [109, 252, 12, 337, 0, 0, 255, 1], [103, 0, 12, 337, 0, 0, 255, 1], [119, 204, -228, 255, 90, 0, 255, 1]], [[110, 252, 12, 337, 0, 0, 255, 1], [104, 0, 12, 337, 0, 0, 255, 1], [120, 204, -228, 255, 90, 0, 255, 1]], [[105, 0, 12, 337, 0, 0, 255, 1]]], "name": "Thunder All 3", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 70, "volume": 65}}, {"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [221, 102, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": null}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 10, "se": {"name": "Monster2", "pan": 0, "pitch": 100, "volume": 75}}, {"flashColor": [255, 221, 255, 221], "flashDuration": 5, "flashScope": 2, "frame": 21, "se": {"name": "Thunder9", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 221, 255, 221], "flashDuration": 5, "flashScope": 1, "frame": 22, "se": null}, {"flashColor": [255, 221, 255, 221], "flashDuration": 5, "flashScope": 1, "frame": 27, "se": null}, {"flashColor": [255, 221, 255, 221], "flashDuration": 5, "flashScope": 1, "frame": 29, "se": null}, {"flashColor": [255, 221, 255, 221], "flashDuration": 5, "flashScope": 2, "frame": 29, "se": null}, {"flashColor": [255, 221, 255, 221], "flashDuration": 5, "flashScope": 2, "frame": 33, "se": null}, {"flashColor": [255, 221, 255, 221], "flashDuration": 5, "flashScope": 1, "frame": 34, "se": null}]}, {"id": 81, "animation1Hue": 0, "animation1Name": "Water1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 250, 0, 0, 255, 1]], [[1, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[2, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[6, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, 0, 250, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[8, 0, 0, 250, 0, 0, 255, 1], [21, 0, 0, 220, 0, 0, 188, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[9, 0, 0, 250, 0, 0, 255, 1], [21, 0, 0, 280, 0, 0, 255, 1], [13, 0, 0, 280, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[10, 0, 0, 250, 0, 0, 255, 1], [21, 0, 0, 300, 0, 0, 180, 1], [22, 0, 0, 280, 0, 0, 255, 1], [14, 0, 0, 280, 0, 0, 255, 1]], [[22, 0, 0, 300, 0, 0, 255, 1], [15, 0, 0, 280, 0, 0, 255, 1]], [[22, 0, 0, 300, 0, 0, 0, 1], [16, 0, 0, 280, 0, 0, 255, 1]], [[22, 0, 0, 300, 0, 0, 0, 1], [17, 0, 0, 280, 0, 0, 255, 1]], [[22, 0, 0, 300, 0, 0, 0, 1], [18, 0, 0, 280, 0, 0, 255, 1]], [[22, 0, 0, 300, 0, 0, 0, 1], [19, 0, 0, 280, 0, 0, 100, 1]]], "name": "Water One 1", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Water5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Liquid", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [136, 255, 255, 153], "flashDuration": 5, "flashScope": 1, "frame": 9, "se": {"name": "Water2", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [136, 255, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 10, "se": null}]}, {"id": 82, "animation1Hue": 0, "animation1Name": "Water3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 90, 0, 0, 150, 1]], [[0, 0, 0, 120, 0, 0, 255, 1]], [[0, 0, 0, 150, 0, 0, 255, 1]], [[1, 0, 0, 150, 0, 0, 255, 1], [7, 0, 0, 225, 0, 0, 255, 1]], [[2, 0, 0, 150, 0, 0, 255, 1], [8, 0, 0, 225, 0, 0, 255, 1]], [[2, 0, 0, 157, 0, 0, 255, 1], [9, 0, 0, 225, 0, 0, 255, 1]], [[1, 0, 0, 157, 0, 0, 255, 1], [10, 0, 0, 225, 0, 0, 255, 1]], [[3, 0, 0, 300, 0, 0, 255, 1], [0, 0, 0, 225, 0, 0, 100, 1], [11, 0, 0, 255, 0, 0, 255, 1]], [[12, 0, 0, 270, 0, 0, 255, 1], [3, 0, 0, 315, 0, 0, 255, 1]], [[13, 0, 0, 270, 0, 0, 255, 1], [4, 0, 0, 150, 0, 0, 255, 1]], [[14, 0, 0, 270, 0, 0, 255, 1], [5, 0, 0, 195, 0, 0, 255, 1]], [[14, 0, 0, 277, 0, 0, 180, 1], [6, 0, 0, 199, 0, 0, 255, 1]], [[14, 0, 0, 282, 0, 0, 100, 1], [4, 0, 0, 204, 0, 0, 255, 1]], [[5, 0, 0, 207, 0, 0, 180, 1]], [[6, 0, 0, 210, 0, 0, 100, 1]]], "name": "Water One 2", "position": 1, "timings": [{"flashColor": [0, 204, 255, 153], "flashDuration": 10, "flashScope": 2, "frame": 0, "se": {"name": "Dive", "pan": 0, "pitch": 135, "volume": 90}}, {"flashColor": [119, 204, 255, 170], "flashDuration": 7, "flashScope": 1, "frame": 7, "se": {"name": "Water1", "pan": 0, "pitch": 100, "volume": 100}}]}, {"id": 83, "animation1Hue": 0, "animation1Name": "Water2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 500, 0, 0, 100, 1]], [[1, 0, 0, 482, 0, 0, 152, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[2, 0, 0, 464, 0, 0, 203, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, 0, 445, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 427, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 409, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[6, 0, 0, 391, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, 0, 373, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[8, 0, 0, 355, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[9, 0, 0, 336, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[10, 0, 0, 318, 0, 0, 200, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[11, 0, 0, 300, 0, 0, 128, 1]], [[12, 0, 0, 330, 0, 0, 255, 0], [17, 0, 192, 300, 0, 0, 100, 1]], [[13, 0, 0, 330, 0, 0, 255, 0], [18, 0, 128, 300, 0, 0, 200, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[14, 0, 0, 330, 0, 0, 255, 0], [19, 0, 64, 300, 0, 0, 255, 1], [12, -290, 0, 330, 0, 0, 255, 0], [17, -290, 92, 300, 0, 0, 100, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[15, 0, 0, 330, 0, 0, 255, 0], [20, 0, 0, 300, 0, 0, 255, 1], [13, -290, 0, 330, 0, 0, 255, 0], [18, -290, 28, 300, 0, 0, 200, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -120, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[16, 0, 2.5, 330, 0, 0, 255, 0], [21, 0, 0, 300, 0, 0, 255, 1], [14, -290, 0, 330, 0, 0, 255, 0], [19, -290, -64, 300, 0, 0, 255, 1], [12, 290, 0, 330, 0, 0, 255, 0], [17, 290, 72, 300, 0, 0, 100, 1]], [[14, 2.5, 0, 330, 0, 0, 255, 0], [22, 0, 0, 300, 0, 0, 255, 1], [15, -290, 0, 330, 0, 0, 255, 0], [20, -290, -100, 300, 0, 0, 255, 1], [13, 290, 0, 330, 0, 0, 255, 0], [18, 290, 8, 300, 0, 0, 200, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[15, 0, 0, 330, 0, 0, 255, 0], [23, 0, 0, 300, 0, 0, 255, 1], [16, -290, 0, 330, 0, 0, 255, 0], [21, -290, -100, 300, 0, 0, 255, 1], [14, 290, 0, 330, 0, 0, 255, 0], [19, 290, -56, 300, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[16, 0, 0, 330, 0, 0, 255, 0], [24, 0, 0, 300, 0, 0, 255, 1], [14, -290, 0, 330, 0, 0, 255, 0], [22, -290, -100, 300, 0, 0, 255, 1], [15, 290, 0, 330, 0, 0, 255, 0], [20, 290, -120, 300, 0, 0, 255, 1]], [[14, 0, 0, 330, 0, 0, 255, 0], [25, 2.5, 0, 300, 0, 0, 255, 1], [15, -290, 0, 330, 0, 0, 255, 0], [23, -290, -100, 300, 0, 0, 255, 1], [16, 290, 0, 330, 0, 0, 255, 0], [21, 290, -120, 300, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[15, 0, 0, 330, 0, 0, 255, 0], [26, 0, 0, 300, 0, 0, 255, 1], [16, -290, 0, 330, 0, 0, 255, 0], [24, -290, -100, 300, 0, 0, 255, 1], [14, 290, 0, 330, 0, 0, 255, 0], [22, 290, -120, 300, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[16, 0, 0, 330, 0, 0, 255, 0], [17, 0, 0, 300, 0, 0, 255, 1], [14, -290, 0, 330, 0, 0, 255, 0], [25, -290, -100, 300, 0, 0, 255, 1], [15, 290, 0, 330, 0, 0, 255, 0], [23, 290, -120, 300, 0, 0, 255, 1]], [[14, 0, 0, 330, 0, 0, 255, 0], [18, 0, 0, 300, 0, 0, 255, 1], [15, -290, 0, 330, 0, 0, 255, 0], [26, -290, -100, 300, 0, 0, 255, 1], [16, 290, 0, 330, 0, 0, 255, 0], [24, 290, -120, 300, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[15, 0, 0, 330, 0, 0, 200, 0], [19, 0, 0, 300, 0, 0, 200, 1], [16, -290, 0, 330, 0, 0, 200, 0], [17, -290, -100, 300, 0, 0, 200, 1], [14, 290, 0, 330, 0, 0, 200, 0], [25, 290, -120, 300, 0, 0, 200, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[16, 0, 0, 330, 0, 0, 128, 0], [20, 0, 0, 300, 0, 0, 128, 1], [14, -290, 0, 330, 0, 0, 128, 0], [18, -290, -100, 300, 0, 0, 128, 1], [15, 290, 0, 330, 0, 0, 128, 0], [26, 290, -120, 300, 0, 0, 128, 1]]], "name": "Water All 1", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Water5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Liquid", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [153, 204, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 11, "se": {"name": "Water4", "pan": 0, "pitch": 90, "volume": 90}}, {"flashColor": [153, 204, 255, 238], "flashDuration": 10, "flashScope": 1, "frame": 13, "se": {"name": "Water1", "pan": 0, "pitch": 100, "volume": 90}}]}, {"id": 84, "animation1Hue": 0, "animation1Name": "Water4", "animation2Hue": 0, "animation2Name": "", "frames": [[[3, -360, -96, 300, 0, 0, 100, 0], [3, 0, -96, 300, 0, 0, 100, 0], [3, 360, -96, 300, 0, 0, 100, 0]], [[0, -360, -96, 300, 0, 0, 150, 0], [0, 360, -96, 300, 0, 0, 150, 0], [0, 0, -96, 300, 0, 0, 150, 0]], [[1, -360, -96, 300, 0, 0, 200, 0], [1, 360, -96, 300, 0, 0, 200, 0], [1, 0, -96, 300, 0, 0, 200, 0]], [[2, -360, -96, 300, 0, 0, 200, 0], [2, 360, -96, 300, 0, 0, 200, 0], [2, 0, -96, 300, 0, 0, 200, 0]], [[3, -360, -96, 300, 0, 0, 200, 0], [3, 360, -96, 300, 0, 0, 200, 0], [3, 0, -96, 300, 0, 0, 200, 0]], [[0, -360, -96, 300, 0, 0, 200, 0], [0, 360, -96, 300, 0, 0, 200, 0], [0, 0, -96, 300, 0, 0, 200, 0]], [[1, -360, -96, 300, 0, 0, 200, 0], [1, 360, -96, 300, 0, 0, 200, 0], [1, 0, -96, 300, 0, 0, 200, 0]], [[2, -360, -96, 300, 0, 0, 200, 0], [2, 360, -96, 300, 0, 0, 200, 0], [2, 0, -96, 300, 0, 0, 200, 0]], [[3, -360, -96, 300, 0, 0, 200, 0], [3, 360, -96, 300, 0, 0, 200, 0], [3, 0, -96, 300, 0, 0, 200, 0]], [[0, -360, -96, 300, 0, 0, 200, 0], [0, 360, -96, 300, 0, 0, 200, 0], [0, 0, -96, 300, 0, 0, 200, 0]], [[1, -360, -96, 300, 0, 0, 200, 0], [1, 360, -96, 300, 0, 0, 200, 0], [1, 0, -96, 300, 0, 0, 200, 0]], [[2, -360, -96, 300, 0, 0, 200, 0], [2, 360, -96, 300, 0, 0, 200, 0], [2, 0, -96, 300, 0, 0, 200, 0]], [[3, -360, -96, 300, 0, 0, 200, 0], [3, 360, -96, 300, 0, 0, 200, 0], [3, 0, -96, 300, 0, 0, 200, 0]], [[0, -360, -96, 300, 0, 0, 180, 0], [0, 360, -96, 300, 0, 0, 180, 0], [0, 0, -96, 300, 0, 0, 180, 0]], [[1, -360, -96, 300, 0, 0, 120, 0], [1, 360, -96, 300, 0, 0, 120, 0], [1, 0, -96, 300, 0, 0, 120, 0]]], "name": "Water All 2", "position": 3, "timings": [{"flashColor": [221, 238, 238, 170], "flashDuration": 10, "flashScope": 2, "frame": 0, "se": {"name": "Water1", "pan": 0, "pitch": 50, "volume": 100}}, {"flashColor": [187, 255, 255, 255], "flashDuration": 5, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [187, 255, 255, 255], "flashDuration": 5, "flashScope": 1, "frame": 6, "se": null}, {"flashColor": [187, 255, 255, 255], "flashDuration": 5, "flashScope": 1, "frame": 9, "se": null}]}, {"id": 85, "animation1Hue": 180, "animation1Name": "PreSpecial1", "animation2Hue": 0, "animation2Name": "Water5", "frames": [[[0, 0, -72, 330, 0, 0, 255, 1]], [[1, 0, -72, 330, 0, 0, 255, 1]], [[2, 0, -72, 330, 0, 0, 255, 1]], [[3, 0, -72, 330, 0, 0, 255, 1]], [[4, 0, -72, 330, 0, 0, 255, 1], [17, 0, -72, 390, 0, 0, 150, 1]], [[5, 0, -72, 330, 0, 0, 255, 1], [18, 0, -72, 345, 0, 0, 255, 1]], [[6, 0, -72, 330, 0, 0, 255, 1], [19, 0, -72, 345, 0, 0, 255, 1]], [[20, 0, -72, 345, 0, 0, 255, 1], [7, 0, -72, 330, 0, 0, 255, 1]], [[8, 0, -72, 330, 0, 0, 255, 1], [22, 0, -84, 420, 0, 0, 255, 1]], [[9, 0, -72, 330, 0, 0, 255, 1], [23, 0, -84, 420, 0, 0, 255, 1]], [[10, 0, -72, 330, 0, 0, 255, 1], [24, 0, -84, 420, 0, 0, 255, 1]], [[11, 0, -72, 330, 0, 0, 255, 1], [25, 0, -84, 450, 0, 0, 255, 1], [100, 0, -96, 240, 0, 0, 50, 0]], [[12, 0, -72, 330, 0, 0, 255, 1], [20, 0, -84, 300, 0, 0, 200, 1], [22, 0, -84, 375, 0, 0, 255, 1], [26, 0, -84, 450, 0, 0, 255, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[13, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 465, 0, 0, 200, 1], [100, 0, -96, 240, 0, 0, 150, 0]], [[14, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 480, 0, 0, 180, 1], [100, 0, -96, 240, 0, 0, 150, 0]], [[15, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 487, 0, 0, 150, 1], [100, 0, -96, 240, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 495, 0, 0, 120, 1], [100, 0, -96, 240, 0, 0, 150, 0]], [[26, 0, -84, 502, 0, 0, 100, 1], [16, 0, -72, 330, 0, 0, 200, 1], [100, 0, -96, 240, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 150, 1], [26, 0, -84, 502, 0, 0, 70, 1], [100, 0, -96, 240, 0, 0, 140, 0]], [[16, 0, -72, 330, 0, 0, 100, 1], [26, 0, -84, 502, 0, 0, 40, 1], [100, 0, -96, 240, 0, 0, 130, 0]], [[16, 0, -72, 330, 0, 0, 50, 1], [100, 0, -96, 240, 0, 0, 120, 0]], [[-1, 0, -72, 330, 0, 0, 120, 1], [100, 0, -96, 240, 0, 0, 100, 0]], [[100, 0, -96, 240, 0, 0, 80, 0]], [[100, 0, -96, 240, 0, 0, 60, 0], [101, 0, 96, 427, 0, 0, 255, 0]], [[100, 0, -96, 240, 0, 0, 40, 0], [102, 0, 96, 427, 0, 0, 255, 0]], [[100, 0, -96, 240, 0, 0, 20, 0], [103, 0, 96, 427, 0, 0, 255, 0]], [[104, 0, 96, 427, 0, 0, 255, 0]], [[105, 0, 96, 427, 0, 0, 255, 0]], [[106, 0, 96, 427, 0, 0, 255, 0]], [[107, 0, 96, 427, 0, 0, 255, 0]], [[108, 0, 96, 427, 0, 0, 255, 0]], [[109, 0, 96, 427, 0, 0, 255, 0]], [[110, 0, 96, 427, 0, 0, 255, 0]], [[111, 0, 96, 427, 0, 0, 255, 0]], [[112, 0, 96, 427, 0, 0, 255, 0]], [[113, 0, 96, 427, 0, 0, 255, 0]], [[114, 0, 96, 427, 0, 0, 255, 0]], [[115, 0, 96, 427, 0, 0, 255, 0]], [[116, 0, 96, 427, 0, 0, 255, 0]], [[117, 0, 96, 427, 0, 0, 255, 0]], [[118, 0, 96, 427, 0, 0, 255, 0]], [[109, 0, 96, 427, 0, 0, 255, 0]], [[111, 0, 96, 427, 0, 0, 255, 0]], [[113, 0, 96, 427, 0, 0, 255, 0]], [[115, 0, 96, 427, 0, 0, 255, 0]], [[117, 0, 96, 427, 0, 0, 255, 0]], [[109, 0, 96, 427, 0, 0, 255, 0]], [[111, 0, 96, 427, 0, 0, 255, 0]], [[113, 0, 96, 427, 0, 0, 255, 0]], [[115, 0, 96, 427, 0, 0, 255, 0]], [[116, 0, 96, 427, 0, 0, 200, 0]], [[117, 0, 96, 427, 0, 0, 150, 0]], [[118, 0, 96, 427, 0, 0, 80, 0]]], "name": "Water All 3", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 70, "volume": 65}}, {"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [68, 170, 255, 153], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": {"name": "Wind1", "pan": 0, "pitch": 120, "volume": 75}}, {"flashColor": [170, 204, 255, 204], "flashDuration": 5, "flashScope": 2, "frame": 23, "se": {"name": "Water1", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [170, 204, 255, 204], "flashDuration": 5, "flashScope": 1, "frame": 24, "se": {"name": "Water2", "pan": 0, "pitch": 65, "volume": 90}}, {"flashColor": [170, 204, 255, 204], "flashDuration": 5, "flashScope": 1, "frame": 29, "se": null}, {"flashColor": [170, 204, 255, 204], "flashDuration": 5, "flashScope": 1, "frame": 34, "se": null}, {"flashColor": [170, 204, 255, 204], "flashDuration": 5, "flashScope": 1, "frame": 39, "se": null}, {"flashColor": [170, 204, 255, 204], "flashDuration": 5, "flashScope": 1, "frame": 44, "se": null}]}, {"id": 86, "animation1Hue": 0, "animation1Name": "Earth1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 350, 0, 0, 255, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 230, 0, 0, 0, 0]], [[1, 0, 0, 350, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [8, 0, 0, 230, 0, 0, 0, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[2, 0, 0, 350, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [8, 0, 0, 230, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, 0, 350, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [9, 0, 0, 230, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, 0, 350, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [10, 0, 0, 230, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, 0, 350, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [11, 0, 0, 230, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[6, 0, 0, 350, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [12, 0, 0, 230, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, 0, 350, 0, 0, 255, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [13, 0, 0, 230, 0, 0, 255, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [14, 0, 0, 230, 0, 0, 255, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [15, 0, 0, 230, 0, 0, 255, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [16, 0, 0, 230, 0, 0, 255, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [16, 0, 0, 230, 0, 0, 255, 0]], [[7, 0, -2.5, 350, 0, 0, 0, 0], [6, 0, 0, 300, 0, 0, 255, 0], [17, 0, 0, 250, 0, 0, 255, 0], [16, 0, 0, 230, 0, 0, 255, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [5, 0, 0, 300, 0, 0, 255, 0], [18, 0, 0, 250, 0, 0, 255, 0], [16, 0, 0, 207, 0, 0, 203, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, 0, 350, 0, 0, 0, 0], [4, 0, 0, 300, 0, 0, 255, 0], [19, 0, 0, 250, 0, 0, 255, 0], [-1, -22.5, 25, 183, 0, 0, 152, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, 0, 0, 350, 0, 0, 0, 0], [3, 0, 0, 300, 0, 0, 255, 0], [20, 0, 0, 250, 0, 0, 255, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [2, 0, 0, 300, 0, 0, 255, 0], [21, 0, 0, 250, 0, 0, 255, 0], [16, 0, 0, 360, 0, 0, 0, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [1, 0, 0, 300, 0, 0, 200, 0], [22, 0, 0, 260, 0, 0, 255, 0], [16, 0, 0, 360, 0, 0, 0, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [0, 0, 0, 300, 0, 0, 100, 0], [23, 0, 0, 270, 0, 0, 255, 0], [16, 0, 0, 360, 0, 0, 0, 0]], [[7, 0, 0, 350, 0, 0, 0, 0], [24, 0, 0, 280, 0, 0, 255, 0], [16, 0, 0, 360, 0, 0, 0, 0]]], "name": "Earth One 1", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Earth1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [204, 136, 68, 153], "flashDuration": 2, "flashScope": 1, "frame": 5, "se": {"name": "Break", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [204, 136, 68, 153], "flashDuration": 2, "flashScope": 1, "frame": 7, "se": null}, {"flashColor": [204, 136, 68, 153], "flashDuration": 4, "flashScope": 1, "frame": 9, "se": {"name": "Explosion1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [204, 136, 68, 153], "flashDuration": 5, "flashScope": 1, "frame": 12, "se": null}, {"flashColor": [221, 170, 102, 204], "flashDuration": 5, "flashScope": 2, "frame": 12, "se": null}]}, {"id": 87, "animation1Hue": 0, "animation1Name": "Earth3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 12, 96, 375, 0, 0, 255, 0]], [[0, 0, -144, 300, 0, 0, 255, 0]], [[0, 0, -108, 240, 0, 0, 255, 0], [1, 0, -108, 240, 0, 0, 100, 1]], [[1, 0, -72, 210, 0, 0, 255, 0]], [[1, 0, -24, 180, 0, 0, 255, 0]], [[2, 0, 24, 240, 0, 0, 255, 0]], [[3, 0, 24, 225, 0, 0, 255, 0]], [[4, 0, 24, 240, 0, 0, 255, 0]], [[5, 0, 24, 247, 0, 0, 255, 0]], [[6, 0, 24, 252, 0, 0, 180, 0]], [[6, 0, 24, 255, 0, 0, 100, 0]], []], "name": "Earth One 2", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder2", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 153, 0, 255], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": {"name": "Earth5", "pan": 0, "pitch": 50, "volume": 100}}, {"flashColor": [204, 153, 34, 187], "flashDuration": 3, "flashScope": 2, "frame": 6, "se": null}]}, {"id": 88, "animation1Hue": 0, "animation1Name": "Earth2", "animation2Hue": 0, "animation2Name": "", "frames": [[[13, -180, -90, 180, 0, 1, 100, 0], [-1, -355, 115, 180, 0, 1, 255, 0], [-1, -240, 187.5, 180, 0, 1, 0, 0], [13, 175, -115, 180, 0, 1, 100, 0], [-1, -25, 75, 180, 0, 1, 255, 0], [-1, 207.5, 95, 180, 0, 1, 0, 0], [13, -320, -10, 200, 0, 0, 100, 0], [-1, -408, 147.5, 200, 0, 0, 255, 0], [-1, -382.5, 130, 200, 0, 0, 0, 0], [13, 375, -10, 200, 0, 0, 100, 0], [-1, 97.5, 227.5, 200, 0, 0, 255, 0], [-1, 32.5, 167.5, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 100, 0], [-1, -145, 240, 220, 0, 0, 255, 0], [-1, -97.5, 200, 220, 0, 0, 0, 0], [-1, 0, 30, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[13, -180, -90, 180, 0, 1, 126, 0], [-1, -355, 115, 180, 0, 1, 255, 0], [-1, -240, 187.5, 180, 0, 1, 0, 0], [13, 175, -115, 180, 0, 1, 126, 0], [-1, -25, 75, 180, 0, 1, 255, 0], [-1, 207.5, 95, 180, 0, 1, 0, 0], [13, -320, -10, 200, 0, 0, 126, 0], [-1, -408, 147.5, 200, 0, 0, 255, 0], [-1, -382.5, 130, 200, 0, 0, 0, 0], [13, 375, -10, 200, 0, 0, 126, 0], [-1, 97.5, 227.5, 200, 0, 0, 255, 0], [-1, 32.5, 167.5, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 126, 0], [-1, -145, 240, 220, 0, 0, 255, 0], [-1, -97.5, 200, 220, 0, 0, 0, 0], [-1, 0, 30, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[13, -180, -90, 180, 0, 1, 152, 0], [-1, -355, 115, 180, 0, 1, 255, 0], [-1, -240, 187.5, 180, 0, 1, 0, 0], [13, 175, -115, 180, 0, 1, 152, 0], [-1, -25, 75, 180, 0, 1, 255, 0], [-1, 207.5, 95, 180, 0, 1, 0, 0], [13, -320, -10, 200, 0, 0, 152, 0], [-1, -408, 147.5, 200, 0, 0, 255, 0], [-1, -382.5, 130, 200, 0, 0, 0, 0], [13, 375, -10, 200, 0, 0, 152, 0], [-1, 97.5, 227.5, 200, 0, 0, 255, 0], [-1, 32.5, 167.5, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 152, 0], [-1, -145, 240, 220, 0, 0, 255, 0], [-1, -97.5, 200, 220, 0, 0, 0, 0], [-1, 0, 30, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[13, -180, -90, 180, 0, 1, 177, 0], [-1, -272.5, 105, 180, 0, 1, 255, 0], [-1, -272.5, 77.5, 180, 0, 1, 0, 0], [13, 175, -115, 180, 0, 1, 177, 0], [-1, 2.5, 62.5, 180, 0, 1, 255, 0], [-1, 7.5, 80, 180, 0, 1, 0, 0], [13, -320, -10, 200, 0, 0, 177, 0], [-1, -395, 142.5, 200, 0, 0, 255, 0], [-1, -390, 95, 200, 0, 0, 0, 0], [13, 375, -10, 200, 0, 0, 177, 0], [-1, 225, 162.5, 200, 0, 0, 255, 0], [-1, 232.5, 192.5, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 177, 0], [-1, -82.5, 207.5, 220, 0, 0, 255, 0], [-1, -102.5, 220, 220, 0, 0, 0, 0], [-1, 0, 30, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[13, -180, -90, 180, 0, 1, 203, 0], [-1, -372.5, 237.5, 180, 0, 1, 255, 0], [-1, -322.5, 292, 180, 0, 1, 0, 0], [13, 175, -115, 180, 0, 1, 203, 0], [-1, 7.5, 187.5, 180, 0, 1, 255, 0], [-1, -80, 265, 180, 0, 1, 0, 0], [13, -320, -10, 200, 0, 0, 203, 0], [-1, -408, 232.5, 200, 0, 0, 255, 0], [-1, -357.5, 155, 200, 0, 0, 0, 0], [13, 375, -10, 200, 0, 0, 203, 0], [-1, 177.5, 245, 200, 0, 0, 255, 0], [-1, 147.5, 260, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 203, 0], [-1, -115, 257.5, 220, 0, 0, 255, 0], [-1, -97.5, 230, 220, 0, 0, 0, 0], [-1, 0, 30, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[13, -180, -90, 180, 0, 1, 255, 0], [-1, -182.5, -90, 180, 0, 1, 255, 0], [-1, -182.5, -90, 180, 0, 1, 0, 0], [13, 175, -115, 180, 0, 1, 255, 0], [-1, 175, -115, 180, 0, 1, 255, 0], [-1, 177.5, -117.5, 180, 0, 1, 0, 0], [13, -320, -10, 200, 0, 0, 255, 0], [-1, -355, 45, 200, 0, 0, 255, 0], [-1, -395, 100, 200, 0, 0, 0, 0], [13, 375, -10, 200, 0, 0, 255, 0], [-1, 300, 72.5, 200, 0, 0, 255, 0], [-1, 262.5, 105, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 255, 0], [-1, -32.5, 87.5, 220, 0, 0, 255, 0], [-1, -50, 102.5, 220, 0, 0, 0, 0], [-1, 0, 30, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[16, -180, -90, 180, 0, 1, 255, 0], [9, -180, -90, 180, 0, 1, 255, 0], [2, -180, -90, 180, 0, 1, 255, 0], [13, 175, -115, 180, 0, 1, 255, 0], [7, 175, -115, 180, 0, 1, 255, 0], [2, 175, -115, 180, 0, 1, 0, 0], [13, -320, -10, 200, 0, 0, 255, 0], [7, -320, -10, 200, 0, 0, 255, 0], [2, -320, -10, 200, 0, 0, 0, 0], [13, 375, -10, 200, 0, 0, 255, 0], [7, 375, -10, 200, 0, 0, 255, 0], [2, 375, -10, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 255, 0], [7, 30, 25, 220, 0, 0, 255, 0], [2, 30, 25, 220, 0, 0, 0, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [10, -180, -90, 180, 0, 1, 255, 0], [3, -180, -90, 180, 0, 1, 255, 0], [16, 175, -115, 180, 0, 1, 255, 0], [9, 175, -115, 180, 0, 1, 255, 0], [2, 175, -115, 180, 0, 1, 255, 0], [13, -320, -10, 200, 0, 0, 255, 0], [7, -320, -10, 200, 0, 0, 255, 0], [2, -320, -10, 200, 0, 0, 0, 0], [13, 375, -10, 200, 0, 0, 255, 0], [7, 375, -10, 200, 0, 0, 255, 0], [2, 375, -10, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 255, 0], [7, 30, 25, 220, 0, 0, 255, 0], [2, 30, 25, 220, 0, 0, 0, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [11, -180, -90, 180, 0, 1, 255, 0], [4, -180, -90, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [10, 175, -115, 180, 0, 1, 255, 0], [3, 175, -115, 180, 0, 1, 255, 0], [16, -320, -10, 200, 0, 0, 255, 0], [9, -320, -10, 200, 0, 0, 255, 0], [2, -320, -10, 200, 0, 0, 255, 0], [13, 375, -10, 200, 0, 0, 255, 0], [7, 375, -10, 200, 0, 0, 255, 0], [2, 375, -10, 200, 0, 0, 0, 0], [13, 30, 25, 220, 0, 0, 255, 0], [7, 30, 25, 220, 0, 0, 255, 0], [2, 30, 25, 220, 0, 0, 0, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [12, -180, -90, 180, 0, 1, 255, 0], [5, -180, -90, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [11, 175, -115, 180, 0, 1, 255, 0], [4, 175, -115, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 255, 0], [10, -320, -10, 200, 0, 0, 255, 0], [3, -320, -10, 200, 0, 0, 255, 0], [16, 375, -10, 200, 0, 0, 255, 0], [9, 375, -10, 200, 0, 0, 255, 0], [2, 375, -10, 200, 0, 0, 255, 0], [13, 30, 25, 220, 0, 0, 255, 0], [7, 30, 25, 220, 0, 0, 255, 0], [2, 30, 25, 220, 0, 0, 0, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [12, -180, -90, 180, 0, 1, 0, 0], [6, -180, -90, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [12, 175, -115, 180, 0, 1, 255, 0], [5, 175, -115, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 255, 0], [11, -320, -10, 200, 0, 0, 255, 0], [4, -320, -10, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 255, 0], [10, 375, -10, 200, 0, 0, 255, 0], [3, 375, -10, 200, 0, 0, 255, 0], [16, 30, 25, 220, 0, 0, 255, 0], [9, 30, 25, 220, 0, 0, 255, 0], [2, 30, 25, 220, 0, 0, 255, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [12, -180, -90, 180, 0, 1, 0, 0], [6, -180, -90, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [12, 175, -115, 180, 0, 1, 0, 0], [6, 175, -115, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 255, 0], [12, -320, -10, 200, 0, 0, 255, 0], [5, -320, -10, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 255, 0], [11, 375, -10, 200, 0, 0, 255, 0], [4, 375, -10, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 255, 0], [10, 30, 25, 220, 0, 0, 255, 0], [3, 30, 25, 220, 0, 0, 255, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [12, -180, -90, 180, 0, 1, 0, 0], [6, -180, -90, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [12, 175, -115, 180, 0, 1, 0, 0], [6, 175, -115, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 255, 0], [12, -320, -10, 200, 0, 0, 0, 0], [6, -320, -10, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 255, 0], [12, 375, -10, 200, 0, 0, 255, 0], [5, 375, -10, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 255, 0], [11, 30, 25, 220, 0, 0, 255, 0], [4, 30, 25, 220, 0, 0, 255, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [12, -180, -90, 180, 0, 1, 0, 0], [6, -180, -90, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [12, 175, -115, 180, 0, 1, 0, 0], [6, 175, -115, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 255, 0], [12, -320, -10, 200, 0, 0, 0, 0], [6, -320, -10, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 255, 0], [12, 375, -10, 200, 0, 0, 0, 0], [6, 375, -10, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 255, 0], [12, 30, 25, 220, 0, 0, 255, 0], [5, 30, 25, 220, 0, 0, 255, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [12, -180, -90, 180, 0, 1, 0, 0], [6, -180, -90, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [12, 175, -115, 180, 0, 1, 0, 0], [6, 175, -115, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 255, 0], [12, -320, -10, 200, 0, 0, 0, 0], [6, -320, -10, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 255, 0], [12, 375, -10, 200, 0, 0, 0, 0], [6, 375, -10, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 255, 0], [12, 30, 25, 220, 0, 0, 0, 0], [6, 30, 25, 220, 0, 0, 255, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [12, -180, -90, 180, 0, 1, 0, 0], [-1, -282.5, 32.5, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [12, 175, -115, 180, 0, 1, 0, 0], [-1, 15, -30, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 255, 0], [12, -320, -10, 200, 0, 0, 0, 0], [-1, -407.5, 67.5, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 255, 0], [12, 375, -10, 200, 0, 0, 0, 0], [-1, 265, 155, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 255, 0], [12, 30, 25, 220, 0, 0, 0, 0]], [[17, -180, -90, 180, 0, 1, 255, 0], [12, -180, -90, 180, 0, 1, 0, 0], [-1, -282.5, 32.5, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 255, 0], [12, 175, -115, 180, 0, 1, 0, 0], [-1, 15, -30, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 255, 0], [12, -320, -10, 200, 0, 0, 0, 0], [-1, -407.5, 67.5, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 255, 0], [12, 375, -10, 200, 0, 0, 0, 0], [-1, 265, 155, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 255, 0], [12, 30, 25, 220, 0, 0, 0, 0]], [[17, -180, -90, 180, 0, 1, 203, 0], [12, -180, -90, 180, 0, 1, 33, 0], [-1, -282.5, 32.5, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 203, 0], [12, 175, -115, 180, 0, 1, 33, 0], [-1, 15, -30, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 203, 0], [12, -320, -10, 200, 0, 0, 33, 0], [-1, -407.5, 67.5, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 203, 0], [12, 375, -10, 200, 0, 0, 33, 0], [-1, 265, 155, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 203, 0], [12, 30, 25, 220, 0, 0, 33, 0], [-1, 0, 30, 100, 0, 0, 255, 1], [-1, 0, 30, 100, 0, 0, 255, 1]], [[17, -180, -90, 180, 0, 1, 152, 0], [12, -180, -90, 180, 0, 1, 67, 0], [-1, -282.5, 32.5, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 152, 0], [12, 175, -115, 180, 0, 1, 67, 0], [-1, 15, -30, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 152, 0], [12, -320, -10, 200, 0, 0, 67, 0], [-1, -407.5, 67.5, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 152, 0], [12, 375, -10, 200, 0, 0, 67, 0], [-1, 265, 155, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 152, 0], [12, 30, 25, 220, 0, 0, 67, 0], [-1, 0, 30, 100, 0, 0, 255, 1], [-1, 0, 30, 100, 0, 0, 255, 1]], [[17, -180, -90, 180, 0, 1, 100, 0], [12, -180, -90, 180, 0, 1, 100, 0], [-1, -282.5, 32.5, 180, 0, 1, 255, 0], [17, 175, -115, 180, 0, 1, 100, 0], [12, 175, -115, 180, 0, 1, 100, 0], [-1, 15, -30, 180, 0, 1, 255, 0], [17, -320, -10, 200, 0, 0, 100, 0], [12, -320, -10, 200, 0, 0, 100, 0], [-1, -407.5, 67.5, 200, 0, 0, 255, 0], [17, 375, -10, 200, 0, 0, 100, 0], [12, 375, -10, 200, 0, 0, 100, 0], [-1, 265, 155, 200, 0, 0, 255, 0], [17, 30, 25, 220, 0, 0, 100, 0], [12, 30, 25, 220, 0, 0, 100, 0]]], "name": "Earth All 1", "position": 3, "timings": [{"flashColor": [255, 221, 170, 170], "flashDuration": 3, "flashScope": 1, "frame": 5, "se": {"name": "Earth1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 221, 170, 170], "flashDuration": 5, "flashScope": 2, "frame": 5, "se": {"name": "Fire3", "pan": 0, "pitch": 50, "volume": 100}}, {"flashColor": [255, 221, 170, 170], "flashDuration": 3, "flashScope": 1, "frame": 7, "se": {"name": "Earth1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 221, 170, 170], "flashDuration": 3, "flashScope": 1, "frame": 9, "se": {"name": "Explosion1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 10, "se": {"name": "Earth1", "pan": 0, "pitch": 80, "volume": 90}}]}, {"id": 89, "animation1Hue": 0, "animation1Name": "Earth4", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -144, 300, 0, 0, 255, 0]], [[0, -228, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 1], [7, -204, -24, 225, 0, 0, 255, 0], [7, 300, 48, 120, 0, 0, 255, 0]], [[0, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 0], [7, -204, -144, 225, 0, 0, 255, 0], [7, 300, -60, 120, 0, 0, 255, 0], [7, 408, -48, 225, 0, 0, 255, 0]], [[1, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [1, 252, -144, 300, 0, 0, 255, 1], [1, -228, -144, 300, 0, 0, 255, 1], [1, 0, -144, 300, 0, 0, 255, 0], [7, -204, -156, 225, 0, 0, 255, 0], [7, 300, -84, 120, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 1], [2, -12, -72, 225, 0, 0, 255, 0], [7, 408, -72, 225, 0, 0, 255, 0]], [[1, -228, -144, 300, 0, 0, 255, 0], [1, 252, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 0], [7, -204, -168, 225, 0, 0, 180, 0], [7, 300, -96, 120, 0, 0, 255, 0], [3, -12, -72, 225, 0, 0, 255, 0], [7, 408, -84, 225, 0, 0, 255, 0], [-1, 0, -96, 30, 0, 0, 0, 0], [-1, 0, -96, 30, 0, 0, 0, 0], [-1, 0, -96, 30, 0, 0, 0, 0], [-1, 0, -96, 30, 0, 0, 0, 0], [-1, 0, -96, 30, 0, 0, 0, 0], [-1, 0, -96, 30, 0, 0, 0, 0], [-1, 0, -96, 30, 0, 0, 0, 0], [-1, 0, -96, 30, 0, 0, 0, 0], [-1, 0, -96, 30, 0, 0, 0, 0]], [[1, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [7, 300, -108, 120, 0, 0, 255, 0], [1, 252, -144, 300, 0, 0, 255, 1], [1, 0, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 1], [1, 0, -144, 300, 0, 0, 255, 1], [4, -12, -72, 225, 0, 0, 255, 0], [4, -12, -72, 225, 0, 0, 200, 1], [2, -324, -48, 195, 0, 0, 255, 0], [7, 168, 72, 225, 0, 0, 255, 0], [7, 408, -96, 225, 0, 0, 150, 0], [2, -324, -48, 195, 0, 0, 200, 1]], [[1, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 0], [5, -12, -72, 225, 0, 0, 255, 0], [3, -324, -48, 195, 0, 0, 255, 0], [7, 168, -24, 225, 0, 0, 255, 0], [7, -408, 0, 225, 0, 0, 255, 0]], [[1, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 1], [1, 252, -144, 300, 0, 0, 255, 1], [1, 0, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 1], [6, -12, -72, 225, 0, 0, 255, 0], [4, -324, -48, 195, 0, 0, 255, 0], [4, -324, -48, 195, 0, 0, 100, 1], [2, 300, -48, 195, 0, 0, 255, 0], [2, 300, -48, 195, 0, 0, 100, 1], [7, 168, -48, 225, 0, 0, 255, 0], [7, -252, 0, 120, 0, 0, 255, 0], [7, -408, -96, 225, 0, 0, 255, 0], [6, -12, -72, 225, 0, 0, 100, 1]], [[1, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 0], [6, -12, -72, 225, 0, 0, 255, 0], [5, -324, -48, 195, 0, 0, 255, 0], [3, 300, -48, 195, 0, 0, 255, 0], [7, 168, -60, 225, 0, 0, 255, 0], [7, -252, -60, 120, 0, 0, 255, 0], [7, -408, -108, 225, 0, 0, 255, 0], [7, 408, 36, 150, 0, 0, 255, 0]], [[1, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 1], [1, 252, -144, 300, 0, 0, 255, 1], [1, 0, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 1], [6, -12, -72, 225, 0, 0, 255, 0], [6, -324, -48, 195, 0, 0, 255, 0], [6, -324, -48, 195, 0, 0, 100, 1], [6, -12, -72, 225, 0, 0, 100, 1], [4, 300, -48, 195, 0, 0, 255, 0], [4, 300, -48, 195, 0, 0, 100, 1], [7, 168, -72, 225, 0, 0, 150, 0], [7, -252, -84, 120, 0, 0, 255, 0], [7, -408, -120, 225, 0, 0, 150, 0], [7, 408, 0, 150, 0, 0, 255, 0]], [[1, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 0], [6, -12, -72, 225, 0, 0, 180, 0], [6, -324, -48, 195, 0, 0, 255, 0], [5, 300, -48, 195, 0, 0, 255, 0], [7, -252, -96, 120, 0, 0, 255, 0], [7, 408, -12, 150, 0, 0, 255, 0]], [[1, -228, -144, 300, 0, 0, 255, 0], [1, 252, -144, 300, 0, 0, 255, 0], [1, 252, -144, 300, 0, 0, 200, 1], [1, -228, -144, 300, 0, 0, 200, 1], [1, 0, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 200, 1], [6, -12, -72, 225, 0, 0, 100, 0], [6, -324, -48, 195, 0, 0, 255, 0], [6, 300, -48, 195, 0, 0, 255, 0], [7, -252, -108, 120, 0, 0, 255, 0], [7, 408, -24, 150, 0, 0, 150, 0]], [[1, -228, -144, 300, 0, 0, 255, 0], [1, 252, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 0], [6, -324, -48, 195, 0, 0, 180, 0], [6, 300, -48, 195, 0, 0, 255, 0], [7, -252, -120, 120, 0, 0, 150, 0]], [[1, -228, -144, 300, 0, 0, 255, 0], [1, 252, -144, 300, 0, 0, 255, 0], [1, 252, -144, 300, 0, 0, 150, 1], [1, -228, -144, 300, 0, 0, 150, 1], [1, 0, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 150, 1], [6, -324, -48, 195, 0, 0, 100, 0], [6, 300, -48, 195, 0, 0, 255, 0]], [[1, 252, -144, 300, 0, 0, 255, 0], [1, -228, -144, 300, 0, 0, 255, 0], [1, 0, -144, 300, 0, 0, 255, 0], [6, 300, -48, 195, 0, 0, 180, 0]], [[6, 300, -48, 195, 0, 0, 100, 0], [1, 252, -144, 300, 0, 0, 150, 0], [1, -228, -144, 300, 0, 0, 150, 0], [1, 0, -144, 300, 0, 0, 150, 0]], [[6, 300, -48, 195, 0, 0, 80, 0], [1, 252, -144, 300, 0, 0, 80, 0], [1, -228, -144, 300, 0, 0, 80, 0], [1, 0, -144, 300, 0, 0, 80, 0]]], "name": "Earth All 2", "position": 3, "timings": [{"flashColor": [221, 102, 0, 170], "flashDuration": 3, "flashScope": 2, "frame": 0, "se": {"name": "Earth4", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 204, 68, 119], "flashDuration": 1, "flashScope": 1, "frame": 1, "se": {"name": "Earth1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [204, 136, 34, 221], "flashDuration": 5, "flashScope": 2, "frame": 3, "se": {"name": "Earth5", "pan": 0, "pitch": 100, "volume": 100}}, {"conditions": 1, "flashColor": [255, 170, 51, 102], "flashDuration": 2, "flashScope": 1, "frame": 3, "se": null}, {"flashColor": [255, 170, 34, 119], "flashDuration": 2, "flashScope": 1, "frame": 5, "se": null}, {"conditions": 1, "flashColor": [255, 170, 136, 119], "flashDuration": 2, "flashScope": 1, "frame": 7, "se": null}, {"conditions": 1, "flashColor": [255, 170, 136, 85], "flashDuration": 5, "flashScope": 1, "frame": 10, "se": null}]}, {"id": 90, "animation1Hue": 350, "animation1Name": "PreSpecial1", "animation2Hue": 0, "animation2Name": "Earth5", "frames": [[[0, 0, -72, 330, 0, 0, 255, 1]], [[1, 0, -72, 330, 0, 0, 255, 1]], [[2, 0, -72, 330, 0, 0, 255, 1]], [[3, 0, -72, 330, 0, 0, 255, 1]], [[4, 0, -72, 330, 0, 0, 255, 1], [17, 0, -72, 390, 0, 0, 150, 1]], [[5, 0, -72, 330, 0, 0, 255, 1], [18, 0, -72, 345, 0, 0, 255, 1]], [[6, 0, -72, 330, 0, 0, 255, 1], [19, 0, -72, 345, 0, 0, 255, 1]], [[20, 0, -72, 345, 0, 0, 255, 1], [7, 0, -72, 330, 0, 0, 255, 1]], [[8, 0, -72, 330, 0, 0, 255, 1], [22, 0, -84, 420, 0, 0, 255, 1]], [[9, 0, -72, 330, 0, 0, 255, 1], [23, 0, -84, 420, 0, 0, 255, 1]], [[10, 0, -72, 330, 0, 0, 255, 1], [24, 0, -84, 420, 0, 0, 255, 1]], [[11, 0, -72, 330, 0, 0, 255, 1], [25, 0, -84, 450, 0, 0, 255, 1], [100, 24, -84, 300, 0, 0, 50, 0]], [[12, 0, -72, 330, 0, 0, 255, 1], [20, 0, -84, 300, 0, 0, 200, 1], [22, 0, -84, 375, 0, 0, 255, 1], [26, 0, -84, 450, 0, 0, 255, 1], [100, 24, -84, 300, 0, 0, 100, 0]], [[13, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 465, 0, 0, 200, 1], [100, 24, -84, 300, 0, 0, 150, 0]], [[14, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 480, 0, 0, 180, 1], [100, 24, -84, 300, 0, 0, 150, 0]], [[15, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 487, 0, 0, 150, 1], [100, 24, -84, 300, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 495, 0, 0, 120, 1], [100, 24, -84, 300, 0, 0, 150, 0]], [[26, 0, -84, 502, 0, 0, 100, 1], [16, 0, -72, 330, 0, 0, 200, 1], [100, 24, -84, 300, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 150, 1], [26, 0, -84, 502, 0, 0, 70, 1], [100, 24, -84, 300, 0, 0, 140, 0]], [[16, 0, -72, 330, 0, 0, 100, 1], [26, 0, -84, 502, 0, 0, 40, 1], [100, 24, -84, 300, 0, 0, 130, 0]], [[16, 0, -72, 330, 0, 0, 50, 1], [100, 24, -84, 300, 0, 0, 120, 0]], [[-1, 0, -72, 330, 0, 0, 120, 1], [100, 24, -84, 300, 0, 0, 100, 0]], [[100, 24, -84, 300, 0, 0, 80, 0]], [[100, 24, -84, 300, 0, 0, 60, 0]], [[100, 24, -84, 300, 0, 0, 40, 0]], [[100, 24, -84, 300, 0, 0, 20, 0], [101, 0, -252, 405, 0, 0, 255, 0]], [[101, 0, -72, 405, 0, 0, 255, 0]], [[102, 0, 12, 405, 0, 0, 255, 0]], [[103, 0, 12, 405, 0, 0, 255, 0]], [[104, 0, 12, 405, 0, 0, 255, 0], [121, -192, -48, 277, 0, 0, 255, 0]], [[105, 0, 12, 405, 0, 0, 255, 0], [122, -192, -48, 277, 0, 0, 255, 0]], [[106, 0, 12, 405, 0, 0, 255, 0], [123, -192, -48, 277, 0, 0, 255, 0], [121, 276, -48, 277, 0, 0, 255, 0]], [[107, 0, 12, 405, 0, 0, 255, 0], [124, -192, -48, 277, 0, 0, 255, 0], [122, 276, -48, 277, 0, 0, 255, 0]], [[108, 0, 12, 405, 0, 0, 255, 0], [125, -192, -48, 277, 0, 0, 255, 0], [123, 276, -48, 277, 0, 0, 255, 0]], [[109, 0, 12, 405, 0, 0, 255, 0], [126, -192, -48, 277, 0, 0, 255, 0], [124, 276, -48, 277, 0, 0, 255, 0]], [[110, 0, 12, 405, 0, 0, 255, 0], [127, -192, -48, 277, 0, 0, 255, 0], [125, 276, -48, 277, 0, 0, 255, 0]], [[111, 0, 12, 405, 0, 0, 255, 0], [128, -192, -48, 277, 0, 0, 255, 0], [126, 276, -48, 277, 0, 0, 255, 0]], [[112, 0, 12, 405, 0, 0, 255, 0], [129, -192, -48, 277, 0, 0, 255, 0], [127, 276, -48, 277, 0, 0, 255, 0]], [[113, 0, 12, 405, 0, 0, 255, 0], [128, 276, -48, 277, 0, 0, 255, 0]], [[114, 0, 12, 405, 0, 0, 255, 0], [129, 276, -48, 277, 0, 0, 255, 0]], [[115, 0, 12, 405, 0, 0, 255, 0]], [[116, 0, 12, 405, 0, 0, 255, 0]], [[117, 0, 12, 405, 0, 0, 255, 0]], [[118, 0, 12, 405, 0, 0, 255, 0]], [[119, 0, 12, 405, 0, 0, 255, 0]], [[120, 0, 12, 405, 0, 0, 255, 0]], [[101, 0, 12, 405, 0, 0, 255, 0]], [[101, 0, 12, 405, 0, 0, 200, 0]], [[101, 0, 12, 405, 0, 0, 150, 0]], [[101, 0, 12, 405, 0, 0, 100, 0]]], "name": "Earth All 3", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 70, "volume": 65}}, {"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [238, 136, 0, 153], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": {"name": "Earth4", "pan": 0, "pitch": 125, "volume": 75}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 26, "se": {"name": "Damage4", "pan": 0, "pitch": 50, "volume": 100}}, {"flashColor": [238, 187, 0, 187], "flashDuration": 5, "flashScope": 1, "frame": 27, "se": {"name": "Break", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [238, 187, 0, 187], "flashDuration": 5, "flashScope": 2, "frame": 27, "se": {"name": "Earth1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [238, 187, 0, 204], "flashDuration": 5, "flashScope": 1, "frame": 33, "se": null}, {"flashColor": [238, 187, 0, 204], "flashDuration": 5, "flashScope": 1, "frame": 37, "se": null}]}, {"id": 91, "animation1Hue": 0, "animation1Name": "Wind1", "animation2Hue": 0, "animation2Name": "", "frames": [[[-1, 0, -150, 0, 0, 0, 0, 0], [0, 0, -180, 260, 0, 0, 180, 0]], [[-1, 0, -150, 0, 0, 0, 0, 0], [1, 0, -180, 260, 0, 0, 180, 0]], [[-1, 0, -150, 0, 0, 0, 0, 0], [2, 0, -180, 260, 0, 0, 180, 0], [15, 0, -200, 240, 0, 0, 180, 0]], [[9, 0, -180, 230, 0, 0, 180, 0], [3, 0, -180, 260, 0, 0, 180, 0], [16, 0, -200, 240, 0, 0, 255, 1]], [[10, 0, -180, 230, 0, 0, 180, 0], [4, 0, -180, 260, 0, 0, 180, 0], [17, 0, -200, 240, 0, 0, 255, 1]], [[11, 2.5, -180, 230, 0, 0, 180, 0], [5, 0, -180, 260, 0, 0, 180, 0], [18, 0, -200, 240, 0, 0, 255, 1]], [[12, 0, -180, 230, 0, 0, 180, 0], [6, 0, -180, 260, 0, 0, 180, 0], [19, 0, -200, 240, 0, 0, 255, 1]], [[13, 0, -180, 230, 0, 0, 180, 0], [7, 0, -180, 260, 0, 0, 180, 0], [20, 0, -200, 240, 0, 0, 255, 1]], [[14, 0, -180, 230, 0, 0, 180, 0], [8, 0, -180, 260, 0, 0, 180, 0], [21, 0, -200, 240, 0, 0, 255, 1]], [[12, 0, -180, 230, 0, 0, 180, 0], [6, 0, -180, 260, 0, 0, 180, 0], [19, 0, -200, 240, 0, 0, 255, 1]], [[13, 0, -180, 230, 0, 0, 180, 0], [7, 0, -180, 260, 0, 0, 180, 0], [20, 0, -200, 240, 0, 0, 255, 1]], [[14, 0, -180, 230, 0, 0, 180, 0], [8, 0, -180, 260, 0, 0, 180, 0], [21, 0, -200, 240, 0, 0, 255, 1]], [[12, 0, -180, 230, 0, 0, 180, 0], [6, 0, -180, 260, 0, 0, 180, 0], [22, 0, -200, 240, 0, 0, 255, 1]], [[7, 0, -180, 260, 0, 0, 180, 0], [13, 0, -180, 230, 0, 0, 180, 0], [23, 0, -198, 240, 0, 0, 255, 1]], [[14, 0, -180, 230, 0, 0, 180, 0], [8, 0, -180, 260, 0, 0, 180, 0], [24, 0, -198, 240, 0, 0, 255, 1]]], "name": "Wind One 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 221, 187, 187], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [255, 221, 187, 119], "flashDuration": 5, "flashScope": 1, "frame": 8, "se": null}, {"flashColor": [255, 221, 187, 153], "flashDuration": 7, "flashScope": 2, "frame": 4, "se": null}]}, {"id": 92, "animation1Hue": 0, "animation1Name": "Wind3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 375, 0, 0, 255, 1]], [[1, 0, 0, 375, 0, 0, 255, 1]], [[2, -12, 0, 345, 0, 0, 255, 1], [1, 0, 0, 375, 0, 0, 130, 1]], [[3, -12, 0, 300, 0, 0, 255, 1], [8, 0, 12, 225, 0, 0, 255, 1]], [[4, -12, 0, 375, 0, 0, 255, 1], [9, 0, 12, 375, 0, 0, 255, 1]], [[5, -12, 0, 375, 0, 0, 255, 1], [10, 0, 12, 390, 0, 0, 255, 1]], [[6, -12, 0, 375, 0, 0, 255, 1], [11, 0, 12, 390, 0, 0, 255, 1]], [[7, -12, 0, 375, 0, 0, 255, 1], [12, 0, 12, 390, 0, 0, 255, 1]], [[13, 0, 12, 390, 0, 0, 255, 1]], [[13, 0, 12, 405, 0, 0, 200, 1]], [[13, 0, 12, 420, 0, 0, 150, 1]]], "name": "Wind One 2", "position": 1, "timings": [{"flashColor": [204, 221, 102, 187], "flashDuration": 3, "flashScope": 0, "frame": 0, "se": {"name": "Wind5", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [204, 221, 119, 204], "flashDuration": 3, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [204, 221, 102, 170], "flashDuration": 3, "flashScope": 2, "frame": 3, "se": null}]}, {"id": 93, "animation1Hue": 0, "animation1Name": "Wind2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, -47.5, -160, 400, -10, 0, 150, 1], [0, 50, 0, 340, 0, 1, 0, 1]], [[0, 0, 0, 360, 20, 0, 255, 1], [0, 50, 0, 340, 0, 1, 0, 1]], [[1, 0, 0, 300, 20, 0, 255, 1], [0, 50, 0, 340, 0, 1, 0, 1]], [[2, 0, 0, 300, 20, 0, 255, 1], [0, 50, 0, 340, 0, 1, 255, 1], [13, 408, 248, 100, 0, 0, 0, 1], [13, 408, 280, 100, 0, 0, 0, 1], [13, 408, 232.5, 100, 0, 0, 0, 1], [13, -135, -175, 260, 90, 0, 255, 0], [13, 408, 203, 260, 0, 0, 0, 0], [13, 408, 203, 260, 0, 0, 0, 0], [13, 408, 0, 260, 0, 0, 0, 0], [13, 408, 203, 260, 0, 0, 0, 0], [8, -135, -175, 260, 90, 0, 180, 0]], [[3, 0, 0, 300, 20, 0, 255, 1], [1, 50, 0, 340, 0, 1, 255, 1], [0, -250, -150, 380, 0, 0, 255, 1], [14, 408, 310.5, 100, 0, 0, 0, 1], [14, 408, 215, 100, 0, 0, 0, 1], [14, -135, -175, 260, 90, 0, 255, 0], [13, 290, -25, 260, 50, 0, 255, 0], [14, 408, 245, 260, 0, 0, 0, 0], [14, 408, 0, 260, 0, 0, 0, 0], [14, 408, 245, 260, 0, 0, 0, 0], [9, -135, -175, 260, 90, 0, 180, 0], [8, 290, -25, 260, 50, 0, 180, 0]], [[4, 0, 0, 300, 20, 0, 255, 1], [2, 50, 0, 340, 0, 1, 255, 1], [1, -250, -150, 380, 0, 0, 255, 1], [14, 408, 293, 100, 0, 0, 0, 1], [14, 408, 195, 100, 0, 0, 0, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [13, -175, -100, 260, 0, 0, 255, 0], [14, 408, 0, 260, 0, 0, 0, 0], [14, 408, 245, 260, 0, 0, 0, 0], [10, -135, -175, 260, 90, 0, 180, 0], [9, 290, -25, 260, 50, 0, 180, 0], [8, -175, -100, 260, 0, 0, 180, 0]], [[5, 0, 0, 300, 20, 0, 255, 1], [3, 50, 0, 340, 0, 1, 255, 1], [2, -250, -150, 380, 0, 0, 255, 1], [0, -200, 100, 260, -20, 0, 255, 1], [14, 408, 250, 100, 0, 0, 0, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [13, 90, 0, 260, -30, 0, 255, 0], [14, 408, 268, 260, 0, 0, 0, 0], [11, -135, -175, 260, 90, 0, 180, 0], [10, 290, -25, 260, 50, 0, 180, 0], [9, -175, -100, 260, 0, 0, 180, 0], [8, 90, 0, 260, -30, 0, 180, 0]], [[6, 0, 0, 300, 20, 0, 255, 1], [4, 50, 0, 340, 0, 1, 255, 1], [3, -250, -150, 380, 0, 0, 255, 1], [1, -200, 100, 260, -20, 0, 255, 1], [0, 350, -100, 260, 30, 1, 255, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [13, -290, 50, 260, -40, 0, 255, 0], [12, -135, -175, 260, 90, 0, 180, 0], [11, 290, -25, 260, 50, 0, 180, 0], [10, -175, -100, 260, 0, 0, 180, 0], [9, 90, 0, 260, -30, 0, 180, 0], [8, -290, 50, 260, -40, 0, 180, 0]], [[7, 0, 0, 300, 20, 0, 255, 1], [5, 50, 0, 340, 0, 1, 255, 1], [4, -250, -150, 380, 0, 0, 255, 1], [2, -200, 100, 260, -20, 0, 255, 1], [1, 350, -100, 260, 30, 1, 255, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0], [12, -135, -175, 260, 90, 0, 0, 0], [12, 290, -25, 260, 50, 0, 0, 0], [11, -175, -100, 260, 0, 0, 180, 0], [10, 90, 0, 260, -30, 0, 180, 0], [9, -290, 50, 260, -40, 0, 180, 0]], [[6, -408, 50, 100, 20, 0, 0, 1], [6, 50, 0, 340, 0, 1, 255, 1], [5, -250, -155, 380, 0, 0, 255, 1], [3, -200, 100, 260, -20, 0, 255, 1], [2, 350, -100, 260, 30, 1, 255, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0], [12, -135, -175, 260, 90, 0, 0, 0], [12, 290, -25, 260, 50, 0, 0, 0], [12, -175, -100, 260, 0, 0, 180, 0], [11, 90, 0, 260, -30, 0, 180, 0], [10, -290, 50, 260, -40, 0, 180, 0]], [[7, -369, 33, 100, 20, 0, 0, 1], [7, 50, 0, 340, 0, 1, 255, 1], [6, -250, -150, 380, 0, 0, 255, 1], [4, -200, 100, 260, -20, 0, 255, 1], [3, 350, -100, 260, 30, 1, 255, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0], [12, -135, -175, 260, 90, 0, 0, 0], [12, 290, -25, 260, 50, 0, 0, 0], [12, -175, -100, 260, 0, 0, 0, 0], [12, 90, 0, 260, -30, 0, 180, 0], [11, -290, 50, 260, -40, 0, 180, 0]], [[7, -408, 118, 100, 20, 0, 0, 1], [7, -408, 240, 100, 0, 0, 0, 1], [7, -250, -150, 380, 0, 0, 255, 1], [5, -200, 100, 260, -20, 0, 255, 1], [4, 347.5, -100, 260, 30, 1, 255, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0], [12, -135, -175, 260, 90, 0, 0, 0], [12, 290, -25, 260, 50, 0, 0, 0], [12, -175, -100, 260, 0, 0, 0, 0], [12, 90, 0, 260, -30, 0, 180, 0], [12, -290, 50, 260, -40, 0, 180, 0]], [[6, 408, 218, 100, 20, 0, 0, 1], [6, 133, 195, 100, 0, 0, 0, 1], [6, -177, -107, 380, 0, 0, 0, 1], [6, -200, 100, 260, -20, 0, 255, 1], [5, 350, -100, 260, 30, 1, 255, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0]], [[7, 285, 198, 100, 20, 0, 0, 1], [7, 308, 148, 100, 0, 0, 0, 1], [7, 93, 93, 380, 0, 0, 0, 1], [7, -200, 100, 260, -20, 0, 255, 1], [6, 350, -100, 260, 30, 1, 255, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0]], [[7, -408, 222, 100, 20, 0, 0, 1], [7, -376, 243, 100, 0, 0, 0, 1], [7, -208, 258, 380, 0, 0, 0, 1], [7, 90, 295, 100, -20, 0, 0, 1], [7, 350, -100, 260, 30, 1, 255, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0]], [[14, 208, 245.5, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 285, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0]], [[14, 208, 245.5, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 285, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, -135, -175, 260, 90, 0, 255, 0], [14, 290, -25, 260, 50, 0, 255, 0], [14, -175, -100, 260, 0, 0, 255, 0], [14, 90, 0, 260, -30, 0, 255, 0], [14, -290, 50, 260, -40, 0, 255, 0]], [[14, 208, 245.5, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 285, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, -135, -175, 260, 90, 0, 203, 0], [14, 290, -25, 260, 50, 0, 203, 0], [14, -175, -100, 260, 0, 0, 203, 0], [14, 90, 0, 260, -30, 0, 203, 0], [14, -290, 50, 260, -40, 0, 203, 0]], [[14, 208, 245.5, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 285, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, -135, -175, 260, 90, 0, 152, 0], [14, 290, -25, 260, 50, 0, 152, 0], [14, -175, -100, 260, 0, 0, 152, 0], [14, 90, 0, 260, -30, 0, 152, 0], [14, -290, 50, 260, -40, 0, 152, 0]], [[14, 208, 245.5, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, 185, 285, 100, 0, 0, 0, 1], [14, 185, 235, 100, 0, 0, 0, 1], [14, -135, -175, 260, 90, 0, 100, 0], [14, 290, -25, 260, 50, 0, 100, 0], [14, -175, -100, 260, 0, 0, 100, 0], [14, 90, 0, 260, -30, 0, 100, 0], [14, -290, 50, 260, -40, 0, 100, 0]]], "name": "Wind All 1", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind5", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [170, 255, 221, 204], "flashDuration": 5, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [170, 255, 221, 119], "flashDuration": 5, "flashScope": 2, "frame": 2, "se": {"name": "Wind2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [170, 255, 221, 204], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [170, 255, 221, 119], "flashDuration": 5, "flashScope": 2, "frame": 5, "se": {"name": "Wind2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [170, 255, 221, 204], "flashDuration": 5, "flashScope": 1, "frame": 8, "se": null}, {"flashColor": [170, 255, 221, 119], "flashDuration": 5, "flashScope": 2, "frame": 8, "se": {"name": "Wind2", "pan": 0, "pitch": 150, "volume": 90}}]}, {"id": 94, "animation1Hue": 0, "animation1Name": "Wind4", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, -276, -72, 300, 0, 0, 100, 0]], [[1, -240, -72, 300, 0, 0, 150, 0], [6, 36, -276, 600, 0, 0, 50, 0]], [[2, -204, -72, 300, 0, 0, 200, 0], [7, 36, -276, 600, 0, 0, 100, 0]], [[3, -168, -72, 300, 0, 0, 200, 0], [8, 36, -276, 600, 0, 0, 130, 0]], [[-1, -216, -84, 300, 0, 0, 100, 0], [4, -120, -72, 300, 0, 0, 200, 0], [9, 36, -276, 600, 0, 0, 150, 0]], [[0, -72, -72, 300, 0, 0, 200, 0], [5, 36, -276, 600, 0, 0, 150, 0]], [[1, -24, -72, 300, 0, 0, 200, 0], [6, 36, -276, 600, 0, 0, 150, 0]], [[2, 24, -72, 300, 0, 0, 200, 0], [7, 36, -276, 600, 0, 0, 150, 0]], [[3, 48, -72, 300, 0, 0, 200, 0], [8, 36, -276, 600, 0, 0, 150, 0]], [[4, 48, -72, 300, 0, 0, 200, 0], [9, 36, -276, 600, 0, 0, 150, 0]], [[0, 24, -72, 300, 0, 0, 200, 0], [5, 36, -276, 600, 0, 0, 150, 0]], [[1, 0, -72, 300, 0, 0, 200, 0], [6, 36, -276, 600, 0, 0, 150, 0]], [[2, -60, -72, 300, 0, 0, 200, 0], [7, 36, -276, 600, 0, 0, 150, 0]], [[3, -96, -72, 300, 0, 0, 200, 0], [8, 36, -276, 600, 0, 0, 150, 0]], [[4, -84, -72, 300, 0, 0, 200, 0], [9, 36, -276, 600, 0, 0, 150, 0]], [[0, -48, -72, 300, 0, 0, 200, 0], [5, 36, -276, 600, 0, 0, 150, 0]], [[1, -48, -72, 300, 0, 0, 200, 0], [6, 36, -276, 600, 0, 0, 150, 0]], [[2, -12, -72, 300, 0, 0, 200, 0], [7, 36, -276, 600, 0, 0, 150, 0]], [[3, 36, -72, 300, 0, 0, 200, 0], [8, 36, -276, 600, 0, 0, 150, 0]], [[4, 84, -72, 300, 0, 0, 200, 0], [9, 36, -276, 600, 0, 0, 150, 0]], [[0, 144, -72, 300, 0, 0, 200, 0], [5, 36, -276, 600, 0, 0, 150, 0]], [[4, 204, -72, 300, 0, 0, 200, 0], [6, 36, -276, 600, 0, 0, 150, 0]], [[0, 264, -72, 300, 0, 0, 200, 0], [7, 36, -276, 600, 0, 0, 120, 0]], [[1, 336, -72, 300, 0, 0, 150, 0], [8, 36, -276, 600, 0, 0, 100, 0]], [[2, 408, -72, 300, 0, 0, 100, 0], [9, 36, -276, 600, 0, 0, 70, 0]]], "name": "Wind All 2", "position": 3, "timings": [{"flashColor": [255, 221, 187, 153], "flashDuration": 15, "flashScope": 2, "frame": 0, "se": {"name": "Wind5", "pan": 0, "pitch": 80, "volume": 100}}, {"flashColor": [255, 255, 187, 153], "flashDuration": 3, "flashScope": 1, "frame": 1, "se": {"name": "Sand", "pan": 0, "pitch": 70, "volume": 100}}, {"flashColor": [255, 255, 187, 153], "flashDuration": 1, "flashScope": 1, "frame": 3, "se": null}, {"flashColor": [255, 255, 204, 153], "flashDuration": 3, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [255, 255, 187, 153], "flashDuration": 3, "flashScope": 1, "frame": 10, "se": null}, {"flashColor": [255, 255, 187, 153], "flashDuration": 3, "flashScope": 1, "frame": 15, "se": null}, {"flashColor": [255, 255, 187, 153], "flashDuration": 3, "flashScope": 1, "frame": 17, "se": null}, {"flashColor": [255, 255, 187, 153], "flashDuration": 3, "flashScope": 1, "frame": 20, "se": null}]}, {"id": 95, "animation1Hue": 60, "animation1Name": "PreSpecial1", "animation2Hue": 0, "animation2Name": "Wind5", "frames": [[[0, 0, -72, 330, 0, 0, 255, 1]], [[1, 0, -72, 330, 0, 0, 255, 1]], [[2, 0, -72, 330, 0, 0, 255, 1]], [[3, 0, -72, 330, 0, 0, 255, 1]], [[4, 0, -72, 330, 0, 0, 255, 1], [17, 0, -72, 390, 0, 0, 150, 1]], [[5, 0, -72, 330, 0, 0, 255, 1], [18, 0, -72, 345, 0, 0, 255, 1]], [[6, 0, -72, 330, 0, 0, 255, 1], [19, 0, -72, 345, 0, 0, 255, 1]], [[20, 0, -72, 345, 0, 0, 255, 1], [7, 0, -72, 330, 0, 0, 255, 1]], [[8, 0, -72, 330, 0, 0, 255, 1], [22, 0, -84, 420, 0, 0, 255, 1]], [[9, 0, -72, 330, 0, 0, 255, 1], [23, 0, -84, 420, 0, 0, 255, 1]], [[10, 0, -72, 330, 0, 0, 255, 1], [24, 0, -84, 420, 0, 0, 255, 1]], [[11, 0, -72, 330, 0, 0, 255, 1], [25, 0, -84, 450, 0, 0, 255, 1], [100, -12, -96, 255, 0, 0, 50, 0]], [[12, 0, -72, 330, 0, 0, 255, 1], [20, 0, -84, 300, 0, 0, 200, 1], [22, 0, -84, 375, 0, 0, 255, 1], [26, 0, -84, 450, 0, 0, 255, 1], [100, -12, -96, 255, 0, 0, 100, 0]], [[13, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 465, 0, 0, 200, 1], [100, -12, -96, 255, 0, 0, 150, 0]], [[14, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 480, 0, 0, 180, 1], [100, -12, -96, 255, 0, 0, 150, 0]], [[15, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 487, 0, 0, 150, 1], [100, -12, -96, 255, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 495, 0, 0, 120, 1], [100, -12, -96, 255, 0, 0, 150, 0]], [[26, 0, -84, 502, 0, 0, 100, 1], [16, 0, -72, 330, 0, 0, 200, 1], [100, -12, -96, 255, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 150, 1], [26, 0, -84, 502, 0, 0, 70, 1], [100, -12, -96, 255, 0, 0, 140, 0]], [[16, 0, -72, 330, 0, 0, 100, 1], [26, 0, -84, 502, 0, 0, 40, 1], [100, -12, -96, 255, 0, 0, 130, 0]], [[16, 0, -72, 330, 0, 0, 50, 1], [100, -12, -96, 255, 0, 0, 120, 0]], [[-1, 0, -72, 330, 0, 0, 120, 1], [100, -12, -96, 255, 0, 0, 100, 0]], [[100, -12, -96, 255, 0, 0, 80, 0], [101, 0, 96, 427, 0, 0, 255, 0]], [[100, -12, -96, 255, 0, 0, 60, 0], [102, 0, 96, 427, 0, 0, 255, 0], [123, -72, -84, 300, 0, 0, 255, 1]], [[100, -12, -96, 255, 0, 0, 40, 0], [103, 0, 96, 427, 0, 0, 255, 0], [124, -72, -84, 300, 0, 0, 255, 1], [123, 288, 0, 300, 340, 1, 255, 1]], [[100, -12, -96, 255, 0, 0, 20, 0], [104, 0, 96, 427, 0, 0, 255, 0], [125, -72, -84, 300, 0, 0, 255, 1], [124, 288, 0, 300, 340, 1, 255, 1]], [[105, 0, 96, 427, 0, 0, 255, 0], [126, -72, -84, 300, 0, 0, 255, 1], [125, 288, 0, 300, 340, 1, 255, 1]], [[106, 0, 96, 427, 0, 0, 255, 0], [127, -72, -84, 300, 0, 0, 255, 1], [126, 288, 0, 300, 340, 1, 255, 1], [123, 132, -132, 225, 180, 1, 255, 1]], [[107, 0, 96, 427, 0, 0, 255, 0], [127, 288, 0, 300, 340, 1, 255, 1], [124, 84, -216, 225, 180, 1, 255, 1]], [[108, 0, 96, 427, 0, 0, 255, 0], [123, 288, -96, 300, 45, 0, 255, 1], [125, 84, -216, 225, 180, 1, 255, 1], [123, -240, -48, 300, 125, 1, 255, 1]], [[109, 0, 96, 427, 0, 0, 255, 0], [124, 288, -96, 300, 45, 0, 255, 1], [126, 84, -216, 225, 180, 1, 255, 1], [124, -240, -48, 300, 125, 1, 255, 1]], [[110, 0, 96, 427, 0, 0, 255, 0], [125, 288, -96, 300, 45, 0, 255, 1], [127, 84, -216, 225, 180, 1, 255, 1], [125, -240, -48, 300, 125, 1, 255, 1]], [[111, 0, 96, 427, 0, 0, 255, 0], [126, -240, -48, 300, 125, 1, 255, 1], [126, 288, -96, 300, 45, 0, 255, 1]], [[112, 0, 96, 427, 0, 0, 255, 0], [127, -240, -48, 300, 125, 1, 255, 1], [127, 288, -96, 300, 45, 0, 255, 1]], [[113, 0, 96, 427, 0, 0, 255, 0]], [[114, 0, 96, 427, 0, 0, 255, 0], [123, -24, -132, 345, 90, 0, 255, 1]], [[115, 0, 96, 427, 0, 0, 255, 0], [124, -24, -132, 345, 90, 0, 255, 1], [123, 264, -36, 240, 0, 1, 255, 1]], [[116, 0, 96, 427, 0, 0, 255, 0], [125, -24, -132, 345, 90, 0, 255, 1], [124, 264, -36, 240, 0, 1, 255, 1], [123, -288, 24, 240, 315, 0, 255, 1]], [[117, 0, 96, 427, 0, 0, 255, 0], [126, -24, -132, 345, 90, 0, 255, 1], [125, 264, -36, 240, 0, 1, 255, 1], [124, -288, 24, 240, 315, 0, 255, 1]], [[118, 0, 96, 427, 0, 0, 255, 0], [127, -24, -132, 345, 90, 0, 255, 1], [126, 264, -36, 240, 0, 1, 255, 1], [125, -288, 24, 240, 315, 0, 255, 1]], [[118, 0, 96, 427, 0, 0, 180, 0], [-1, -24, -132, 345, 90, 0, 255, 1], [127, 264, -36, 240, 0, 1, 180, 1], [126, -288, 24, 240, 315, 0, 180, 1]], [[118, 0, 96, 427, 0, 0, 100, 0], [-1, -24, -132, 345, 90, 0, 255, 1], [-1, 369, -6, 240, 0, 1, 180, 1], [127, -288, 24, 240, 315, 0, 100, 1]]], "name": "Wind All 3", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 70, "volume": 65}}, {"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [136, 221, 0, 170], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": {"name": "Wind2", "pan": 0, "pitch": 125, "volume": 75}}, {"flashColor": [187, 221, 170, 187], "flashDuration": 5, "flashScope": 1, "frame": 23, "se": null}, {"flashColor": [187, 221, 170, 187], "flashDuration": 5, "flashScope": 2, "frame": 23, "se": {"name": "Skill2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [187, 221, 170, 204], "flashDuration": 5, "flashScope": 1, "frame": 29, "se": {"name": "Wind5", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [187, 221, 170, 187], "flashDuration": 5, "flashScope": 2, "frame": 29, "se": null}, {"flashColor": [187, 221, 170, 221], "flashDuration": 5, "flashScope": 1, "frame": 35, "se": null}, {"flashColor": [187, 221, 170, 153], "flashDuration": 5, "flashScope": 2, "frame": 35, "se": null}]}, {"id": 96, "animation1Hue": 0, "animation1Name": "Holy1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -290, 300, 0, 0, 255, 1]], [[1, 0, -290, 300, 0, 0, 255, 1]], [[2, 0, -290, 300, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [4, 5, -270, 330, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [5, 5, -270, 330, 0, 0, 255, 1], [9, 0, -210, 260, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [6, 5, -270, 330, 0, 0, 255, 1], [10, 0, -210, 260, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [7, 5, -270, 330, 0, 0, 255, 1], [11, 0, -210, 260, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [12, 0, -210, 260, 0, 0, 255, 1], [16, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [13, 0, -210, 260, 0, 0, 255, 1], [17, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [14, 0, -210, 260, 0, 0, 255, 1], [18, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [15, 0, -210, 260, 0, 0, 255, 1], [19, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [15, 0, -210, 260, 0, 0, 0, 1], [20, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [15, 0, -210, 260, 0, 0, 0, 1], [21, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [15, 0, -210, 260, 0, 0, 0, 1], [22, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [15, 0, -210, 260, 0, 0, 0, 1], [23, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [15, 0, -210, 260, 0, 0, 0, 1], [24, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [15, 0, -210, 260, 0, 0, 0, 1], [25, 0, -240, 340, 0, 0, 255, 1]], [[3, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 255, 1], [15, 0, -210, 260, 0, 0, 0, 1], [26, 0, -240, 340, 0, 0, 255, 1]], [[2, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 187, 1]], [[1, 0, -290, 300, 0, 0, 255, 1], [8, 5, -270, 330, 0, 0, 118, 1]]], "name": "Light One 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Up3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 221, 204], "flashDuration": 3, "flashScope": 1, "frame": 6, "se": {"name": "Reflection", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 221, 136], "flashDuration": 7, "flashScope": 2, "frame": 7, "se": {"name": "Sword4", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 221, 170], "flashDuration": 3, "flashScope": 1, "frame": 8, "se": {"name": "Reflection", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 9, "se": {"name": "Sword4", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 10, "se": {"name": "Reflection", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 11, "se": {"name": "Sword4", "pan": 0, "pitch": 150, "volume": 90}}]}, {"id": 97, "animation1Hue": 0, "animation1Name": "Holy3", "animation2Hue": 0, "animation2Name": "", "frames": [[[1, 0, 0, 300, 0, 0, 255, 1], [0, 0, 0, 375, 0, 0, 50, 1], [0, 0, 0, 150, 0, 0, 255, 1]], [[2, 0, 0, 300, 0, 0, 255, 1], [0, 0, 0, 390, 0, 0, 150, 1], [0, 0, 0, 180, 0, 0, 150, 1]], [[3, 0, 0, 300, 0, 0, 255, 1], [0, 0, 0, 405, 0, 0, 255, 1]], [[4, 0, 0, 300, 0, 0, 255, 1], [0, 0, 0, 412, 0, 0, 200, 1]], [[5, 0, 0, 300, 0, 0, 255, 1], [0, 0, 0, 420, 0, 0, 150, 1]], [[6, 0, 0, 300, 0, 0, 255, 1], [0, 0, 0, 427, 0, 0, 100, 1]], [[7, 0, 0, 300, 0, 0, 255, 1]], [[8, 0, 0, 300, 0, 0, 255, 1]], [[9, 0, 0, 300, 0, 0, 255, 1]], [[10, 0, 0, 300, 0, 0, 255, 1]], [[11, 0, 0, 300, 0, 0, 255, 1]], [[12, 0, 0, 300, 0, 0, 255, 1]], [[13, 0, 0, 300, 0, 0, 255, 1]], [[14, 0, 0, 300, 0, 0, 255, 1]], [[14, 0, 0, 300, 0, 0, 150, 1]]], "name": "Light One 2", "position": 1, "timings": [{"flashColor": [255, 255, 221, 85], "flashDuration": 3, "flashScope": 2, "frame": 0, "se": {"name": "Ice4", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [255, 255, 170, 187], "flashDuration": 3, "flashScope": 1, "frame": 6, "se": null}, {"flashColor": [255, 255, 221, 170], "flashDuration": 5, "flashScope": 2, "frame": 6, "se": {"name": "Saint4", "pan": 0, "pitch": 115, "volume": 100}}]}, {"id": 98, "animation1Hue": 0, "animation1Name": "Holy2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -144, 380, 0, 0, 180, 1]], [[0, 2, -144, 400, 0, 0, 255, 1]], [[1, 0, -144, 400, 0, 0, 255, 1]], [[2, 0, -144, 400, 0, 0, 255, 1]], [[3, 0, -144, 400, 0, 0, 255, 1]], [[4, 0, -144, 400, 0, 0, 203, 1], [7, 0, -50, 380, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 10, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, 0, -144, 400, 0, 0, 152, 1], [8, 0, -50, 380, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 10, 100, 0, 0, 255, 1], [-1, 0, -30, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[6, 0, -144, 400, 0, 0, 100, 1], [9, 0, -50, 380, 0, 0, 255, 1]], [[6, 0, -144, 410, 0, 0, 60, 1], [10, 0, -50, 380, 0, 0, 255, 1]], [[-1, -165, -85, 410, 0, 0, 60, 1], [11, 0, -50, 380, 0, 0, 255, 1]], [[-1, -165, -85, 410, 0, 0, 60, 1], [12, 0, -50, 380, 0, 0, 255, 1]], [[-1, -161, 232.5, 100, 0, 0, 0, 1], [-1, -168.5, 202.5, 300, 0, 0, 0, 1], [-1, -176, 227.5, 100, 0, 0, 0, 1], [-1, -263.5, 205, 300, 0, 0, 0, 1], [16, -310, -75, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [13, 0, -50, 380, 0, 0, 255, 1]], [[-1, -408, 249.5, 100, 0, 0, 0, 1], [-1, 274.5, -7.5, 300, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, 92, 28, 300, 0, 0, 0, 1], [-1, -408, 305.5, 100, 0, 0, 0, 1], [-1, 2, -4.5, 300, 0, 0, 0, 1], [-1, -408, 290.5, 100, 0, 0, 0, 1], [-1, 279.5, 110.5, 300, 0, 0, 0, 1], [17, -310, -75, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [14, 0, -50, 380, 0, 0, 255, 1]], [[29, -310, 95, 100, 0, 0, 0, 1], [-1, -310, -312, 300, 0, 0, 255, 1], [29, -100, 49, 100, 0, 0, 0, 1], [24, -110, -180, 300, 0, 0, 0, 1], [-1, 373, 207, 100, 0, 0, 0, 1], [-1, 291, 18.5, 300, 0, 0, 0, 1], [-1, 374.5, 117.5, 100, 0, 0, 0, 1], [-1, 285.5, 119, 300, 0, 0, 0, 1], [18, -310, -75, 300, 0, 0, 255, 1], [16, -110, -120, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [15, 0, -50, 380, 0, 0, 255, 1]], [[25, -310, 95, 200, 0, 0, 255, 1], [23, -310, -312, 300, 0, 0, 255, 1], [29, -100, 49, 100, 0, 0, 0, 1], [24, -112, -178, 300, 0, 0, 0, 1], [-1, 408, 94.5, 100, 0, 0, 0, 1], [24, 94, -122, 300, 0, 0, 0, 1], [-1, 408, 95, 100, 0, 0, 0, 1], [-1, -24.5, 36.5, 300, 0, 0, 0, 1], [19, -310, -75, 300, 0, 0, 255, 1], [17, -112.5, -120, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0]], [[26, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [29, -100, 49, 100, 0, 0, 0, 1], [-1, -110, -312, 300, 0, 0, 255, 1], [29, 98, 112, 100, 0, 0, 0, 1], [24, 96, -122, 300, 0, 0, 0, 1], [29, 322, 75, 100, 0, 0, 0, 1], [24, 320, -156, 300, 0, 0, 0, 1], [20, -310, -75, 300, 0, 0, 255, 1], [18, -110, -120, 300, 0, 0, 255, 1], [16, 100, -60, 300, 0, 0, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0]], [[27, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [25, -100, 49, 200, 0, 0, 255, 1], [23, -110, -312, 300, 0, 0, 255, 1], [28, 98, 112, 100, 0, 0, 0, 1], [24, 96, -122, 300, 0, 0, 0, 1], [29, 322, 75, 100, 0, 0, 0, 1], [24, 320, -156, 300, 0, 0, 0, 1], [21, -310, -75, 300, 0, 0, 255, 1], [19, -110, -120, 300, 0, 0, 255, 1], [17, 100, -60, 300, 0, 0, 255, 1]], [[28, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [26, -100, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [-1, -83.5, 244.5, 100, 0, 0, 0, 1], [-1, 96, -312, 300, 0, 0, 255, 1], [-1, 287, 257.5, 100, 0, 0, 0, 1], [-1, 90, 49, 300, 0, 0, 0, 1], [22, -310, -75, 300, 0, 0, 255, 1], [20, -110, -120, 300, 0, 0, 255, 1], [18, 100, -60, 300, 0, 0, 255, 1], [16, 320, -95, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [27, -100, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [25, 98, 110, 200, 0, 0, 255, 1], [23, 96, -312, 300, 0, 0, 255, 1], [-1, 302, 222.5, 100, 0, 0, 0, 1], [-1, 320, -156.5, 300, 0, 0, 255, 1], [-1, -312.5, -75, 300, 0, 0, 0, 1], [21, -110, -120, 300, 0, 0, 255, 1], [19, 100, -60, 300, 0, 0, 255, 1], [17, 320, -95, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [28, -100, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [26, 98, 112, 200, 0, 0, 255, 1], [24, 96, -122, 300, 0, 0, 255, 1], [29, 322, 75, 100, 0, 0, 0, 1], [-1, 320, -312, 300, 0, 0, 255, 1], [-1, -237.5, 12.5, 300, 0, 0, 0, 1], [22, -110, -120, 300, 0, 0, 255, 1], [20, 100, -60, 300, 0, 0, 255, 1], [18, 320, -95, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [29, -100.5, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [27, 96, 112, 200, 0, 0, 255, 1], [24, 96, -122, 300, 0, 0, 255, 1], [25, 320, 75, 200, 0, 0, 255, 1], [23, 320, -312, 300, 0, 0, 255, 1], [-1, -207.5, -10, 300, 0, 0, 0, 1], [-1, -408, 50, 300, 0, 0, 0, 1], [21, 100, -60, 300, 0, 0, 255, 1], [19, 320, -95, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [29, -100, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [28, 98, 112, 200, 0, 0, 255, 1], [24, 96, -122, 300, 0, 0, 255, 1], [26, 322, 75, 200, 0, 0, 255, 1], [24, 320, -156.5, 300, 0, 0, 255, 1], [-1, -305, 240, 300, 0, 0, 0, 1], [-1, -217.5, 235, 300, 0, 0, 0, 1], [22, 100, -60, 300, 0, 0, 255, 1], [20, 320, -95, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [29, -100.5, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [29, 98.5, 112, 200, 0, 0, 255, 1], [24, 96, -122, 300, 0, 0, 255, 1], [27, 322, 75, 200, 0, 0, 255, 1], [24, 320, -156.5, 300, 0, 0, 255, 1], [-1, -272.5, 20, 300, 0, 0, 0, 1], [-1, -170, -12.5, 300, 0, 0, 0, 1], [-1, -297.5, 92.5, 300, 0, 0, 0, 1], [21, 320, -95, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [29, -100.5, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [29, 98.5, 112, 200, 0, 0, 255, 1], [24, 96, -122, 300, 0, 0, 255, 1], [28, 322, 75, 200, 0, 0, 255, 1], [24, 320, -156.5, 300, 0, 0, 255, 1], [-1, -277.5, 17.5, 300, 0, 0, 0, 1], [-1, -287.5, 27.5, 300, 0, 0, 0, 1], [-1, -190, 60, 300, 0, 0, 0, 1], [22, 320, -95, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [29, -100.5, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [29, 98.5, 112, 200, 0, 0, 255, 1], [24, 96, -122, 300, 0, 0, 255, 1], [29, 322.5, 75, 200, 0, 0, 255, 1], [24, 320, -156.5, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [29, -100.5, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [29, 98.5, 112, 200, 0, 0, 255, 1], [24, 96, -122, 300, 0, 0, 255, 1], [29, 322.5, 75, 200, 0, 0, 255, 1], [24, 320, -156.5, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 255, 1], [24, -310, -125, 300, 0, 0, 255, 1], [29, -100.5, 49, 200, 0, 0, 255, 1], [24, -110.5, -180, 300, 0, 0, 255, 1], [29, 98.5, 112, 200, 0, 0, 255, 1], [24, 96, -122, 300, 0, 0, 255, 1], [29, 322.5, 75, 200, 0, 0, 255, 1], [24, 320, -156.5, 300, 0, 0, 255, 1]], [[29, -310, 95, 200, 0, 0, 120, 1], [24, -310, -125, 300, 0, 0, 120, 1], [29, -100.5, 49, 200, 0, 0, 120, 1], [24, -110.5, -180, 300, 0, 0, 120, 1], [29, 98.5, 112, 200, 0, 0, 120, 1], [24, 96, -122, 300, 0, 0, 120, 1], [29, 322.5, 75, 200, 0, 0, 120, 1], [24, 320, -156.5, 300, 0, 0, 120, 1]], [[29, -310, 95, 200, 0, 0, 60, 1], [24, -310, -125, 300, 0, 0, 60, 1], [29, -100.5, 49, 200, 0, 0, 60, 1], [24, -110.5, -180, 300, 0, 0, 60, 1], [29, 98.5, 112, 200, 0, 0, 60, 1], [24, 96, -122, 300, 0, 0, 60, 1], [29, 322, 75, 200, 0, 0, 60, 1], [24, 320, -156.5, 300, 0, 0, 60, 1]]], "name": "Light All 1", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Starlight", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 1, "se": {"name": "Ice5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Up1", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [255, 255, 221, 204], "flashDuration": 3, "flashScope": 1, "frame": 13, "se": {"name": "Skill3", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 221, 119], "flashDuration": 6, "flashScope": 2, "frame": 13, "se": null}, {"flashColor": [255, 255, 221, 204], "flashDuration": 6, "flashScope": 2, "frame": 14, "se": {"name": "Sword2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 221, 204], "flashDuration": 3, "flashScope": 1, "frame": 15, "se": {"name": "Sword2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 221, 204], "flashDuration": 3, "flashScope": 1, "frame": 17, "se": {"name": "Sword2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 221, 204], "flashDuration": 3, "flashScope": 1, "frame": 19, "se": {"name": "Sword2", "pan": 0, "pitch": 150, "volume": 90}}]}, {"id": 99, "animation1Hue": 0, "animation1Name": "Holy4", "animation2Hue": 0, "animation2Name": "", "frames": [[[18, 0, -132, 405, 0, 0, 255, 1], [0, 0, 84, 450, 0, 0, 100, 1], [23, 0, 84, 150, 0, 0, 150, 1]], [[18, 0, -120, 405, 0, 0, 255, 1], [19, 0, -108, 390, 0, 0, 150, 1], [23, 0, 84, 195, 0, 0, 180, 1], [0, 0, 84, 390, 0, 0, 150, 1]], [[19, 0, -120, 405, 0, 0, 255, 1], [23, 0, 84, 225, 0, 0, 255, 1], [0, 0, 84, 300, 0, 0, 200, 1]], [[19, 0, -108, 405, 0, 0, 200, 1], [23, 0, 84, 240, 0, 0, 180, 1], [24, 0, 84, 150, 0, 0, 255, 1], [0, 0, 84, 270, 0, 0, 200, 1]], [[19, 0, -108, 405, 0, 0, 255, 1], [24, 0, 84, 225, 0, 0, 255, 1], [0, 0, 84, 225, 0, 0, 200, 1]], [[19, 0, -108, 405, 0, 0, 200, 1], [24, 0, 84, 300, 0, 0, 255, 1], [0, 0, 84, 150, 0, 0, 100, 1]], [[19, 0, -108, 405, 0, 0, 255, 1], [1, 0, -84, 300, 0, 0, 255, 1], [24, 0, 84, 270, 0, 0, 200, 1]], [[19, 0, -108, 405, 0, 0, 150, 1], [18, 0, -108, 405, 0, 0, 255, 1], [13, 0, -72, 300, 0, 0, 255, 1], [-1, 0, 108, 30, 0, 0, 0, 0], [2, 0, -84, 300, 0, 0, 255, 1]], [[18, 0, -108, 375, 0, 0, 200, 1], [3, 0, -72, 300, 0, 0, 255, 1], [14, 0, -84, 300, 0, 0, 255, 1], [24, -252, 96, 270, 0, 0, 200, 1], [1, 264, -72, 300, 0, 1, 255, 1], [1, -264, -72, 300, 0, 0, 255, 1], [24, 252, 96, 270, 0, 0, 200, 1]], [[4, 0, -60, 300, 0, 0, 255, 1], [15, 0, -48, 300, 0, 0, 255, 1], [20, 0, 96, 300, 0, 0, 255, 1], [5, 0, -72, 300, 0, 0, 255, 1], [2, -264, -72, 300, 0, 0, 255, 1], [13, -264, -72, 300, 0, 0, 255, 1], [13, 264, -60, 300, 0, 0, 255, 1], [2, 252, -60, 300, 0, 1, 255, 1]], [[22, 0, 96, 300, 0, 0, 255, 1], [16, 0, -36, 300, 0, 0, 255, 1], [15, 0, -24, 225, 0, 0, 255, 1], [8, 0, -132, 300, 0, 0, 255, 1], [3, -264, -72, 300, 0, 0, 255, 1], [14, -264, -72, 300, 0, 0, 255, 1], [14, 264, -60, 300, 0, 0, 255, 1], [3, 264, -60, 300, 0, 1, 255, 1]], [[6, 0, -60, 300, 0, 0, 255, 1], [17, 0, -36, 300, 0, 0, 255, 1], [22, 0, 96, 330, 0, 0, 160, 1], [9, 0, -132, 300, 0, 0, 255, 1], [18, 0, -36, 300, 0, 0, 255, 1], [4, -264, -72, 300, 0, 0, 255, 1], [8, -264, -156, 300, 0, 0, 255, 1], [15, -264, -24, 225, 0, 0, 255, 1], [15, 264, -24, 225, 0, 0, 255, 1], [8, 264, -156, 300, 0, 0, 255, 1], [4, 264, -72, 300, 0, 0, 255, 1], [20, -264, 84, 300, 0, 0, 255, 1], [20, 264, 84, 300, 0, 0, 255, 1]], [[7, 0, -60, 300, 0, 0, 255, 1], [10, 0, -132, 300, 0, 0, 255, 1], [18, -264, -60, 300, 0, 0, 255, 1], [9, -264, -156, 300, 0, 0, 255, 1], [18, 264, -48, 300, 0, 0, 255, 1], [9, 264, -156, 300, 0, 0, 255, 1], [22, -264, 84, 300, 0, 0, 255, 1], [22, 264, 84, 300, 0, 0, 255, 1], [4, -192, 12, 150, 0, 0, 255, 1], [4, 192, 12, 150, 0, 0, 255, 1]], [[11, 0, -132, 300, 0, 0, 255, 1], [10, -264, -156, 300, 0, 0, 255, 1], [10, 264, -156, 300, 0, 0, 255, 1], [22, -264, 84, 330, 0, 0, 160, 1], [22, 264, 84, 330, 0, 0, 160, 1], [18, -192, -60, 300, 0, 0, 255, 1], [5, -192, 12, 150, 0, 0, 255, 1], [13, -192, 12, 150, 0, 0, 255, 1], [18, 192, -60, 300, 0, 0, 255, 1], [13, 192, 12, 150, 0, 0, 255, 1], [5, 192, 12, 150, 0, 0, 255, 1]], [[12, 0, -132, 300, 0, 0, 255, 1], [11, -264, -156, 300, 0, 0, 255, 1], [11, 264, -156, 300, 0, 0, 255, 1], [6, -192, 12, 150, 0, 0, 255, 1], [18, -192, -204, 300, 0, 0, 150, 1], [6, 192, 12, 150, 0, 0, 255, 1], [18, 192, -204, 300, 0, 0, 150, 1], [13, 336, 12, 150, 0, 0, 255, 1], [4, -336, 12, 150, 0, 0, 255, 1], [18, 336, -204, 300, 0, 0, 150, 1], [4, 336, 12, 150, 0, 0, 255, 1], [13, -336, 12, 150, 0, 0, 255, 1]], [[12, 0, -132, 315, 0, 0, 150, 1], [12, -264, -156, 300, 0, 0, 255, 1], [12, 264, -156, 300, 0, 0, 255, 1], [7, -192, 12, 150, 0, 0, 255, 1], [7, 192, 12, 150, 0, 0, 255, 1], [18, 336, -60, 300, 0, 0, 255, 1], [18, -336, -60, 300, 0, 0, 255, 1], [5, -336, 12, 150, 0, 0, 255, 1], [5, 336, 12, 150, 0, 0, 255, 1]], [[12, -264, -156, 315, 0, 0, 150, 1], [12, 264, -156, 315, 0, 0, 150, 1], [6, -336, 12, 150, 0, 0, 255, 1], [18, -336, -204, 300, 0, 0, 150, 1], [6, 336, 12, 150, 0, 0, 255, 1], [18, 336, -204, 300, 0, 0, 150, 1]], [[7, -336, 12, 150, 0, 0, 255, 1], [7, 336, 12, 150, 0, 0, 255, 1]]], "name": "Light All 2", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Skill1", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 8, "se": {"name": "Explosion1", "pan": 0, "pitch": 150, "volume": 100}}, {"flashColor": [255, 255, 204, 204], "flashDuration": 5, "flashScope": 1, "frame": 8, "se": {"name": "Saint4", "pan": 0, "pitch": 120, "volume": 90}}, {"conditions": 0, "flashColor": [255, 255, 187, 153], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": null}]}, {"id": 100, "animation1Hue": 360, "animation1Name": "PreSpecial2", "animation2Hue": 0, "animation2Name": "Holy5", "frames": [[[0, 0, -72, 330, 0, 0, 255, 1]], [[1, 0, -72, 330, 0, 0, 255, 1]], [[2, 0, -72, 330, 0, 0, 255, 1]], [[3, 0, -72, 330, 0, 0, 255, 1]], [[4, 0, -72, 330, 0, 0, 255, 1], [17, 0, -72, 390, 0, 0, 150, 1]], [[5, 0, -72, 330, 0, 0, 255, 1], [18, 0, -72, 345, 0, 0, 255, 1]], [[6, 0, -72, 330, 0, 0, 255, 1], [19, 0, -72, 345, 0, 0, 255, 1]], [[20, 0, -72, 345, 0, 0, 255, 1], [7, 0, -72, 330, 0, 0, 255, 1]], [[8, 0, -72, 330, 0, 0, 255, 1], [22, 0, -84, 420, 0, 0, 255, 1]], [[9, 0, -72, 330, 0, 0, 255, 1], [23, 0, -84, 420, 0, 0, 255, 1]], [[10, 0, -72, 330, 0, 0, 255, 1], [24, 0, -84, 420, 0, 0, 255, 1]], [[11, 0, -72, 330, 0, 0, 255, 1], [25, 0, -84, 450, 0, 0, 255, 1], [100, 0, -72, 330, 0, 0, 50, 0]], [[12, 0, -72, 330, 0, 0, 255, 1], [20, 0, -84, 300, 0, 0, 200, 1], [22, 0, -84, 375, 0, 0, 255, 1], [26, 0, -84, 450, 0, 0, 255, 1], [100, 0, -72, 330, 0, 0, 100, 0]], [[13, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 465, 0, 0, 200, 1], [-1, 0, 72, 525, 0, 0, 50, 1], [100, 0, -72, 330, 0, 0, 150, 0]], [[14, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 480, 0, 0, 180, 1], [-1, 0, 72, 525, 0, 0, 100, 1], [100, 0, -72, 330, 0, 0, 150, 0]], [[15, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 487, 0, 0, 150, 1], [-1, 0, 72, 525, 0, 0, 150, 1], [100, 0, -72, 330, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 255, 1], [26, 0, -84, 495, 0, 0, 120, 1], [-1, 0, 72, 525, 0, 0, 180, 1], [100, 0, -72, 330, 0, 0, 150, 0]], [[26, 0, -84, 502, 0, 0, 100, 1], [16, 0, -72, 330, 0, 0, 200, 1], [-1, 0, 72, 525, 0, 0, 180, 1], [100, 0, -72, 330, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 150, 1], [26, 0, -84, 502, 0, 0, 70, 1], [100, 0, -72, 330, 0, 0, 140, 0], [101, 0, -84, 225, 0, 0, 100, 1]], [[16, 0, -72, 330, 0, 0, 100, 1], [26, 0, -84, 502, 0, 0, 40, 1], [100, 0, -72, 330, 0, 0, 130, 0], [102, 0, -84, 225, 0, 0, 100, 1]], [[16, 0, -72, 330, 0, 0, 50, 1], [100, 0, -72, 330, 0, 0, 120, 0], [103, 0, -84, 225, 0, 0, 100, 1]], [[100, 0, -72, 330, 0, 0, 100, 0], [104, 0, -84, 225, 0, 0, 120, 1]], [[100, 0, -72, 330, 0, 0, 80, 0], [105, 0, -84, 225, 0, 0, 150, 1]], [[100, 0, -72, 330, 0, 0, 60, 0], [106, 0, -84, 255, 0, 0, 180, 1]], [[100, 0, -72, 330, 0, 0, 40, 0], [107, 0, -84, 277, 0, 0, 200, 1]], [[100, 0, -72, 330, 0, 0, 20, 0], [108, 0, -84, 285, 0, 0, 220, 1]], [[109, 0, -84, 288, 0, 0, 240, 1]], [[110, 0, -84, 291, 0, 0, 255, 1], [118, 0, 96, 427, 0, 0, 255, 1]], [[111, 0, -84, 294, 0, 0, 255, 1], [119, 0, 96, 427, 0, 0, 255, 1]], [[112, 0, -84, 300, 0, 0, 255, 1], [120, 0, 96, 427, 0, 0, 255, 1]], [[113, 0, -84, 300, 0, 0, 255, 1], [121, 0, 96, 427, 0, 0, 255, 1]], [[114, 0, -84, 300, 0, 0, 255, 1], [122, 0, 96, 427, 0, 0, 255, 1]], [[115, 0, -84, 300, 0, 0, 255, 1], [123, 0, 96, 427, 0, 0, 255, 1]], [[116, 0, -84, 300, 0, 0, 255, 1], [124, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [125, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [126, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [123, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [124, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [125, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [126, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [123, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [124, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [125, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [126, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [123, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 255, 1], [122, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 200, 1], [121, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 150, 1], [120, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 100, 1], [119, 0, 96, 427, 0, 0, 255, 1]], [[117, 0, -84, 300, 0, 0, 50, 1], [118, 0, 96, 427, 0, 0, 255, 1]]], "name": "Light All 3", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 70, "volume": 65}}, {"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 187, 153], "flashDuration": 10, "flashScope": 2, "frame": 9, "se": {"name": "Skill3", "pan": 0, "pitch": 110, "volume": 75}}, {"flashColor": [187, 221, 170, 187], "flashDuration": 5, "flashScope": 0, "frame": 19, "se": {"name": "Ice5", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 204, 187], "flashDuration": 10, "flashScope": 2, "frame": 22, "se": null}, {"flashColor": [255, 255, 204, 187], "flashDuration": 3, "flashScope": 0, "frame": 27, "se": {"name": "Ice4", "pan": 0, "pitch": 80, "volume": 90}}, {"flashColor": [255, 255, 204, 187], "flashDuration": 3, "flashScope": 1, "frame": 30, "se": null}, {"flashColor": [255, 255, 204, 187], "flashDuration": 3, "flashScope": 1, "frame": 33, "se": null}, {"flashColor": [255, 255, 204, 187], "flashDuration": 3, "flashScope": 1, "frame": 36, "se": null}, {"flashColor": [255, 255, 204, 187], "flashDuration": 10, "flashScope": 1, "frame": 39, "se": null}]}, {"id": 101, "animation1Hue": 0, "animation1Name": "Darkness1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 280, 0, 0, 255, 0]], [[1, -2, 0, 260, 0, 0, 255, 0]], [[2, 0, 0, 240, 0, 0, 255, 0]], [[3, 0, 0, 220, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[4, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[5, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[6, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[7, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[3, 0, 0, 200, 0, 0, 255, 0], [8, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[4, 0, 0, 200, 0, 0, 255, 0], [9, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[5, 0, 0, 200, 0, 0, 255, 0], [10, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[6, -10, -10, 180, 0, 0, 255, 0], [11, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1], [-1, 0, 0, 0, 0, 0, 0, 0]], [[7, 10, 10, 180, 0, 0, 255, 0], [12, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[3, 0, 0, 200, 0, 0, 255, 0], [13, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[4, 0, 0, 200, 0, 0, 255, 0], [14, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[5, 0, 0, 200, 0, 0, 255, 0], [15, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[6, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[7, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[3, 0, 0, 180, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[5, 0, 0, 220, 0, 0, 180, 0], [16, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 0, 1]], [[-1, 0, 0, 100, 0, 0, 0, 1], [17, 0, 0, 180, 0, 0, 255, 0], [-1, -65, 27.5, 260, 0, 0, 100, 0]], [[-1, 0, 0, 100, 0, 0, 0, 1], [18, 0, 0, 160, 0, 0, 255, 0], [-1, 15, 15, 280, 0, 0, 100, 0]]], "name": "Darkness One 1", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Darkness1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [136, 136, 187, 221], "flashDuration": 3, "flashScope": 2, "frame": 9, "se": null}, {"flashColor": [136, 136, 187, 221], "flashDuration": 5, "flashScope": 2, "frame": 11, "se": null}, {"flashColor": [119, 119, 187, 238], "flashDuration": 3, "flashScope": 1, "frame": 10, "se": null}, {"flashColor": [119, 119, 187, 238], "flashDuration": 5, "flashScope": 1, "frame": 12, "se": null}]}, {"id": 102, "animation1Hue": 0, "animation1Name": "Darkness3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 150, 0, 0, 200, 0]], [[1, 0, 0, 195, 0, 0, 255, 0]], [[2, 0, 0, 225, 0, 0, 255, 0], [2, 0, 0, 300, 0, 0, 150, 0], [4, 0, 0, 300, 0, 1, 255, 1]], [[3, 0, 0, 225, 0, 0, 255, 0], [5, 0, 0, 300, 0, 1, 255, 1], [2, 0, 0, 315, 0, 0, 100, 0]], [[0, 0, 0, 225, 0, 0, 255, 0], [6, 0, 0, 300, 0, 1, 255, 1]], [[1, 0, 0, 225, 0, 0, 255, 0], [7, 0, 0, 300, 0, 1, 255, 1]], [[2, 0, 0, 225, 0, 0, 255, 0], [8, 0, 0, 300, 0, 1, 255, 1]], [[3, 0, 0, 225, 0, 0, 255, 0]], [[0, 0, 0, 225, 0, 0, 255, 0]], [[1, 0, 0, 225, 0, 0, 255, 0]], [[2, 0, 0, 225, 0, 0, 255, 0], [4, 0, 0, 300, 90, 1, 255, 1]], [[3, 0, 0, 225, 0, 0, 255, 0], [5, 0, 0, 300, 90, 1, 255, 1]], [[0, 0, 0, 225, 0, 0, 255, 0], [6, 0, 0, 300, 90, 1, 255, 1]], [[1, 0, 0, 225, 0, 0, 200, 0], [7, 0, 0, 300, 90, 1, 255, 1]], [[2, 0, 0, 225, 0, 0, 150, 0], [8, 0, 0, 300, 90, 1, 255, 1]], [[3, 0, 0, 225, 0, 0, 100, 0]]], "name": "Darkness One 2", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Darkness3", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 102, 221, 153], "flashDuration": 15, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [238, 102, 221, 170], "flashDuration": 5, "flashScope": 2, "frame": 2, "se": null}, {"flashColor": [255, 102, 221, 153], "flashDuration": 5, "flashScope": 2, "frame": 10, "se": null}]}, {"id": 103, "animation1Hue": 0, "animation1Name": "Darkness2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -50, 200, 0, 0, 100, 0]], [[1, 0, -50, 255, 0, 0, 180, 0], [-1, -390.5, 22.5, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[2, 0, -50, 310, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, -50, 365, 0, 0, 255, 0], [-1, -408, 207.5, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, -50, 420, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[0, 0, -50, 475, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[1, 0, -50, 530, 0, 0, 255, 0], [-1, -235, 175, 300, 0, 0, 160, 0], [-1, -105, 0, 300, 0, 0, 160, 0], [-1, 110, 0, 300, 0, 0, 160, 0], [-1, 300, 0, 300, 0, 0, 160, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[2, 0, -50, 585, 0, 0, 200, 0], [-1, -95, 97.5, 400, 0, 0, 200, 0], [-1, -102.5, -2, 400, 0, 0, 200, 0], [-1, 110, 0, 400, 0, 0, 200, 0], [-1, 300, 0, 400, 0, 0, 200, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[3, 0, -50, 640, 0, 0, 160, 0], [-1, -52.5, 180, 500, 0, 0, 255, 0], [-1, -102.5, 0, 500, 0, 0, 255, 0], [-1, 110, 0, 500, 0, 0, 255, 0], [-1, 300, 0, 500, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[4, 0, -50, 640, 0, 0, 160, 0], [-1, -52.5, 180, 500, 0, 0, 255, 0], [-1, -102.5, 0, 500, 0, 0, 255, 0], [-1, 110, 0, 500, 0, 0, 255, 0], [-1, 300, 0, 500, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, -290, -170, 320, 0, 0, 100, 0], [8, -290, -312, 300, 0, 0, 255, 0], [-1, 403, 309.5, 100, 0, 0, 0, 1]], [[6, -290, -170, 320, 0, 0, 255, 0], [9, -290, -128, 360, 0, 0, 255, 0], [11, -290, -70, 360, 0, 0, 255, 0]], [[7, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [12, -290, -70, 360, 0, 0, 255, 0], [5, -105, -230, 320, 0, 0, 100, 0], [8, -105, -312, 300, 0, 0, 255, 0]], [[5, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [13, -290, -70, 360, 0, 0, 255, 0], [6, -105, -230, 320, 0, 0, 255, 0], [9, -105, -128, 360, 0, 0, 255, 0], [11, -105, -130, 360, 0, 1, 255, 0]], [[6, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [14, -290, -70, 360, 0, 0, 255, 0], [7, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [12, -105, -130, 360, 0, 1, 255, 0], [5, 110, -145, 320, 0, 0, 100, 0], [8, 110, -312, 300, 0, 0, 255, 0]], [[7, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [15, -290, -70, 360, 0, 0, 255, 0], [5, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [13, -105, -130, 360, 0, 1, 255, 0], [6, 110, -145, 320, 0, 0, 255, 0], [9, 110, -64, 360, 0, 0, 255, 0], [11, 110, -45, 360, 0, 0, 255, 0]], [[5, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [16, -290, -70, 360, 0, 0, 255, 0], [6, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [14, -105, -130, 360, 0, 1, 255, 0], [7, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [12, 110, -45, 360, 0, 0, 255, 0], [5, 300, -220, 320, 0, 0, 100, 0], [8, 300, -312, 300, 0, 0, 255, 0]], [[6, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [17, -290, -70, 360, 0, 0, 255, 0], [7, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [15, -105, -130, 360, 0, 1, 255, 0], [5, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [13, 110, -45, 360, 0, 0, 255, 0], [6, 300, -220, 320, 0, 0, 255, 0], [9, 300, -128, 360, 0, 0, 255, 0], [11, 300, -110, 360, 0, 1, 255, 0]], [[7, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [18, -290, -70, 360, 0, 0, 255, 0], [5, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [16, -105, -130, 360, 0, 1, 255, 0], [6, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [14, 110, -45, 360, 0, 0, 255, 0], [7, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [12, 300, -110, 360, 0, 1, 255, 0]], [[5, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [19, -290, -70, 360, 0, 0, 255, 0], [6, -105, -230, 320, 0, 0, 255, 0], [-1, 405.5, 312, 100, 0, 0, 0, 1], [17, -105, -130, 360, 0, 1, 255, 0], [7, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [15, 110, -45, 360, 0, 0, 255, 0], [5, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [13, 300, -110, 360, 0, 1, 255, 0]], [[6, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [20, -290, -70, 360, 0, 0, 255, 0], [7, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [18, -105, -130, 360, 0, 1, 255, 0], [5, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [16, 110, -45, 360, 0, 0, 255, 0], [6, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [14, 300, -110, 360, 0, 1, 255, 0]], [[5, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [16, -290, -70, 360, 0, 0, 255, 0], [5, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [19, -105, -130, 360, 0, 1, 255, 0], [6, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [17, 110, -45, 360, 0, 0, 255, 0], [7, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [15, 300, -110, 360, 0, 1, 255, 0]], [[6, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [17, -290, -70, 360, 0, 0, 255, 0], [6, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [20, -105, -130, 360, 0, 1, 255, 0], [7, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [18, 110, -45, 360, 0, 0, 255, 0], [5, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [16, 300, -110, 360, 0, 1, 255, 0]], [[7, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [18, -290, -70, 360, 0, 0, 255, 0], [5, -105, -230, 320, 0, 0, 255, 0], [-1, 405.5, 312, 100, 0, 0, 0, 1], [16, -105, -130, 360, 0, 1, 255, 0], [5, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [19, 110, -45, 360, 0, 0, 255, 0], [6, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [17, 300, -110, 360, 0, 1, 255, 0]], [[5, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [19, -290, -70, 360, 0, 0, 255, 0], [6, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [17, -105, -130, 360, 0, 1, 255, 0], [6, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [20, 110, -45, 360, 0, 0, 255, 0], [7, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [18, 300, -110, 360, 0, 1, 255, 0]], [[6, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [20, -290, -70, 360, 0, 0, 255, 0], [7, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [18, -105, -130, 360, 0, 1, 255, 0], [7, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [16, 110, -45, 360, 0, 0, 255, 0], [5, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 309.5, 100, 0, 0, 0, 1], [19, 300, -110, 360, 0, 1, 255, 0]], [[7, -290, -170, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [16, -290, -70, 360, 0, 0, 255, 0], [5, -105, -230, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [19, -105, -130, 360, 0, 1, 255, 0], [5, 110, -145, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [17, 110, -45, 360, 0, 0, 255, 0], [6, 300, -220, 320, 0, 0, 255, 0], [-1, 408, 312, 100, 0, 0, 0, 1], [20, 300, -110, 360, 0, 1, 255, 0]], [[6, -290, -170, 320, 0, 0, 197, 0], [-1, 408, 312, 100, 0, 0, 27, 1], [17, -290, -70, 360, 0, 0, 197, 0], [6, -105, -230, 320, 0, 0, 197, 0], [-1, 408, 312, 100, 0, 0, 27, 1], [20, -105, -130, 360, 0, 1, 197, 0], [7, 110, -145, 320, 0, 0, 197, 0], [-1, 408, 312, 100, 0, 0, 27, 1], [18, 110, -45, 360, 0, 0, 197, 0], [5, 300, -220, 320, 0, 0, 197, 0], [-1, 408, 312, 100, 0, 0, 27, 1], [16, 300, -110, 360, 0, 1, 197, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[7, -290, -170, 320, 0, 0, 138, 0], [-1, 408, 312, 100, 0, 0, 53, 1], [18, -290, -70, 360, 0, 0, 138, 0], [5, -105, -230, 320, 0, 0, 138, 0], [-1, 408, 312, 100, 0, 0, 53, 1], [16, -105, -130, 360, 0, 1, 138, 0], [5, 110, -145, 320, 0, 0, 138, 0], [-1, 408, 309.5, 100, 0, 0, 53, 1], [19, 110, -45, 360, 0, 0, 138, 0], [6, 300, -220, 320, 0, 0, 138, 0], [-1, 408, 312, 100, 0, 0, 53, 1], [17, 300, -110, 360, 0, 1, 138, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[5, -290, -170, 320, 0, 0, 80, 0], [-1, 408, 312, 100, 0, 0, 80, 1], [19, -290, -70, 360, 0, 0, 80, 0], [6, -105, -230, 320, 0, 0, 80, 0], [-1, 408, 312, 100, 0, 0, 80, 1], [17, -105, -130, 360, 0, 1, 80, 0], [6, 110, -145, 320, 0, 0, 80, 0], [-1, 408, 312, 100, 0, 0, 80, 1], [20, 110, -45, 360, 0, 0, 80, 0], [7, 300, -220, 320, 0, 0, 80, 0], [-1, 408, 312, 100, 0, 0, 80, 1], [18, 300, -110, 360, 0, 1, 80, 0]]], "name": "Darkness All 1", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Darkness4", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [187, 170, 221, 255], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": {"name": "Thunder3", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [51, 34, 85, 255], "flashDuration": 3, "flashScope": 1, "frame": 11, "se": null}, {"flashColor": [51, 34, 85, 255], "flashDuration": 3, "flashScope": 1, "frame": 13, "se": {"name": "Thunder3", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [51, 34, 85, 255], "flashDuration": 3, "flashScope": 1, "frame": 15, "se": null}, {"flashColor": [51, 34, 85, 255], "flashDuration": 7, "flashScope": 1, "frame": 17, "se": null}]}, {"id": 104, "animation1Hue": 0, "animation1Name": "Darkness4", "animation2Hue": 0, "animation2Name": "", "frames": [[[5, 0, 0, 300, 0, 0, 180, 1], [9, 0, 0, 300, 0, 0, 180, 1]], [[6, 0, 0, 345, 0, 0, 200, 1], [10, 0, 0, 345, 0, 0, 200, 1]], [[7, 0, 0, 390, 0, 0, 230, 1], [11, 0, 0, 390, 0, 0, 230, 1], [-1, 0, 0, 30, 0, 0, 0, 0], [13, 0, 0, 150, 0, 0, 255, 1]], [[8, 0, 0, 420, 0, 0, 255, 1], [12, 0, 0, 420, 0, 0, 255, 1], [-1, 0, 0, 30, 0, 0, 0, 0], [14, 0, 0, 240, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 255, 1], [9, 0, 0, 450, 0, 0, 255, 1], [-1, 0, 0, 30, 0, 0, 0, 0], [15, 0, 0, 300, 0, 0, 255, 1]], [[6, 0, 0, 450, 0, 0, 255, 1], [10, 0, 0, 450, 0, 0, 255, 1], [-1, 0, 0, 30, 0, 0, 0, 0], [16, 0, 0, 330, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [11, 0, 0, 450, 0, 0, 255, 1], [16, 0, 0, 360, 0, 0, 255, 1]], [[8, 0, 0, 450, 0, 0, 255, 1], [12, 0, 0, 450, 0, 0, 255, 1], [16, 0, 0, 390, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 255, 1], [9, 0, 0, 450, 0, 0, 255, 1], [13, 0, 0, 420, 0, 0, 255, 1]], [[6, 0, 0, 450, 0, 0, 255, 1], [10, 0, 0, 450, 0, 0, 255, 1], [0, 0, 0, 375, 0, 0, 255, 1], [14, 0, 0, 435, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [11, 0, 0, 450, 0, 0, 255, 1], [1, 0, 0, 375, 0, 0, 255, 1], [15, 0, 0, 450, 0, 0, 255, 1]], [[8, 0, 0, 450, 0, 0, 255, 1], [12, 0, 0, 450, 0, 0, 255, 1], [2, 0, 0, 375, 0, 0, 255, 1], [16, 0, 0, 465, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 255, 1], [9, 0, 0, 450, 0, 0, 255, 1], [3, 0, 0, 375, 0, 0, 255, 1], [16, 0, 0, 480, 0, 0, 255, 1]], [[6, 0, 0, 450, 0, 0, 255, 1], [10, 0, 0, 450, 0, 0, 255, 1], [4, 0, 0, 375, 0, 0, 255, 1], [16, 0, 0, 495, 0, 0, 200, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [11, 0, 0, 450, 0, 0, 255, 1], [0, 0, 0, 375, 0, 0, 255, 1], [16, 0, 0, 502, 0, 0, 150, 1], [17, 0, 0, 600, 0, 0, 255, 1], [21, -300, -108, 105, 0, 0, 255, 1], [21, 300, 72, 105, 0, 0, 255, 1]], [[8, 0, 0, 450, 0, 0, 255, 1], [12, 0, 0, 450, 0, 0, 255, 1], [1, 0, 0, 375, 0, 0, 255, 1], [18, 0, 0, 600, 0, 0, 255, 1], [22, -300, -108, 150, 0, 0, 255, 1], [22, 300, 72, 150, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 255, 1], [9, 0, 0, 450, 0, 0, 255, 1], [2, 0, 0, 375, 0, 0, 255, 1], [19, 0, 0, 600, 0, 0, 255, 1], [23, -300, -108, 150, 0, 0, 255, 1], [23, 300, 72, 150, 0, 0, 255, 1]], [[6, 0, 0, 450, 0, 0, 255, 1], [10, 0, 0, 450, 0, 0, 255, 1], [3, 0, 0, 375, 0, 0, 255, 1], [20, 0, 0, 600, 0, 0, 255, 1], [24, -300, -108, 150, 0, 0, 255, 1], [24, 300, 72, 150, 0, 0, 255, 1], [21, 216, -192, 75, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [11, 0, 0, 450, 0, 0, 255, 1], [4, 0, 0, 375, 0, 0, 255, 1], [17, 0, 0, 600, 0, 0, 255, 1], [25, -300, -108, 150, 0, 0, 255, 1], [25, 300, 72, 150, 0, 0, 255, 1], [22, 216, -192, 120, 0, 0, 255, 1]], [[8, 0, 0, 450, 0, 0, 255, 1], [12, 0, 0, 450, 0, 0, 255, 1], [0, 0, 0, 375, 0, 0, 255, 1], [18, 0, 0, 600, 0, 0, 255, 1], [23, 216, -192, 120, 0, 0, 255, 1], [21, -264, 72, 75, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 255, 1], [9, 0, 0, 450, 0, 0, 255, 1], [1, 0, 0, 375, 0, 0, 255, 1], [19, 0, 0, 600, 0, 0, 255, 1], [24, 216, -192, 120, 0, 0, 255, 1], [22, -264, 72, 120, 0, 0, 255, 1]], [[6, 0, 0, 450, 0, 0, 255, 1], [10, 0, 0, 450, 0, 0, 255, 1], [2, 0, 0, 375, 0, 0, 255, 1], [20, 0, 0, 600, 0, 0, 255, 1], [25, 216, -192, 120, 0, 0, 255, 1], [23, -264, 72, 120, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [11, 0, 0, 450, 0, 0, 255, 1], [3, 0, 0, 375, 0, 0, 255, 1], [17, 0, 0, 600, 0, 0, 255, 1], [24, -264, 72, 120, 0, 0, 255, 1], [21, -132, -228, 105, 0, 0, 255, 1]], [[8, 0, 0, 450, 0, 0, 255, 1], [12, 0, 0, 450, 0, 0, 255, 1], [4, 0, 0, 375, 0, 0, 255, 1], [18, 0, 0, 600, 0, 0, 255, 1], [25, -264, 72, 120, 0, 0, 255, 1], [22, -132, -228, 150, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 255, 1], [9, 0, 0, 450, 0, 0, 255, 1], [0, 0, 0, 375, 0, 0, 255, 1], [19, 0, 0, 600, 0, 0, 255, 1], [23, -132, -228, 150, 0, 0, 255, 1], [21, 312, -12, 75, 0, 0, 255, 1]], [[6, 0, 0, 450, 0, 0, 255, 1], [10, 0, 0, 450, 0, 0, 255, 1], [1, 0, 0, 375, 0, 0, 255, 1], [20, 0, 0, 600, 0, 0, 255, 1], [24, -132, -228, 150, 0, 0, 255, 1], [22, 312, -12, 120, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [11, 0, 0, 450, 0, 0, 255, 1], [2, 0, 0, 375, 0, 0, 255, 1], [17, 0, 0, 600, 0, 0, 255, 1], [25, -132, -228, 150, 0, 0, 255, 1], [23, 312, -12, 120, 0, 0, 255, 1]], [[8, 0, 0, 450, 0, 0, 255, 1], [12, 0, 0, 450, 0, 0, 255, 1], [3, 0, 0, 375, 0, 0, 255, 1], [18, 0, 0, 600, 0, 0, 255, 1], [24, 312, -12, 120, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 255, 1], [9, 0, 0, 450, 0, 0, 255, 1], [4, 0, 0, 375, 0, 0, 255, 1], [19, 0, 0, 600, 0, 0, 255, 1], [25, 312, -12, 120, 0, 0, 255, 1]], [[6, 0, 0, 450, 0, 0, 255, 1], [10, 0, 0, 450, 0, 0, 255, 1], [0, 0, 0, 375, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [11, 0, 0, 450, 0, 0, 255, 1], [1, 0, 0, 375, 0, 0, 255, 1]], [[8, 0, 0, 450, 0, 0, 255, 1], [12, 0, 0, 450, 0, 0, 255, 1], [2, 0, 0, 375, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 255, 1], [9, 0, 0, 450, 0, 0, 255, 1], [3, 0, 0, 375, 0, 0, 255, 1]], [[6, 0, 0, 450, 0, 0, 255, 1], [10, 0, 0, 450, 0, 0, 255, 1], [4, 0, 0, 375, 0, 0, 255, 1]], [[7, 0, 0, 450, 0, 0, 255, 1], [11, 0, 0, 450, 0, 0, 200, 1], [4, 0, 0, 270, 0, 0, 255, 1]], [[8, 0, 0, 450, 0, 0, 255, 1], [12, 0, 0, 450, 0, 0, 150, 1], [4, 0, 0, 150, 0, 0, 255, 1]], [[5, 0, 0, 450, 0, 0, 100, 1], [9, 0, 0, 450, 0, 0, 100, 1]]], "name": "Darkness All 2", "position": 3, "timings": [{"flashColor": [0, 0, 0, 255], "flashDuration": 10, "flashScope": 1, "frame": 0, "se": {"name": "Teleport", "pan": 0, "pitch": 100, "volume": 100}}, {"conditions": 0, "flashColor": [255, 136, 255, 170], "flashDuration": 5, "flashScope": 2, "frame": 9, "se": null}, {"flashColor": [255, 119, 238, 204], "flashDuration": 3, "flashScope": 1, "frame": 14, "se": {"name": "Blind", "pan": 0, "pitch": 100, "volume": 80}}, {"conditions": 0, "flashColor": [255, 136, 255, 170], "flashDuration": 5, "flashScope": 2, "frame": 14, "se": null}, {"flashColor": [255, 119, 238, 170], "flashDuration": 3, "flashScope": 1, "frame": 17, "se": null}, {"flashColor": [255, 119, 238, 170], "flashDuration": 3, "flashScope": 1, "frame": 20, "se": null}, {"flashColor": [255, 119, 238, 170], "flashDuration": 3, "flashScope": 1, "frame": 23, "se": null}, {"flashColor": [255, 119, 238, 170], "flashDuration": 8, "flashScope": 1, "frame": 26, "se": null}]}, {"id": 105, "animation1Hue": 0, "animation1Name": "PreSpecial3", "animation2Hue": 0, "animation2Name": "Darkness5", "frames": [[[0, 0, -72, 330, 0, 0, 255, 0]], [[1, 0, -72, 330, 0, 0, 255, 0]], [[2, 0, -72, 330, 0, 0, 255, 0]], [[3, 0, -72, 330, 0, 0, 255, 0]], [[4, 0, -72, 330, 0, 0, 255, 0], [17, 0, -72, 390, 0, 0, 150, 0]], [[5, 0, -72, 330, 0, 0, 255, 0], [18, 0, -72, 345, 0, 0, 255, 0]], [[6, 0, -72, 330, 0, 0, 255, 0], [19, 0, -72, 345, 0, 0, 255, 0]], [[20, 0, -72, 345, 0, 0, 255, 0], [7, 0, -72, 330, 0, 0, 255, 0]], [[8, 0, -72, 330, 0, 0, 255, 0], [22, 0, -84, 420, 0, 0, 255, 0]], [[9, 0, -72, 330, 0, 0, 255, 0], [23, 0, -84, 420, 0, 0, 255, 0]], [[10, 0, -72, 330, 0, 0, 255, 0], [24, 0, -84, 420, 0, 0, 255, 0]], [[11, 0, -72, 330, 0, 0, 255, 0], [25, 0, -84, 450, 0, 0, 255, 0], [100, -24, -84, 315, 0, 0, 50, 0]], [[12, 0, -72, 330, 0, 0, 255, 0], [20, 0, -84, 300, 0, 0, 200, 0], [22, 0, -84, 375, 0, 0, 255, 0], [26, 0, -84, 450, 0, 0, 255, 0], [100, -24, -84, 315, 0, 0, 100, 0]], [[13, 0, -72, 330, 0, 0, 255, 0], [26, 0, -84, 465, 0, 0, 200, 0], [125, 0, 72, 525, 0, 0, 50, 0], [100, -24, -84, 315, 0, 0, 150, 0]], [[14, 0, -72, 330, 0, 0, 255, 0], [26, 0, -84, 480, 0, 0, 180, 0], [125, 0, 72, 525, 0, 0, 100, 0], [100, -24, -84, 315, 0, 0, 150, 0]], [[15, 0, -72, 330, 0, 0, 255, 0], [26, 0, -84, 487, 0, 0, 150, 0], [125, 0, 72, 525, 0, 0, 150, 0], [100, -24, -84, 315, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 255, 0], [26, 0, -84, 495, 0, 0, 120, 0], [125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 150, 0]], [[26, 0, -84, 502, 0, 0, 100, 0], [16, 0, -72, 330, 0, 0, 200, 0], [125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 150, 0]], [[16, 0, -72, 330, 0, 0, 150, 0], [26, 0, -84, 502, 0, 0, 70, 0], [125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 140, 0]], [[16, 0, -72, 330, 0, 0, 100, 0], [26, 0, -84, 502, 0, 0, 40, 0], [125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 130, 0], [101, 0, -132, 150, 0, 0, 255, 0]], [[16, 0, -72, 330, 0, 0, 50, 0], [125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 120, 0], [101, 0, -156, 210, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 100, 0], [101, 0, -168, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 80, 0], [101, 0, -165, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 60, 0], [101, 0, -161, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 40, 0], [101, 0, -156, 240, 0, 0, 255, 0]], [[125, 0, 72, 525, 0, 0, 180, 0], [100, -24, -84, 315, 0, 0, 20, 0], [102, 0, -144, 300, 0, 0, 255, 0]], [[103, 0, -132, 375, 0, 0, 255, 0]], [[104, 0, -120, 450, 0, 0, 255, 0]], [[105, 0, -108, 480, 0, 0, 255, 0]], [[106, 0, -96, 495, 0, 0, 255, 0]], [[107, 0, -96, 510, 0, 0, 255, 0]], [[108, 0, -96, 525, 0, 0, 255, 0]], [[109, 0, -96, 525, 0, 0, 255, 0]], [[111, 0, -96, 525, 0, 0, 255, 0]], [[112, 0, -96, 525, 0, 0, 255, 0]], [[113, 0, -96, 525, 0, 0, 255, 0]], [[114, 0, -96, 525, 0, 0, 255, 0]], [[115, 0, -96, 525, 0, 0, 255, 0]], [[116, 0, -96, 525, 0, 0, 255, 0]], [[117, 0, -96, 525, 0, 0, 255, 0]], [[118, 0, -96, 525, 0, 0, 255, 0]], [[119, 0, -96, 525, 0, 0, 255, 0]], [[120, 0, -96, 525, 0, 0, 255, 0]], [[121, 0, -96, 525, 0, 0, 255, 0]], [[122, 0, -96, 525, 0, 0, 255, 0]], [[123, 0, -96, 525, 0, 0, 255, 0]], [[124, 0, -96, 525, 0, 0, 255, 0]], [[110, 0, -96, 525, 0, 0, 255, 0]], [[111, 0, -96, 525, 0, 0, 255, 0]], [[112, 0, -96, 525, 0, 0, 255, 0]], [[113, 0, -96, 525, 0, 0, 255, 0]], [[114, 0, -96, 525, 0, 0, 255, 0]], [[115, 0, -96, 525, 0, 0, 255, 0]], [[116, 0, -96, 525, 0, 0, 255, 0]], [[117, 0, -96, 525, 0, 0, 255, 0]], [[118, 0, -96, 525, 0, 0, 255, 0]], [[119, 0, -96, 525, 0, 0, 255, 0]], [[120, 0, -96, 525, 0, 0, 255, 0]], [[121, 0, -96, 525, 0, 0, 200, 0]], [[122, 0, -96, 525, 0, 0, 150, 0]], [[123, 0, -96, 525, 0, 0, 100, 0]], [[124, 0, -96, 525, 0, 0, 50, 0]]], "name": "Darkness All 3", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 70, "volume": 65}}, {"flashColor": [255, 34, 0, 136], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [153, 51, 102, 153], "flashDuration": 10, "flashScope": 2, "frame": 9, "se": {"name": "Darkness4", "pan": 0, "pitch": 90, "volume": 75}}, {"flashColor": [187, 221, 170, 187], "flashDuration": 5, "flashScope": 0, "frame": 25, "se": {"name": "Magic2", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [170, 51, 136, 187], "flashDuration": 10, "flashScope": 1, "frame": 34, "se": {"name": "Wind1", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [51, 0, 34, 187], "flashDuration": 15, "flashScope": 2, "frame": 34, "se": {"name": "Thunder4", "pan": 0, "pitch": 70, "volume": 90}}, {"flashColor": [170, 51, 136, 187], "flashDuration": 10, "flashScope": 1, "frame": 46, "se": null}, {"flashColor": [51, 0, 34, 187], "flashDuration": 10, "flashScope": 2, "frame": 46, "se": null}]}, {"id": 106, "animation1Hue": 20, "animation1Name": "Magic1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 300, 0, 0, 255, 1]], [[1, 0, 0, 288, 0, 0, 255, 1]], [[2, 0, 0, 276, 0, 0, 255, 1]], [[3, 0, 0, 264, 0, 0, 255, 1], [10, 0, 0, 220, 0, 0, 80, 0]], [[4, 0, 0, 252, 0, 0, 255, 1], [6, 0, 0, 200, 0, 0, 138, 0]], [[5, 0, 0, 240, 0, 0, 255, 1], [7, 0, 0, 195, 0, 0, 197, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 150, 0, 0, 0, 0], [8, 0, 0, 203, 0, 0, 200, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 75, 0, 0, 0, 0], [9, 0, 0, 200, 0, 0, 255, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 0, 0, 0, 0, 0, 0, 0], [10, 0, 0, 200, 0, 0, 255, 0]], [[-1, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 200, 0, 0, 255, 0]], [[-1, 0, 0, 0, 0, 0, 0, 0], [7, 0, 0, 200, 0, 0, 255, 0]], [[-1, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 200, 0, 0, 255, 0]], [[-1, 0, 0, 0, 0, 0, 0, 0], [9, 0, 0, 200, 0, 0, 255, 0]], [[-1, 25, 20, 260, 0, 0, 60, 1], [-1, 10, 7.5, 180, -45, 0, 255, 1], [7, 0, 0, 200, 0, 0, 230, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[-1, 282.5, 30, 140, -45, 0, 255, 1], [-1, 40, 10, 220, 0, 0, 60, 1], [8, 0, 0, 180, 0, 0, 180, 0], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[11, 0, 0, 260, 0, 0, 255, 1], [9, 0, 0, 100, 0, 0, 139, 0], [-1, 295, 153, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[12, 0, 0, 200, 0, 0, 255, 1]], [[13, 0, 0, 220, 0, 0, 100, 1]]], "name": "Neutral One 1", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Darkness1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [221, 238, 255, 204], "flashDuration": 5, "flashScope": 2, "frame": 4, "se": {"name": "Flash2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [221, 238, 255, 187], "flashDuration": 10, "flashScope": 1, "frame": 6, "se": {"name": "Thunder6", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [221, 238, 255, 187], "flashDuration": 5, "flashScope": 1, "frame": 15, "se": {"name": "Ice2", "pan": 0, "pitch": 150, "volume": 90}}]}, {"id": 107, "animation1Hue": 0, "animation1Name": "Explosion1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 225, 0, 0, 255, 0]], [[1, 0, 0, 225, 0, 0, 255, 0]], [[2, 0, 0, 345, 0, 0, 255, 0]], [[3, 0, 0, 345, 0, 0, 255, 0]], [[4, 0, 0, 345, 0, 0, 255, 0]], [[5, 0, 0, 345, 0, 0, 255, 0]], [[6, 0, 0, 352, 0, 0, 255, 0]], [[7, 0, 0, 360, 0, 0, 255, 0]], [[8, 0, 0, 360, 0, 0, 255, 0]], [[9, 0, 0, 360, 0, 0, 255, 0]], [[10, 0, 0, 360, 0, 0, 255, 0]], [[10, 0, 0, 367, 0, 0, 200, 0]]], "name": "Neutral One 2", "position": 1, "timings": [{"flashColor": [255, 255, 119, 170], "flashDuration": 5, "flashScope": 2, "frame": 0, "se": {"name": "Explosion2", "pan": 0, "pitch": 105, "volume": 100}}, {"flashColor": [255, 255, 102, 170], "flashDuration": 5, "flashScope": 1, "frame": 1, "se": null}]}, {"id": 108, "animation1Hue": 0, "animation1Name": "Magic2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, -310, -20, 300, 0, 0, 100, 1]], [[1, -310, -20, 280, 0, 0, 200, 1]], [[2, -310, -20, 260, 0, 0, 255, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [0, 30, 45, 300, 0, 0, 100, 1]], [[3, -310, -20, 240, 0, 0, 255, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [1, 30, 45, 280, 0, 0, 200, 1]], [[4, -310, -20, 220, 0, 0, 255, 1], [6, -310, -20, 280, 0, 0, 80, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, 30, 45, 260, 0, 0, 255, 1], [-1, -408, 309.5, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [0, -105, -160, 300, 0, 0, 100, 1]], [[5, -310, -20, 220, 0, 0, 255, 1], [7, -310, -20, 269, 0, 0, 138, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [3, 30, 45, 240, 0, 0, 255, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [1, -105, -160, 280, 0, 0, 200, 1]], [[0, -310, -20, 220, 0, 0, 255, 1], [8, -310, -20, 258, 0, 0, 197, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [4, 30, 45, 220, 0, 0, 255, 1], [6, 30, 45, 280, 0, 0, 80, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, -105, -160, 260, 0, 0, 255, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [0, 250, -110, 300, 0, 0, 100, 1]], [[1, -310, -20, 220, 0, 0, 255, 1], [9, -310, -20, 247, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [5, 30, 45, 220, 0, 0, 255, 1], [7, 30, 45, 269, 0, 0, 138, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [3, -105, -160, 240, 0, 0, 255, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [1, 250, -110, 280, 0, 0, 200, 1]], [[2, -310, -20, 220, 0, 0, 255, 1], [10, -310, -20, 236, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [0, 30, 45, 220, 0, 0, 255, 1], [8, 30, 45, 258, 0, 0, 197, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [4, -105, -160, 220, 0, 0, 255, 1], [6, -105, -160, 280, 0, 0, 80, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, 250, -110, 260, 0, 0, 255, 1]], [[3, -310, -20, 220, 0, 0, 255, 1], [6, -310, -20, 225, 0, 0, 255, 0], [-1, -408, 309.5, 100, 0, 0, 0, 1], [1, 30, 45, 220, 0, 0, 255, 1], [9, 30, 45, 247, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [5, -105, -160, 220, 0, 0, 255, 1], [7, -105, -160, 269, 0, 0, 138, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [3, 250, -110, 240, 0, 0, 255, 1]], [[4, -310, -20, 220, 0, 0, 255, 1], [7, -310, -20, 215, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, 30, 45, 220, 0, 0, 255, 1], [10, 30, 45, 236, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [0, -105, -160, 220, 0, 0, 255, 1], [8, -105, -160, 258, 0, 0, 197, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [4, 250, -110, 220, 0, 0, 255, 1], [6, 250, -110, 280, 0, 0, 80, 0]], [[5, -310, -20, 220, 0, 0, 255, 1], [8, -310, -20, 204, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [3, 30, 45, 220, 0, 0, 255, 1], [6, 30, 45, 225, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [1, -105, -160, 220, 0, 0, 255, 1], [9, -105, -160, 247, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [5, 250, -110, 220, 0, 0, 255, 1], [7, 250, -110, 269, 0, 0, 138, 0]], [[0, -310, -20, 220, 0, 0, 255, 1], [9, -310, -20, 193, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [4, 30, 45, 220, 0, 0, 255, 1], [7, 30, 45, 215, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, -105, -160, 220, 0, 0, 255, 1], [10, -105, -160, 236, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [0, 250, -110, 220, 0, 0, 255, 1], [8, 250, -110, 258, 0, 0, 197, 0]], [[1, -310, -20, 220, 0, 0, 255, 1], [10, -310, -20, 182, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [5, 30, 45, 220, 0, 0, 255, 1], [8, 30, 45, 204, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [3, -105, -160, 220, 0, 0, 255, 1], [6, -105, -160, 225, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [1, 250, -110, 220, 0, 0, 255, 1], [9, 250, -110, 247, 0, 0, 255, 0]], [[2, -310, -20, 220, 0, 0, 255, 1], [6, -310, -20, 171, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [0, 30, 45, 220, 0, 0, 255, 1], [9, 30, 45, 193, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [4, -105, -160, 220, 0, 0, 255, 1], [7, -105, -160, 215, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, 250, -110, 220, 0, 0, 255, 1], [10, 250, -110, 236, 0, 0, 255, 0]], [[3, -310, -20, 220, 0, 0, 255, 1], [7, -310, -20, 160, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [1, 30, 45, 220, 0, 0, 255, 1], [10, 30, 45, 182, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [5, -105, -160, 220, 0, 0, 255, 1], [8, -105, -160, 204, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [3, 250, -110, 220, 0, 0, 255, 1], [6, 250, -110, 225, 0, 0, 255, 0]], [[4, -310, -20, 220, 0, 0, 255, 1], [8, -310, -20, 100, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, 30, 45, 220, 0, 0, 255, 1], [6, 30, 45, 171, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [0, -105, -160, 220, 0, 0, 255, 1], [9, -105, -160, 193, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [4, 250, -110, 220, 0, 0, 255, 1], [7, 250, -110, 215, 0, 0, 255, 0]], [[5, -310, -20, 220, 0, 0, 255, 1], [9, -310, -20, 220, 0, 0, 180, 0], [11, -310, -20, 200, 0, 0, 255, 0], [3, 30, 45, 220, 0, 0, 255, 1], [7, 30, 45, 160, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [1, -105, -160, 220, 0, 0, 255, 1], [10, -105, -160, 182, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [5, 250, -110, 220, 0, 0, 255, 1], [8, 250, -110, 204, 0, 0, 255, 0]], [[10, -310, -20, 220, 0, 0, 0, 1], [10, -310, -20, 300, 0, 0, 100, 0], [12, -310, -20, 200, 0, 0, 255, 0], [4, 30, 45, 220, 0, 0, 255, 1], [8, 30, 45, 100, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, -105, -160, 220, 0, 0, 255, 1], [6, -105, -160, 171, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [0, 250, -110, 220, 0, 0, 255, 1], [9, 250, -110, 193, 0, 0, 255, 0]], [[-1, -408, 309.5, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [15, -310, -20, 240, 0, 0, 255, 0], [5, 30, 45, 220, 0, 0, 255, 1], [9, 30, 45, 220, 0, 0, 180, 0], [11, 30, 45, 200, 0, 0, 255, 0], [3, -105, -160, 220, 0, 0, 255, 1], [7, -105, -160, 160, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [1, 250, -110, 220, 0, 0, 255, 1], [10, 250, -110, 182, 0, 0, 255, 0]], [[-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [16, -310, -20, 260, 0, 0, 255, 0], [10, 30, 45, 220, 0, 0, 0, 1], [10, 30, 45, 300, 0, 0, 100, 0], [12, 30, 45, 200, 0, 0, 255, 0], [4, -105, -160, 220, 0, 0, 255, 1], [8, -105, -160, 100, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [2, 250, -110, 220, 0, 0, 255, 1], [6, 250, -110, 171, 0, 0, 255, 0]], [[-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [17, -310, -20, 280, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [15, 30, 45, 240, 0, 0, 255, 0], [5, -105, -160, 220, 0, 0, 255, 1], [9, -105, -160, 220, 0, 0, 180, 0], [11, -105, -160, 200, 0, 0, 255, 0], [3, 250, -110, 220, 0, 0, 255, 1], [7, 250, -110, 160, 0, 0, 255, 0]], [[-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [18, -310, -20, 280, 0, 0, 255, 0], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 287, 100, 0, 0, 0, 1], [16, 30, 45, 260, 0, 0, 255, 0], [10, -105, -160, 220, 0, 0, 0, 1], [10, -105, -160, 300, 0, 0, 100, 0], [12, -105, -160, 200, 0, 0, 255, 0], [4, 250, -110, 220, 0, 0, 255, 1], [8, 250, -110, 100, 0, 0, 255, 0]], [[-1, -408, 174.5, 100, 0, 0, 0, 1], [-1, -408, 209.5, 100, 0, 0, 0, 1], [-1, -408, 189.5, 100, 0, 0, 0, 1], [-1, -408, 194.5, 100, 0, 0, 0, 1], [-1, -408, 199.5, 100, 0, 0, 0, 1], [17, 30, 45, 280, 0, 0, 255, 0], [-1, -408, 202, 100, 0, 0, 0, 1], [-1, -408, 167, 100, 0, 0, 0, 1], [15, -105, -160, 240, 0, 0, 255, 0], [5, 250, -110, 220, 0, 0, 255, 1], [9, 250, -110, 220, 0, 0, 180, 0], [11, 250, -110, 200, 0, 0, 255, 0]], [[-1, -408, 142, 100, 0, 0, 0, 1], [-1, -408, 187, 100, 0, 0, 0, 1], [-1, -408, 149.5, 100, 0, 0, 0, 1], [-1, -408, 157, 100, 0, 0, 0, 1], [-1, -408, 159.5, 100, 0, 0, 0, 1], [18, 30, 45, 280, 0, 0, 255, 0], [-1, -408, 184.5, 100, 0, 0, 0, 1], [-1, -408, 224.5, 100, 0, 0, 0, 1], [16, -105, -160, 260, 0, 0, 255, 0], [10, 250, -110, 220, 0, 0, 0, 1], [10, 250, -110, 300, 0, 0, 100, 0], [12, 250, -110, 200, 0, 0, 255, 0]], [[-1, -408, 184.5, 100, 0, 0, 0, 1], [-1, -408, 194.5, 100, 0, 0, 0, 1], [-1, -408, 142, 100, 0, 0, 0, 1], [-1, -408, 142, 100, 0, 0, 0, 1], [-1, -408, 139.5, 100, 0, 0, 0, 1], [-1, -408, 124.5, 100, 0, 0, 0, 1], [-1, -408, 134.5, 100, 0, 0, 0, 1], [-1, -408, 124.5, 100, 0, 0, 0, 1], [17, -105, -160, 280, 0, 0, 255, 0], [-1, -408, 137, 100, 0, 0, 0, 1], [-1, -408, 162, 100, 0, 0, 0, 1], [15, 250, -110, 240, 0, 0, 255, 0]], [[-1, -408, 144.5, 100, 0, 0, 0, 1], [-1, -408, 157, 100, 0, 0, 0, 1], [-1, -408, 139.5, 100, 0, 0, 0, 1], [-1, -408, 147, 100, 0, 0, 0, 1], [-1, -408, 139.5, 100, 0, 0, 0, 1], [-1, -408, 149.5, 100, 0, 0, 0, 1], [-1, -408, 152, 100, 0, 0, 0, 1], [-1, -408, 139.5, 100, 0, 0, 0, 1], [18, -105, -160, 280, 0, 0, 255, 0], [-1, -408, 117, 100, 0, 0, 0, 1], [-1, -408, 94.5, 100, 0, 0, 0, 1], [16, 250, -110, 260, 0, 0, 255, 0]], [[-1, -408, -3, 100, 0, 0, 0, 1], [-1, -408, 4.5, 100, 0, 0, 0, 1], [-1, -408, 24.5, 100, 0, 0, 0, 1], [-1, -408, 2, 100, 0, 0, 0, 1], [-1, -408, -20.5, 100, 0, 0, 0, 1], [-1, -408, 17, 100, 0, 0, 0, 1], [-1, -408, 42, 100, 0, 0, 0, 1], [-1, -408, 39.5, 100, 0, 0, 0, 1], [-1, -408, 57, 100, 0, 0, 0, 1], [-1, -408, 47, 100, 0, 0, 0, 1], [-1, -408, 34.5, 100, 0, 0, 0, 1], [17, 250, -110, 280, 0, 0, 255, 0]], [[-1, -408, 44.5, 100, 0, 0, 0, 1], [-1, -390.5, 49.5, 100, 0, 0, 0, 1], [-1, -408, 54.5, 100, 0, 0, 0, 1], [-1, -408, 52, 100, 0, 0, 0, 1], [-1, -408, 92, 100, 0, 0, 0, 1], [-1, -408, 84.5, 100, 0, 0, 0, 1], [-1, -368, 62, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [-1, -408, 312, 100, 0, 0, 0, 1], [18, 250, -110, 280, 0, 0, 255, 0]]], "name": "Neutral All 1", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Wind2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Evasion2", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 4, "se": {"name": "Wind2", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [221, 238, 255, 255], "flashDuration": 5, "flashScope": 2, "frame": 18, "se": {"name": "Explosion1", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [221, 238, 255, 170], "flashDuration": 5, "flashScope": 2, "frame": 20, "se": null}, {"flashColor": [255, 221, 170, 255], "flashDuration": 5, "flashScope": 1, "frame": 19, "se": null}, {"flashColor": [255, 221, 170, 187], "flashDuration": 5, "flashScope": 1, "frame": 21, "se": {"name": "Explosion2", "pan": 0, "pitch": 120, "volume": 90}}, {"flashColor": [255, 221, 170, 187], "flashDuration": 5, "flashScope": 1, "frame": 23, "se": null}]}, {"id": 109, "animation1Hue": 0, "animation1Name": "Explosion2", "animation2Hue": 0, "animation2Name": "Explosion1", "frames": [[[0, 0, 0, 450, 0, 0, 255, 1]], [[1, 0, 0, 450, 0, 0, 255, 1]], [[2, 0, 0, 525, 0, 0, 255, 1]], [[3, 0, 0, 525, 0, 0, 255, 1]], [[4, 0, 0, 495, 0, 0, 255, 1]], [[5, 0, 0, 405, 0, 0, 255, 1]], [[6, 0, 0, 405, 0, 0, 255, 1]], [[7, 0, 0, 405, 0, 0, 255, 1]], [[8, 0, 0, 405, 0, 0, 255, 1]], [[9, 0, 0, 405, 0, 0, 255, 1]], [[10, 0, 0, 405, 0, 0, 255, 1]], [[11, 0, 0, 405, 0, 0, 255, 1]], [[12, 0, 0, 450, 0, 0, 255, 1]], [[13, 0, 0, 450, 0, 0, 255, 1], [24, 0, 0, 450, 0, 0, 255, 1], [100, -300, 12, 300, 0, 0, 255, 1], [100, 312, -48, 300, 0, 0, 255, 1]], [[14, 0, 0, 450, 0, 0, 255, 1], [25, 0, 0, 450, 0, 0, 255, 1], [101, -300, 12, 300, 0, 0, 255, 1], [101, 312, -48, 300, 0, 0, 255, 1]], [[15, 0, 0, 450, 0, 0, 255, 1], [26, 0, 0, 450, 0, 0, 255, 1], [102, -300, 12, 300, 0, 0, 255, 1], [102, 312, -48, 300, 0, 0, 255, 1]], [[16, 0, 0, 450, 0, 0, 255, 1], [27, 0, 0, 450, 0, 0, 255, 1], [103, -300, 12, 300, 0, 0, 255, 1], [103, 312, -48, 300, 0, 0, 255, 1]], [[17, 0, 0, 450, 0, 0, 255, 1], [28, 0, 0, 450, 0, 0, 255, 1], [104, -300, 12, 300, 0, 0, 255, 1], [104, 312, -48, 300, 0, 0, 255, 1], [100, 12, 72, 300, 0, 0, 255, 1]], [[18, 0, 0, 450, 0, 0, 255, 1], [29, 0, 0, 450, 0, 0, 255, 1], [105, -300, 12, 300, 0, 0, 255, 1], [105, 312, -48, 300, 0, 0, 255, 1], [101, 12, 72, 300, 0, 0, 255, 1]], [[19, 0, 0, 450, 0, 0, 255, 1], [106, -300, 12, 300, 0, 0, 200, 1], [106, 312, -48, 300, 0, 0, 200, 1], [102, 12, 72, 300, 0, 0, 255, 1]], [[20, 0, 0, 450, 0, 0, 255, 1], [107, -300, 12, 300, 0, 0, 150, 1], [107, 312, -48, 300, 0, 0, 150, 1], [103, 12, 72, 300, 0, 0, 255, 1]], [[22, 0, 0, 450, 0, 0, 200, 1], [108, -300, 12, 300, 0, 0, 100, 1], [108, 312, -48, 300, 0, 0, 100, 1], [104, 12, 72, 300, 0, 0, 255, 1]], [[23, 0, 0, 450, 0, 0, 150, 1], [105, 12, 72, 300, 0, 0, 255, 1]], [[23, 0, 0, 465, 0, 0, 100, 1], [106, 12, 72, 300, 0, 0, 200, 1]], [[23, 0, 0, 472, 0, 0, 50, 1], [107, 12, 72, 300, 0, 0, 150, 1]], [[108, 12, 72, 300, 0, 0, 100, 1]]], "name": "Neutral All 2", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Sand", "pan": 0, "pitch": 100, "volume": 100}}, {"flashColor": [255, 255, 187, 187], "flashDuration": 5, "flashScope": 1, "frame": 1, "se": {"name": "Flash2", "pan": 0, "pitch": 120, "volume": 80}}, {"conditions": 0, "flashColor": [255, 238, 221, 136], "flashDuration": 6, "flashScope": 2, "frame": 2, "se": null}, {"flashColor": [255, 221, 170, 187], "flashDuration": 2, "flashScope": 1, "frame": 10, "se": {"name": "Explosion2", "pan": 0, "pitch": 110, "volume": 100}}, {"conditions": 1, "flashColor": [255, 255, 221, 221], "flashDuration": 3, "flashScope": 2, "frame": 11, "se": null}, {"flashColor": [255, 221, 170, 187], "flashDuration": 3, "flashScope": 1, "frame": 13, "se": null}, {"flashColor": [255, 221, 170, 187], "flashDuration": 10, "flashScope": 1, "frame": 16, "se": null}]}, {"id": 110, "animation1Hue": 0, "animation1Name": "Meteor", "animation2Hue": 0, "animation2Name": "Gun2", "frames": [[[0, 240, -192, 225, 0, 0, 100, 0]], [[0, 240, -192, 195, 0, 0, 150, 0]], [[0, 240, -192, 180, 0, 0, 180, 0]], [[0, 240, -192, 165, 0, 0, 200, 0]], [[0, 240, -192, 157, 0, 0, 200, 0], [2, -240, 0, 225, 0, 0, 100, 0]], [[0, 240, -192, 150, 0, 0, 200, 0], [2, -240, 0, 195, 0, 0, 150, 0]], [[0, 240, -192, 150, 0, 0, 150, 0], [2, -240, 0, 180, 0, 0, 180, 0]], [[0, 240, -192, 150, 0, 0, 100, 0], [2, -240, 0, 165, 0, 0, 200, 0]], [[0, 240, -192, 150, 0, 0, 50, 0], [2, -240, 0, 157, 0, 0, 200, 0], [0, -240, -192, 225, 0, 0, 100, 0]], [[0, 240, -192, 150, 0, 0, 20, 0], [2, -240, 0, 150, 0, 0, 200, 0], [0, -240, -192, 195, 0, 0, 150, 0]], [[2, -240, 0, 150, 0, 0, 150, 0], [0, -240, -192, 180, 0, 0, 180, 0]], [[2, -240, 0, 150, 0, 0, 100, 0], [0, -240, -192, 165, 0, 0, 200, 0]], [[2, -240, 0, 150, 0, 0, 50, 0], [0, -240, -192, 157, 0, 0, 200, 0], [2, 240, 0, 225, 0, 0, 100, 0]], [[2, -240, 0, 150, 0, 0, 20, 0], [0, -240, -192, 150, 0, 0, 200, 0], [2, 240, 0, 195, 0, 0, 150, 0]], [[0, -240, -192, 150, 0, 0, 150, 0], [2, 240, 0, 180, 0, 0, 180, 0]], [[0, -240, -192, 150, 0, 0, 100, 0], [2, 240, 0, 165, 0, 0, 200, 0]], [[0, -240, -192, 150, 0, 0, 50, 0], [2, 240, 0, 157, 0, 0, 200, 0], [1, 0, -96, 300, 0, 0, 100, 0]], [[0, -240, -192, 150, 0, 0, 20, 0], [2, 240, 0, 150, 0, 0, 200, 0], [1, 0, -96, 270, 0, 0, 150, 0]], [[2, 240, 0, 150, 0, 0, 150, 0], [1, 0, -96, 255, 0, 0, 180, 0]], [[2, 240, 0, 150, 0, 0, 100, 0], [1, 0, -96, 240, 0, 0, 200, 0]], [[2, 240, 0, 150, 0, 0, 50, 0], [1, 0, -96, 232, 0, 0, 200, 0]], [[2, 240, 0, 150, 0, 0, 20, 0], [1, 0, -96, 228, 0, 0, 200, 0]], [[1, 0, -96, 225, 0, 0, 200, 0]], [[1, 0, -96, 225, 0, 0, 200, 0]], [[1, 0, -96, 225, 0, 0, 255, 0], [103, 0, -96, 300, 0, 0, 255, 1], [1, 0, -96, 225, 0, 0, 150, 1]], [[1, 0, -96, 225, 0, 0, 255, 0], [1, 0, -96, 270, 0, 0, 200, 1], [104, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 285, 0, 0, 150, 1], [105, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 292, 0, 0, 100, 0], [106, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 300, 0, 0, 60, 0], [107, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 304, 0, 0, 30, 0], [108, 0, -96, 427, 0, 0, 255, 1]], [[109, 0, -96, 427, 0, 0, 255, 1]], [[110, 0, -96, 427, 0, 0, 255, 1]], [[111, 0, -96, 427, 0, 0, 255, 1]], [[112, 0, -96, 427, 0, 0, 255, 1]], [[112, 0, -96, 420, 0, 0, 150, 1], [3, 156, -84, 240, 0, 0, 255, 0]], [[112, 0, -96, 412, 0, 0, 50, 1], [4, 156, -84, 240, 0, 0, 255, 0]], [[5, 156, -84, 240, 0, 0, 255, 0], [3, -168, -96, 240, 0, 0, 255, 0], [15, 120, 84, 300, 0, 0, 255, 1]], [[6, 156, -84, 240, 0, 0, 255, 0], [4, -168, -96, 240, 0, 0, 255, 0], [16, 120, 84, 300, 0, 0, 255, 1]], [[7, 156, -84, 240, 0, 0, 255, 0], [5, -168, -96, 240, 0, 0, 255, 0], [3, 240, -84, 270, 0, 0, 255, 0], [17, 120, 84, 300, 0, 0, 255, 1], [15, -204, 72, 300, 0, 0, 255, 1]], [[8, 156, -84, 240, 0, 0, 255, 0], [6, -168, -96, 240, 0, 0, 255, 0], [4, 240, -84, 270, 0, 0, 255, 0], [3, -336, -84, 270, 0, 0, 255, 0], [18, 120, 84, 300, 0, 0, 255, 1], [16, -204, 72, 300, 0, 0, 255, 1]], [[9, 156, -84, 240, 0, 0, 255, 0], [7, -168, -96, 240, 0, 0, 255, 0], [5, 240, -84, 270, 0, 0, 255, 0], [4, -336, -84, 270, 0, 0, 255, 0], [19, 120, 84, 300, 0, 0, 255, 1], [17, -204, 72, 300, 0, 0, 255, 1], [15, 192, 96, 330, 0, 0, 255, 1]], [[10, 156, -84, 240, 0, 0, 255, 0], [8, -168, -96, 240, 0, 0, 255, 0], [6, 240, -84, 270, 0, 0, 255, 0], [5, -336, -84, 270, 0, 0, 255, 0], [20, 120, 84, 300, 0, 0, 255, 1], [18, -204, 72, 300, 0, 0, 255, 1], [15, -384, 96, 330, 0, 0, 255, 1], [16, 192, 96, 330, 0, 0, 255, 1]], [[11, 156, -84, 240, 0, 0, 255, 0], [9, -168, -96, 240, 0, 0, 255, 0], [7, 240, -84, 270, 0, 0, 255, 0], [6, -336, -84, 270, 0, 0, 255, 0], [21, 120, 84, 300, 0, 0, 255, 1], [19, -204, 72, 300, 0, 0, 255, 1], [16, -384, 96, 330, 0, 0, 255, 1], [17, 192, 96, 330, 0, 0, 255, 1]], [[12, 156, -84, 240, 0, 0, 255, 0], [10, -168, -96, 240, 0, 0, 255, 0], [8, 240, -84, 270, 0, 0, 255, 0], [7, -336, -84, 270, 0, 0, 255, 0], [20, -204, 72, 300, 0, 0, 255, 1], [17, -384, 96, 330, 0, 0, 255, 1], [18, 192, 96, 330, 0, 0, 255, 1]], [[13, 156, -84, 240, 0, 0, 255, 0], [11, -168, -96, 240, 0, 0, 255, 0], [9, 240, -84, 270, 0, 0, 255, 0], [8, -336, -84, 270, 0, 0, 255, 0], [3, -12, -108, 300, 0, 0, 255, 0], [21, -204, 72, 300, 0, 0, 255, 1], [18, -384, 96, 330, 0, 0, 255, 1], [19, 192, 96, 330, 0, 0, 255, 1]], [[14, 156, -84, 240, 0, 0, 255, 0], [12, -168, -96, 240, 0, 0, 255, 0], [10, 240, -84, 270, 0, 0, 255, 0], [9, -336, -84, 270, 0, 0, 255, 0], [4, -12, -108, 300, 0, 0, 255, 0], [19, -384, 96, 330, 0, 0, 255, 1], [20, 192, 96, 330, 0, 0, 255, 1]], [[13, -168, -96, 240, 0, 0, 255, 0], [11, 240, -84, 270, 0, 0, 255, 0], [10, -336, -84, 270, 0, 0, 255, 0], [5, -12, -108, 300, 0, 0, 255, 0], [20, -384, 96, 330, 0, 0, 255, 1], [21, 192, 96, 330, 0, 0, 255, 1], [15, -72, 96, 360, 0, 0, 255, 1]], [[14, -168, -96, 240, 0, 0, 255, 0], [12, 240, -84, 270, 0, 0, 255, 0], [11, -336, -84, 270, 0, 0, 255, 0], [6, -12, -108, 300, 0, 0, 255, 0], [3, -252, -84, 240, 0, 0, 255, 0], [21, -384, 96, 330, 0, 0, 255, 1], [16, -72, 96, 360, 0, 0, 255, 1]], [[13, 240, -84, 270, 0, 0, 255, 0], [12, -336, -84, 270, 0, 0, 255, 0], [3, 108, -84, 240, 0, 0, 255, 0], [7, -12, -108, 300, 0, 0, 255, 0], [4, -252, -84, 240, 0, 0, 255, 0], [17, -72, 96, 360, 0, 0, 255, 1]], [[14, 240, -84, 270, 0, 0, 255, 0], [13, -336, -84, 270, 0, 0, 255, 0], [4, 108, -84, 240, 0, 0, 255, 0], [8, -12, -108, 300, 0, 0, 255, 0], [5, -252, -84, 240, 0, 0, 255, 0], [18, -72, 96, 360, 0, 0, 255, 1], [15, -300, 84, 300, 0, 0, 255, 1]], [[14, -336, -84, 270, 0, 0, 255, 0], [9, -12, -108, 300, 0, 0, 255, 0], [5, 108, -84, 240, 0, 0, 255, 0], [6, -252, -84, 240, 0, 0, 255, 0], [3, 312, -84, 270, 0, 0, 255, 0], [19, -72, 96, 360, 0, 0, 255, 1], [16, -300, 84, 300, 0, 0, 255, 1], [15, 72, 84, 300, 0, 0, 255, 1]], [[10, -12, -108, 300, 0, 0, 255, 0], [6, 108, -84, 240, 0, 0, 255, 0], [7, -252, -84, 240, 0, 0, 255, 0], [4, 312, -84, 270, 0, 0, 255, 0], [20, -72, 96, 360, 0, 0, 255, 1], [17, -300, 84, 300, 0, 0, 255, 1], [16, 72, 84, 300, 0, 0, 255, 1]], [[11, -12, -108, 300, 0, 0, 255, 0], [8, -252, -84, 240, 0, 0, 255, 0], [7, 108, -84, 240, 0, 0, 255, 0], [5, 312, -84, 270, 0, 0, 255, 0], [3, -192, -108, 300, 0, 0, 255, 0], [21, -72, 96, 360, 0, 0, 255, 1], [18, -300, 84, 300, 0, 0, 255, 1], [17, 72, 84, 300, 0, 0, 255, 1], [15, 264, 96, 330, 0, 0, 255, 1]], [[12, -12, -108, 300, 0, 0, 255, 0], [9, -252, -84, 240, 0, 0, 255, 0], [6, 312, -84, 270, 0, 0, 255, 0], [8, 108, -84, 240, 0, 0, 255, 0], [4, -192, -108, 300, 0, 0, 255, 0], [3, -408, -84, 240, 0, 0, 255, 0], [19, -300, 84, 300, 0, 0, 255, 1], [18, 72, 84, 300, 0, 0, 255, 1], [16, 264, 96, 330, 0, 0, 255, 1]], [[13, -12, -108, 300, 0, 0, 255, 0], [10, -252, -84, 240, 0, 0, 255, 0], [7, 312, -84, 270, 0, 0, 255, 0], [9, 108, -84, 240, 0, 0, 255, 0], [5, -192, -108, 300, 0, 0, 255, 0], [4, -408, -84, 240, 0, 0, 255, 0], [20, -300, 84, 300, 0, 0, 255, 1], [19, 72, 84, 300, 0, 0, 255, 1], [17, 264, 96, 330, 0, 0, 255, 1], [15, -252, 96, 360, 0, 0, 255, 1]], [[14, -12, -108, 300, 0, 0, 255, 0], [11, -252, -84, 240, 0, 0, 255, 0], [8, 312, -84, 270, 0, 0, 255, 0], [10, 108, -84, 240, 0, 0, 255, 0], [6, -192, -108, 300, 0, 0, 255, 0], [5, -408, -84, 240, 0, 0, 255, 0], [21, -300, 84, 300, 0, 0, 255, 1], [20, 72, 84, 300, 0, 0, 255, 1], [18, 264, 96, 330, 0, 0, 255, 1], [16, -252, 96, 360, 0, 0, 255, 1]], [[12, -252, -84, 240, 0, 0, 255, 0], [9, 312, -84, 270, 0, 0, 255, 0], [11, 108, -84, 240, 0, 0, 255, 0], [7, -192, -108, 300, 0, 0, 255, 0], [6, -408, -84, 240, 0, 0, 255, 0], [21, 72, 84, 300, 0, 0, 255, 1], [19, 264, 96, 330, 0, 0, 255, 1], [17, -252, 96, 360, 0, 0, 255, 1]], [[13, -252, -84, 240, 0, 0, 255, 0], [10, 312, -84, 270, 0, 0, 255, 0], [12, 108, -84, 240, 0, 0, 255, 0], [8, -192, -108, 300, 0, 0, 255, 0], [7, -408, -84, 240, 0, 0, 255, 0], [20, 264, 96, 330, 0, 0, 255, 1], [18, -252, 96, 360, 0, 0, 255, 1]], [[14, -252, -84, 240, 0, 0, 255, 0], [11, 312, -84, 270, 0, 0, 255, 0], [9, -192, -108, 300, 0, 0, 255, 0], [13, 108, -84, 240, 0, 0, 255, 0], [8, -408, -84, 240, 0, 0, 255, 0], [21, 264, 96, 330, 0, 0, 255, 1], [19, -252, 96, 360, 0, 0, 255, 1]], [[12, 312, -84, 270, 0, 0, 255, 0], [10, -192, -108, 300, 0, 0, 255, 0], [14, 108, -84, 240, 0, 0, 255, 0], [9, -408, -84, 240, 0, 0, 255, 0], [20, -252, 96, 360, 0, 0, 255, 1]], [[13, 312, -84, 270, 0, 0, 255, 0], [11, -192, -108, 300, 0, 0, 255, 0], [10, -408, -84, 240, 0, 0, 255, 0], [21, -252, 96, 360, 0, 0, 255, 1]], [[14, 312, -84, 270, 0, 0, 255, 0], [12, -192, -108, 300, 0, 0, 255, 0], [11, -408, -84, 240, 0, 0, 255, 0]], [[13, -192, -108, 300, 0, 0, 255, 0], [12, -408, -84, 240, 0, 0, 255, 0]], [[14, -192, -108, 300, 0, 0, 255, 0], [13, -408, -84, 240, 0, 0, 255, 0]], [[14, -408, -84, 240, 0, 0, 255, 0]]], "name": "Neutral All 3", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Thunder7", "pan": 0, "pitch": 80, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 23, "se": {"name": "Battle3", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 221, 187], "flashDuration": 8, "flashScope": 2, "frame": 24, "se": null}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 34, "se": {"name": "Explosion1", "pan": 0, "pitch": 90, "volume": 90}}, {"flashColor": [255, 221, 102, 204], "flashDuration": 3, "flashScope": 2, "frame": 36, "se": null}, {"flashColor": [255, 187, 102, 204], "flashDuration": 5, "flashScope": 1, "frame": 37, "se": {"name": "Fire3", "pan": 0, "pitch": 130, "volume": 95}}, {"flashColor": [255, 102, 68, 221], "flashDuration": 5, "flashScope": 1, "frame": 40, "se": {"name": "Fire3", "pan": 0, "pitch": 130, "volume": 95}}, {"flashColor": [255, 221, 102, 204], "flashDuration": 3, "flashScope": 2, "frame": 41, "se": null}, {"flashColor": [255, 204, 0, 221], "flashDuration": 5, "flashScope": 1, "frame": 46, "se": {"name": "Fire3", "pan": 0, "pitch": 130, "volume": 95}}, {"flashColor": [255, 153, 51, 238], "flashDuration": 5, "flashScope": 1, "frame": 50, "se": {"name": "Fire3", "pan": 0, "pitch": 130, "volume": 95}}, {"flashColor": [255, 204, 102, 170], "flashDuration": 3, "flashScope": 2, "frame": 50, "se": null}, {"flashColor": [255, 221, 51, 204], "flashDuration": 3, "flashScope": 1, "frame": 52, "se": {"name": "Fire3", "pan": 0, "pitch": 130, "volume": 95}}, {"flashColor": [255, 170, 85, 153], "flashDuration": 3, "flashScope": 2, "frame": 54, "se": null}, {"flashColor": [255, 153, 119, 221], "flashDuration": 5, "flashScope": 1, "frame": 55, "se": {"name": "Fire3", "pan": 0, "pitch": 130, "volume": 95}}]}, {"id": 111, "animation1Hue": 0, "animation1Name": "Gun1", "animation2Hue": 0, "animation2Name": "", "frames": [[[11, 0, 0, 250, 0, 0, 255, 1], [1, 0, 0, 200, 0, 0, 200, 0], [0, 0, 0, 280, 0, 0, 255, 1]], [[17, 0, 0, 250, 0, 0, 255, 0], [12, 0, 0, 230, 0, 0, 255, 1], [1, 0, 0, 300, 0, 0, 255, 0]], [[2, 0, 0, 240, 0, 0, 255, 0], [13, 0, 0, 250, 0, 0, 255, 1], [18, 0, 0, 250, 0, 0, 255, 0]], [[2, 0, 0, 240, 0, 0, 255, 0], [14, 0, 0, 250, 0, 0, 255, 1], [19, 0, 0, 250, 0, 0, 255, 0]], [[2, 0, 0, 240, 0, 0, 255, 0], [15, 0, 0, 250, 0, 0, 255, 1], [20, 0, 0, 250, 0, 0, 255, 0]], [[2, 0, 0, 240, 0, 0, 255, 0], [16, 0, 0, 250, 0, 0, 255, 1]], [[2, 0, 0, 240, 0, 0, 255, 0]], [[2, 0, 0, 240, 0, 0, 255, 0]], [[2, 0, 0, 240, 0, 0, 255, 0]], [[4, 0, 0, 240, 0, 0, 255, 0]], [[5, 0, 0, 240, 0, 0, 255, 0]], [[7, 0, 0, 240, 0, 0, 255, 0]], [[8, 0, 0, 240, 0, 0, 188, 0]], [[10, 0, 0, 240, 0, 0, 100, 0]]], "name": "Shoot Normal", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Gun1", "pan": 0, "pitch": 110, "volume": 90}}, {"flashColor": [255, 238, 204, 119], "flashDuration": 5, "flashScope": 2, "frame": 1, "se": null}, {"flashColor": [255, 238, 204, 204], "flashDuration": 5, "flashScope": 1, "frame": 2, "se": null}]}, {"id": 112, "animation1Hue": 0, "animation1Name": "Gun3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 24, 72, 180, 0, 0, 255, 1]], [[1, 24, 72, 180, 0, 0, 255, 1]], [[2, 24, 72, 180, 0, 0, 255, 1], [0, -36, 12, 180, 0, 1, 255, 1]], [[1, -36, 12, 180, 0, 1, 255, 1], [8, 24, 72, 180, 0, 0, 255, 0], [3, 24, 72, 180, 0, 0, 255, 1]], [[2, -36, 12, 180, 0, 1, 255, 1], [9, 24, 72, 180, 0, 0, 255, 0], [4, 24, 72, 180, 0, 0, 255, 1], [0, 36, -24, 180, 0, 1, 255, 1]], [[10, 24, 72, 180, 0, 0, 255, 0], [5, 24, 72, 180, 0, 0, 255, 1], [8, -36, 12, 180, 0, 1, 255, 0], [3, -36, 12, 180, 0, 1, 255, 1], [1, 36, -24, 180, 0, 1, 255, 1]], [[2, 36, -24, 180, 0, 1, 255, 1], [11, 24, 72, 180, 0, 0, 255, 0], [9, -36, 12, 180, 0, 1, 255, 0], [6, 24, 72, 180, 0, 0, 255, 1], [4, -36, 12, 180, 0, 1, 255, 1], [0, -48, -60, 180, 0, 0, 255, 1]], [[12, 24, 72, 180, 0, 0, 255, 0], [7, 24, 72, 180, 0, 0, 255, 1], [8, 36, -24, 180, 0, 1, 255, 0], [10, -36, 12, 180, 0, 1, 255, 0], [5, -36, 12, 180, 0, 1, 255, 1], [3, 36, -24, 180, 0, 1, 255, 1], [1, -48, -60, 180, 0, 0, 255, 1]], [[11, -36, 12, 180, 0, 1, 255, 0], [9, 36, -24, 180, 0, 1, 255, 0], [2, -48, -60, 180, 0, 0, 255, 1], [13, 24, 72, 180, 0, 0, 255, 0], [6, -36, 12, 180, 0, 1, 255, 1], [4, 36, -24, 180, 0, 1, 255, 1], [0, 24, -96, 180, 0, 0, 255, 1]], [[14, 24, 72, 180, 0, 0, 255, 0], [12, -36, 12, 180, 0, 1, 255, 0], [8, -48, -60, 180, 0, 0, 255, 0], [7, -36, 12, 180, 0, 1, 255, 1], [10, 36, -24, 180, 0, 1, 255, 0], [5, 36, -24, 180, 0, 1, 255, 1], [3, -48, -60, 180, 0, 0, 255, 1], [1, 24, -96, 180, 0, 0, 255, 1]], [[15, 24, 72, 180, 0, 0, 255, 0], [9, -48, -60, 180, 0, 0, 255, 0], [11, 36, -24, 180, 0, 1, 255, 0], [2, 24, -96, 180, 0, 1, 255, 1], [13, -36, 12, 180, 0, 1, 255, 0], [6, 36, -24, 180, 0, 1, 255, 1], [4, -48, -60, 180, 0, 0, 255, 1]], [[16, 24, 72, 180, 0, 0, 255, 0], [14, -36, 12, 180, 0, 1, 255, 0], [7, 36, -24, 180, 0, 1, 255, 1], [12, 36, -24, 180, 0, 1, 255, 0], [8, 24, -96, 180, 0, 1, 255, 0], [10, -48, -60, 180, 0, 0, 255, 0], [5, -48, -60, 180, 0, 0, 255, 1], [3, 24, -96, 180, 0, 1, 255, 1], [-1, 0, 0, 30, 0, 0, 0, 0]], [[16, 24, 72, 180, 0, 0, 255, 0], [11, -48, -60, 180, 0, 0, 255, 0], [13, 36, -24, 180, 0, 1, 255, 0], [9, 24, -96, 180, 0, 1, 255, 0], [6, -48, -60, 180, 0, 0, 255, 1], [15, -36, 12, 180, 0, 1, 255, 0], [4, 24, -96, 180, 0, 1, 255, 1]], [[16, 24, 72, 180, 0, 0, 255, 0], [12, -48, -60, 180, 0, 0, 255, 0], [14, 36, -24, 180, 0, 1, 255, 0], [10, 24, -96, 180, 0, 1, 255, 0], [7, -48, -60, 180, 0, 0, 255, 1], [16, -36, 12, 180, 0, 1, 255, 0], [5, 24, -96, 180, 0, 1, 255, 1]], [[16, 24, 72, 180, 0, 0, 255, 0], [16, -36, 12, 180, 0, 1, 255, 0], [15, 36, -24, 180, 0, 1, 255, 0], [13, -48, -60, 180, 0, 0, 255, 0], [11, 24, -96, 180, 0, 1, 255, 0], [6, 24, -96, 180, 0, 1, 255, 1]], [[16, 24, 72, 180, 0, 0, 255, 0], [16, -36, 12, 180, 0, 1, 255, 0], [16, 36, -24, 180, 0, 1, 255, 0], [12, 24, -96, 180, 0, 1, 255, 0], [7, 24, -96, 180, 0, 1, 255, 1], [14, -48, -60, 180, 0, 0, 255, 0]], [[16, 24, 72, 180, 0, 0, 255, 0], [16, -36, 12, 180, 0, 1, 255, 0], [16, 36, -24, 180, 0, 1, 255, 0], [13, 24, -96, 180, 0, 1, 255, 0], [15, -48, -60, 180, 0, 0, 255, 0]], [[16, 24, 72, 180, 0, 0, 255, 0], [16, -36, 12, 180, 0, 1, 255, 0], [16, 36, -24, 180, 0, 1, 255, 0], [16, -48, -60, 180, 0, 0, 255, 0], [14, 24, -96, 180, 0, 1, 255, 0]], [[16, 24, 72, 180, 0, 0, 255, 0], [16, -36, 12, 180, 0, 1, 255, 0], [16, 36, -24, 180, 0, 1, 255, 0], [16, -48, -60, 180, 0, 0, 255, 0], [15, 24, -96, 180, 0, 1, 255, 0]], [[16, 24, 72, 180, 0, 0, 255, 0], [16, -36, 12, 180, 0, 1, 255, 0], [16, 36, -24, 180, 0, 1, 255, 0], [16, -48, -60, 180, 0, 0, 255, 0], [16, 24, -96, 180, 0, 1, 255, 0]], [[16, 24, 72, 180, 0, 0, 150, 0], [16, -36, 12, 180, 0, 1, 150, 0], [16, 36, -24, 180, 0, 1, 150, 0], [16, -48, -60, 180, 0, 0, 150, 0], [16, 24, -96, 180, 0, 1, 150, 0]], [[16, 24, 72, 180, 0, 0, 100, 0], [16, -36, 12, 180, 0, 1, 100, 0], [16, 36, -24, 180, 0, 1, 100, 0], [16, -48, -60, 180, 0, 0, 100, 0], [16, 24, -96, 180, 0, 1, 100, 0]], [[16, 24, 72, 180, 0, 0, 50, 0], [16, -36, 12, 180, 0, 1, 50, 0], [16, 36, -24, 180, 0, 1, 50, 0], [16, -48, -60, 180, 0, 0, 50, 0], [16, 24, -96, 180, 0, 1, 50, 0]]], "name": "Shoot Barrage", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Gun2", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 221, 136], "flashDuration": 1, "flashScope": 1, "frame": 2, "se": null}, {"flashColor": [255, 255, 221, 136], "flashDuration": 1, "flashScope": 1, "frame": 4, "se": null}, {"flashColor": [255, 255, 221, 136], "flashDuration": 1, "flashScope": 1, "frame": 6, "se": null}, {"flashColor": [255, 255, 221, 136], "flashDuration": 1, "flashScope": 1, "frame": 8, "se": null}, {"flashColor": [255, 255, 221, 136], "flashDuration": 3, "flashScope": 1, "frame": 10, "se": null}]}, {"id": 113, "animation1Hue": 0, "animation1Name": "Gun1", "animation2Hue": 0, "animation2Name": "", "frames": [[[2, -250, -3, 280, 0, 0, 255, 0], [1, -250, -3, 260, 0, 0, 188, 0], [11, -250, -3, 280, 0, 0, 255, 1], [0, -250, -3, 280, 0, 0, 255, 1], [1, -354, -166, 140, 0, 0, 255, 0]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [17, -250, -3, 280, 0, 0, 255, 0], [1, 34, -112, 300, 0, 0, 188, 0], [1, -250, -3, 280, 0, 0, 255, 0], [12, -250, -3, 280, 0, 0, 255, 1], [11, 34, -112, 320, 0, 0, 255, 1], [0, 34, -112, 320, 0, 0, 255, 1], [1, -42.5, 72, 140, 0, 0, 255, 0]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0], [18, -250, -3, 280, 0, 0, 255, 0], [17, 34, -112, 320, 0, 0, 255, 0], [1, 280, 10, 240, 0, 0, 188, 0], [13, -250, -3, 280, 0, 0, 255, 1], [12, 34, -112, 320, 0, 0, 255, 1], [11, 280, 10, 260, 0, 0, 255, 1], [0, -250, -3, 280, 0, 0, 0, 1], [1, 34, -112, 320, 0, 0, 255, 0], [0, 280, 10, 260, 0, 0, 255, 1], [1, 256.5, -152.5, 140, 0, 0, 255, 0]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0], [19, -250, -3, 280, 0, 0, 255, 0], [18, 34, -112, 320, 0, 0, 255, 0], [17, 280, 10, 260, 0, 0, 255, 0], [14, -250, -3, 280, 0, 0, 255, 1], [13, 34, -112, 320, 0, 0, 255, 1], [12, 280, 10, 260, 0, 0, 255, 1], [1, 280, 10, 260, 0, 0, 255, 0]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0], [20, -250, -3, 280, 0, 0, 255, 0], [19, 34, -112, 320, 0, 0, 255, 0], [18, 280, 10, 260, 0, 0, 255, 0], [15, -250, -3, 280, 0, 0, 255, 1], [14, 34, -112, 320, 0, 0, 255, 1], [13, 280, 10, 260, 0, 0, 255, 1]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0], [20, -250, -3, 280, 0, 0, 0, 0], [20, 34, -112, 320, 0, 0, 255, 0], [19, 280, 10, 260, 0, 0, 255, 0], [16, -250, -3, 280, 0, 0, 255, 1], [15, 34, -112, 320, 0, 0, 255, 1], [14, 280, 10, 260, 0, 0, 255, 1]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0], [20, 34, -112, 320, 0, 0, 0, 0], [20, 280, 8, 260, 0, 0, 255, 0], [16, 34, -112, 320, 0, 0, 255, 1], [15, 280, 10, 260, 0, 0, 255, 1]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0], [16, 280, 10, 260, 0, 0, 255, 1]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0]], [[2, -250, -3, 280, 0, 0, 255, 0], [2, 34, -112, 320, 0, 0, 255, 0], [2, 280, 10, 260, 0, 0, 255, 0]], [[4, -250, -3, 280, 0, 0, 255, 0], [4, 34, -112, 320, 0, 0, 255, 0], [4, 280, 10, 260, 0, 0, 255, 0]], [[5, -250, -3, 280, 0, 0, 255, 0], [5, 34, -112, 320, 0, 0, 255, 0], [5, 280, 10, 260, 0, 0, 255, 0]], [[7, -250, -3, 280, 0, 0, 255, 0], [7, 34, -112, 320, 0, 0, 255, 0], [7, 280, 10, 260, 0, 0, 255, 0]], [[8, -250, -3, 280, 0, 0, 188, 0], [8, 34, -112, 320, 0, 0, 188, 0], [8, 280, 10, 260, 0, 0, 188, 0]], [[10, -250, -3, 280, 0, 0, 100, 0], [10, 34, -112, 320, 0, 0, 100, 0], [10, 280, 10, 260, 0, 0, 100, 0]]], "name": "Shoot All", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Gun1", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 2, "flashScope": 2, "frame": 2, "se": {"name": "Gun1", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 2, "flashScope": 1, "frame": 3, "se": null}, {"flashColor": [255, 255, 204, 153], "flashDuration": 2, "flashScope": 2, "frame": 4, "se": {"name": "Gun1", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 153], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": null}]}, {"id": 114, "animation1Hue": 0, "animation1Name": "Gun2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 240, -168, 135, 0, 0, 125, 1], [0, -240, -24, 135, 0, 0, 125, 1]], [[0, -240, -24, 135, 0, 0, 255, 1], [0, 240, -168, 135, 0, 0, 255, 1]], [[0, -240, -24, 135, 0, 0, 125, 1], [0, 240, -168, 135, 0, 0, 125, 1]], [[0, -240, -24, 135, 0, 0, 255, 1], [0, 240, -168, 135, 0, 0, 255, 1]], [[0, -240, -24, 135, 0, 0, 125, 1], [0, 240, -168, 135, 0, 0, 125, 1]], [[0, -240, -24, 135, 0, 0, 255, 1], [0, 240, -168, 135, 0, 0, 255, 1]], [[0, -240, -24, 135, 0, 0, 125, 1], [0, 240, -168, 135, 0, 0, 125, 1]], [[0, -240, -24, 135, 0, 0, 255, 1], [0, 240, -168, 135, 0, 0, 255, 1]], [[0, -240, -24, 135, 0, 0, 255, 1], [0, 240, -168, 135, 0, 0, 255, 1]], [[0, -240, -24, 135, 0, 0, 255, 1], [0, 240, -168, 135, 0, 0, 255, 1]], [[0, -200, -36, 135, 0, 0, 255, 1], [0, 199, -156, 135, 0, 0, 255, 1]], [[0, -159, -48, 135, 0, 0, 255, 1], [0, 159, -144, 135, 0, 0, 255, 1]], [[0, -120, -60, 135, 0, 0, 255, 1], [0, 120, -132, 135, 0, 0, 255, 1]], [[0, -80, -72, 135, 0, 0, 255, 1], [0, 79, -120, 135, 0, 0, 255, 1]], [[0, -39, -84, 135, 0, 0, 255, 1], [0, 39, -108, 135, 0, 0, 255, 1]], [[0, 0, -96, 135, 0, 0, 255, 1], [0, 0, -96, 135, 0, 0, 255, 1], [1, 0, -96, 135, 0, 0, 255, 0], [1, 0, -96, 165, 0, 0, 200, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [1, 0, -96, 225, 0, 0, 150, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [1, 0, -96, 240, 0, 0, 140, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [1, 0, -96, 247, 0, 0, 120, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [1, 0, -96, 252, 0, 0, 100, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [1, 0, -96, 255, 0, 0, 50, 1]], [[1, 0, -96, 135, 0, 0, 255, 0]], [[1, 0, -96, 135, 0, 0, 255, 0], [3, 0, -96, 300, 0, 0, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [4, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [5, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [6, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [7, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [8, 0, -96, 427, 0, 0, 255, 1], [2, 324, -24, 375, 45, 0, 255, 1], [2, -312, -264, 375, 120, 1, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [9, 0, -96, 427, 0, 0, 255, 1], [2, 288, -156, 300, 340, 0, 255, 1], [2, -300, -96, 300, 340, 0, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [10, 0, -96, 427, 0, 0, 255, 1], [2, 240, -216, 180, 300, 0, 255, 1], [2, -252, -12, 180, 50, 1, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [11, 0, -96, 427, 0, 0, 255, 1], [2, 156, -192, 120, 60, 0, 255, 1], [2, -180, 12, 120, 130, 0, 255, 1]], [[1, 0, -96, 135, 0, 0, 255, 0], [12, 0, -96, 427, 0, 0, 255, 1], [2, 96, -168, 75, 45, 0, 255, 1], [2, -108, -24, 75, 160, 1, 255, 1]], [[1, 0, -96, 135, 0, 0, 150, 0], [12, 0, -96, 427, 0, 0, 100, 1], [13, 0, -96, 427, 0, 0, 255, 1]], [[1, 0, -96, 135, 0, 0, 100, 0], [14, 0, -96, 450, 0, 0, 255, 1]], [[13, 0, -96, 427, 0, 0, 255, 0], [15, 0, -96, 450, 0, 0, 255, 1]], [[16, 0, -96, 427, 0, 0, 255, 1], [13, -216, -108, 300, 0, 1, 255, 1]], [[17, 0, -96, 435, 0, 0, 255, 1], [14, -216, -108, 375, 0, 1, 255, 1]], [[18, 0, -96, 450, 0, 0, 255, 1], [15, -216, -108, 375, 0, 1, 255, 1], [13, 228, -48, 300, 0, 0, 255, 1]], [[19, 0, -96, 450, 0, 0, 255, 1], [16, -216, -108, 375, 0, 1, 255, 1], [14, 228, -48, 375, 0, 1, 255, 1]], [[20, 0, -96, 450, 0, 0, 255, 1], [17, -216, -108, 375, 0, 1, 255, 1], [15, 228, -48, 375, 0, 1, 255, 1]], [[21, 0, -96, 450, 0, 0, 255, 1], [18, -216, -108, 375, 0, 1, 255, 1], [16, 228, -48, 375, 0, 1, 255, 1]], [[22, 0, -96, 450, 0, 0, 255, 1], [19, -216, -108, 375, 0, 1, 255, 1], [17, 228, -48, 375, 0, 1, 255, 1]], [[23, 0, -96, 450, 0, 0, 255, 1], [20, -216, -108, 375, 0, 1, 255, 1], [18, 228, -48, 375, 0, 1, 255, 1]], [[24, 0, -96, 450, 0, 0, 255, 1], [21, -216, -108, 375, 0, 1, 255, 1], [19, 228, -48, 375, 0, 1, 255, 1]], [[22, -216, -108, 382, 0, 1, 255, 1], [20, 228, -48, 375, 0, 1, 255, 1]], [[23, -216, -108, 382, 0, 1, 255, 1], [21, 228, -48, 375, 0, 1, 255, 1]], [[24, -216, -108, 382, 0, 1, 255, 1], [22, 228, -48, 382, 0, 1, 255, 1]], [[23, 228, -48, 382, 0, 1, 255, 1]], [[24, 228, -48, 382, 0, 1, 255, 1]]], "name": "Shoot Special", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Phone", "pan": 0, "pitch": 150, "volume": 55}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 10, "se": {"name": "Skill1", "pan": 0, "pitch": 95, "volume": 80}}, {"flashColor": [255, 238, 153, 102], "flashDuration": 8, "flashScope": 2, "frame": 15, "se": {"name": "Skill2", "pan": 0, "pitch": 100, "volume": 80}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 22, "se": {"name": "Ice4", "pan": 0, "pitch": 120, "volume": 80}}, {"flashColor": [255, 255, 221, 187], "flashDuration": 8, "flashScope": 2, "frame": 23, "se": null}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 26, "se": {"name": "Battle3", "pan": 0, "pitch": 100, "volume": 85}}, {"flashColor": [255, 255, 187, 238], "flashDuration": 5, "flashScope": 1, "frame": 32, "se": null}, {"flashColor": [255, 255, 204, 221], "flashDuration": 10, "flashScope": 2, "frame": 32, "se": {"name": "Explosion2", "pan": 0, "pitch": 100, "volume": 90}}, {"flashColor": [255, 255, 187, 238], "flashDuration": 3, "flashScope": 1, "frame": 37, "se": null}, {"flashColor": [255, 255, 187, 238], "flashDuration": 3, "flashScope": 1, "frame": 40, "se": null}]}, {"id": 115, "animation1Hue": 0, "animation1Name": "Laser1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 49, -282, 300, 0, 0, 255, 1], [5, 5, 10, 300, 0, 0, 0, 0]], [[1, 49, -282, 300, 0, 0, 255, 1], [5, 5, 10, 300, 0, 0, 255, 0]], [[13, 5, -182, 200, 0, 0, 255, 0], [2, 49, -282, 300, 0, 0, 255, 1], [6, 5, 10, 300, 0, 0, 255, 0]], [[14, 5, -182, 200, 0, 0, 255, 0], [3, 49, -282, 300, 0, 0, 255, 1], [7, 5, 10, 300, 0, 0, 255, 0]], [[15, 5, -182, 200, 0, 0, 255, 0], [4, 49, -282, 300, 0, 0, 255, 1], [8, 5, 10, 300, 0, 0, 255, 0]], [[16, 5, -182, 200, 0, 0, 255, 0], [2, 49, -282, 300, 0, 0, 255, 1], [9, 5, 10, 300, 0, 0, 255, 0]], [[17, 5, -182, 200, 0, 0, 255, 0], [3, 49, -282, 300, 0, 0, 255, 1], [10, 5, 10, 300, 0, 0, 255, 0], [5, 5, 15, 280, 0, 0, 255, 0]], [[18, 5, -182, 200, 0, 0, 255, 0], [4, 49, -282, 300, 0, 0, 255, 1], [11, 5, 10, 300, 0, 0, 255, 0], [6, 5, 15, 280, 0, 0, 255, 0]], [[19, 5, -182, 200, 0, 0, 255, 0], [2, 49, -282, 300, 0, 0, 255, 1], [12, 5, 10, 300, 0, 0, 255, 0], [7, 5, 15, 280, 0, 0, 255, 0]], [[20, 5, -182, 200, 0, 0, 255, 0], [3, 49, -282, 300, 0, 0, 255, 1], [8, 5, 15, 280, 0, 0, 255, 0]], [[17, 5, -182, 200, 0, 0, 255, 0], [4, 49, -282, 300, 0, 0, 255, 1], [9, 5, 15, 280, 0, 0, 255, 0]], [[18, 5, -182, 200, 0, 0, 255, 0], [2, 49, -282, 300, 0, 0, 255, 1], [10, 5, 15, 280, 0, 0, 255, 0]], [[19, 5, -182, 200, 0, 0, 188, 0], [3, 49, -282, 300, 0, 0, 188, 1], [11, 5, 15, 280, 0, 0, 188, 0]], [[20, 5, -182, 200, 0, 0, 100, 0], [4, 49, -282, 300, 0, 0, 100, 1], [12, 5, 15, 280, 0, 0, 100, 0]]], "name": "Laser One", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Laser1", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 255, 204, 170], "flashDuration": 5, "flashScope": 2, "frame": 1, "se": {"name": "Paralyze2", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 255, 204, 204], "flashDuration": 5, "flashScope": 1, "frame": 2, "se": {"name": "Evasion2", "pan": 0, "pitch": 130, "volume": 90}}, {"flashColor": [255, 255, 204, 204], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": null}, {"flashColor": [255, 255, 204, 204], "flashDuration": 5, "flashScope": 1, "frame": 8, "se": null}]}, {"id": 116, "animation1Hue": 0, "animation1Name": "Laser2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, -210, -60, 260, 0, 0, 255, 1], [0, 150, -80, 330, 0, 1, 0, 1]], [[1, -210, -60, 260, 0, 0, 255, 1], [0, 150, -80, 330, 0, 1, 0, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[2, -210, -60, 260, 0, 0, 255, 1], [0, 150, -80, 330, 0, 1, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[3, -210, -60, 260, 0, 0, 255, 1], [1, 150, -80, 330, 0, 1, 255, 1], [-1, -364, 225.5, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[4, -210, -60, 260, 0, 0, 255, 1], [2, 150, -80, 330, 0, 1, 255, 1], [7, -210, -60, 260, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[5, -210, -60, 260, 0, 0, 255, 1], [3, 150, -80, 330, 0, 1, 255, 1], [8, -210, -60, 260, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [14, -200, 0, 300, 0, 0, 255, 1], [14, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[6, -210, -60, 260, 0, 0, 255, 1], [4, 150, -80, 330, 0, 1, 255, 1], [9, -210, -60, 260, 0, 0, 255, 1], [7, 150, -80, 330, 0, 1, 255, 1], [15, -200, 0, 300, 0, 0, 255, 1], [15, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[7, -210, -60, 260, 0, 0, 0, 1], [5, 150, -80, 330, 0, 1, 255, 1], [10, -210, -60, 260, 0, 0, 255, 1], [8, 150, -80, 330, 0, 1, 255, 1], [16, -200, 0, 300, 0, 0, 255, 1], [16, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[7, -210, -60, 260, 0, 0, 0, 1], [6, 150, -80, 330, 0, 1, 255, 1], [11, -210, -60, 260, 0, 0, 255, 1], [9, 150, -80, 330, 0, 1, 255, 1], [17, -200, 0, 300, 0, 0, 255, 1], [17, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[12, -210, -60, 260, 0, 0, 255, 1], [5, 150, -80, 330, 0, 1, 0, 1], [10, 150, -80, 330, 0, 1, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [18, -200, 0, 300, 0, 0, 255, 1], [18, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[13, -210, -60, 260, 0, 0, 255, 1], [6, 150, -80, 330, 0, 1, 255, 1], [11, 150, -80, 330, 0, 1, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [19, -200, 0, 300, 0, 0, 255, 1], [19, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[9, -210, -60, 260, 0, 0, 255, 1], [12, 150, -80, 330, 0, 1, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [20, -200, 0, 300, 0, 0, 255, 1], [20, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[10, -210, -60, 260, 0, 0, 255, 1], [13, 150, -80, 330, 0, 1, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [21, -200, 0, 300, 0, 0, 255, 1], [21, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[11, -210, -60, 260, 0, 0, 255, 1], [10, 150, -80, 330, 0, 1, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [14, -200, 0, 300, 0, 0, 255, 1], [14, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[12, -210, -60, 260, 0, 0, 255, 1], [11, 150, -80, 330, 0, 1, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [15, -200, 0, 300, 0, 0, 255, 1], [15, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[13, -210, -60, 260, 0, 0, 255, 1], [12, 150, -80, 330, 0, 1, 255, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [16, -200, 0, 300, 0, 0, 255, 1], [16, 200, 0, 300, 0, 0, 255, 1]], [[10, -210, -60, 260, 0, 0, 255, 1], [13, 150, -80, 330, 0, 1, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [17, -200, 0, 300, 0, 0, 255, 1], [17, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[11, -210, -60, 260, 0, 0, 203, 1], [10, 150, -80, 330, 0, 1, 203, 1], [-1, 0, -100, 100, 0, 0, 170, 1], [-1, 0, -100, 100, 0, 0, 170, 1], [18, -200, 0, 300, 0, 0, 255, 1], [18, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[12, -210, -60, 260, 0, 0, 152, 1], [11, 150, -80, 330, 0, 1, 152, 1], [-1, 0, -100, 100, 0, 0, 85, 1], [-1, 0, -100, 100, 0, 0, 85, 1], [19, -200, 0, 300, 0, 0, 255, 1], [19, 200, 0, 300, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1], [-1, 0, -100, 100, 0, 0, 255, 1]], [[13, -210, -60, 260, 0, 0, 100, 1], [12, 150, -80, 330, 0, 1, 100, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [20, -200, 0, 300, 0, 0, 203, 1], [20, 200, 0, 300, 0, 0, 203, 1]], [[-1, -237.5, -65, 260, 0, 0, 100, 1], [-1, -60, 217.5, 330, 0, 1, 100, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [14, -200, 0, 300, 0, 0, 152, 1], [14, 200, 0, 300, 0, 0, 152, 1]], [[-1, -207.5, -65, 260, 0, 0, 100, 1], [-1, 110, -75, 330, 0, 1, 100, 1], [-1, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 0, 0, 0, 0, 0, 0], [15, -200, 0, 300, 0, 0, 100, 1], [15, 200, 0, 300, 0, 0, 100, 1]]], "name": "Laser All", "position": 3, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Powerup", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 2, "se": {"name": "Paralyze2", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 255, 170, 187], "flashDuration": 5, "flashScope": 1, "frame": 5, "se": {"name": "Explosion1", "pan": 0, "pitch": 150, "volume": 90}}, {"flashColor": [255, 255, 170, 187], "flashDuration": 10, "flashScope": 2, "frame": 5, "se": null}, {"flashColor": [255, 255, 170, 187], "flashDuration": 10, "flashScope": 1, "frame": 7, "se": {"name": "Thunder1", "pan": 0, "pitch": 50, "volume": 90}}, {"flashColor": [255, 255, 170, 187], "flashDuration": 10, "flashScope": 2, "frame": 7, "se": null}]}, {"id": 117, "animation1Hue": 0, "animation1Name": "Light1", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -132, 195, 0, 0, 255, 1]], [[1, 0, -132, 195, 0, 0, 255, 1]], [[2, 0, -132, 195, 0, 0, 255, 1]], [[3, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 50, 1]], [[4, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 100, 1]], [[5, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 200, 1]], [[6, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[7, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[5, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[6, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 255, 1]], [[7, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 255, 1]], [[8, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 255, 1]], [[9, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[10, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[11, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[12, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 255, 1]], [[13, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 255, 1]], [[14, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 255, 1]], [[15, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[16, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[17, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[18, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 255, 1]], [[19, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 255, 1]], [[20, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 255, 1]], [[21, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[22, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[23, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[18, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 255, 1]], [[19, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 255, 1]], [[20, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 255, 1]], [[21, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[22, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[23, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[18, 0, -132, 195, 0, 0, 200, 1], [24, 0, -132, 195, 0, 0, 200, 1]], [[19, 0, -132, 195, 0, 0, 150, 1], [25, 0, -132, 195, 0, 0, 150, 1]], [[20, 0, -132, 195, 0, 0, 100, 1], [26, 0, -132, 195, 0, 0, 100, 1]], [[21, 0, -132, 195, 0, 0, 50, 1], [27, 0, -132, 195, 0, 0, 50, 1]]], "name": "Light Pillar 1", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic2", "pan": 0, "pitch": 150, "volume": 70}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 7, "se": {"name": "Up3", "pan": 0, "pitch": 150, "volume": 90}}]}, {"id": 118, "animation1Hue": 0, "animation1Name": "Light2", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, -132, 195, 0, 0, 255, 1]], [[1, 0, -132, 195, 0, 0, 255, 1]], [[2, 0, -132, 195, 0, 0, 255, 1]], [[3, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 50, 1]], [[4, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 100, 1]], [[5, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 200, 1]], [[6, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[7, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[5, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[6, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 255, 1]], [[7, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 255, 1]], [[8, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 255, 1]], [[9, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[10, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[11, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[12, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 255, 1]], [[13, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 255, 1]], [[14, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 255, 1]], [[15, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[16, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[17, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[18, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 255, 1]], [[19, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 255, 1]], [[20, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 255, 1]], [[21, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[22, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[23, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[18, 0, -132, 195, 0, 0, 255, 1], [24, 0, -132, 195, 0, 0, 255, 1]], [[19, 0, -132, 195, 0, 0, 255, 1], [25, 0, -132, 195, 0, 0, 255, 1]], [[20, 0, -132, 195, 0, 0, 255, 1], [26, 0, -132, 195, 0, 0, 255, 1]], [[21, 0, -132, 195, 0, 0, 255, 1], [27, 0, -132, 195, 0, 0, 255, 1]], [[22, 0, -132, 195, 0, 0, 255, 1], [28, 0, -132, 195, 0, 0, 255, 1]], [[23, 0, -132, 195, 0, 0, 255, 1], [29, 0, -132, 195, 0, 0, 255, 1]], [[18, 0, -132, 195, 0, 0, 200, 1], [24, 0, -132, 195, 0, 0, 200, 1]], [[19, 0, -132, 195, 0, 0, 150, 1], [25, 0, -132, 195, 0, 0, 150, 1]], [[20, 0, -132, 195, 0, 0, 100, 1], [26, 0, -132, 195, 0, 0, 100, 1]], [[21, 0, -132, 195, 0, 0, 50, 1], [27, 0, -132, 195, 0, 0, 50, 1]]], "name": "Light Pillar 2", "position": 2, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 0, "se": {"name": "Magic2", "pan": 0, "pitch": 150, "volume": 70}}, {"flashColor": [255, 255, 255, 255], "flashDuration": 5, "flashScope": 0, "frame": 7, "se": {"name": "Up3", "pan": 0, "pitch": 150, "volume": 80}}]}, {"id": 119, "animation1Hue": 0, "animation1Name": "Light3", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 375, 0, 0, 255, 1]], [[1, 0, 0, 375, 0, 0, 255, 1]], [[2, 0, 0, 375, 0, 0, 255, 1]], [[3, 0, 0, 375, 0, 0, 255, 1]], [[4, 0, 0, 375, 0, 0, 255, 1]], [[5, 0, 0, 375, 0, 0, 255, 1]], [[6, 0, 0, 375, 0, 0, 255, 1]], [[7, 0, 0, 375, 0, 0, 255, 1]], [[8, 0, 0, 375, 0, 0, 255, 1]], [[9, 0, 0, 375, 0, 0, 255, 1]], [[10, 0, 0, 375, 0, 0, 255, 1]], [[11, 0, 0, 375, 0, 0, 255, 1]], [[12, 0, 0, 375, 0, 0, 255, 1]], [[13, 0, 0, 375, 0, 0, 255, 1]], [[14, 0, 0, 375, 0, 0, 255, 1]], [[15, 0, 0, 375, 0, 0, 255, 1]], [[16, 0, 0, 375, 0, 0, 255, 1]], [[17, 0, 0, 375, 0, 0, 255, 1]], [[18, 0, 0, 375, 0, 0, 255, 1]], [[19, 0, 0, 375, 0, 0, 255, 1]], [[20, 0, 0, 375, 0, 0, 255, 1]], [[21, 0, 0, 375, 0, 0, 255, 1]], [[22, 0, 0, 375, 0, 0, 187, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[23, 0, 0, 375, 0, 0, 118, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1], [-1, 0, 0, 100, 0, 0, 255, 1]], [[24, 0, 0, 375, 0, 0, 50, 1]]], "name": "Ball of Light", "position": 1, "timings": [{"flashColor": [255, 255, 255, 255], "flashDuration": 15, "flashScope": 0, "frame": 0, "se": {"name": "Teleport", "pan": 0, "pitch": 90, "volume": 80}}]}, {"id": 120, "animation1Hue": 0, "animation1Name": "Light4", "animation2Hue": 0, "animation2Name": "", "frames": [[[0, 0, 0, 180, 0, 0, 255, 1]], [[0, 0, 0, 300, 0, 0, 255, 1]], [[1, 0, 0, 300, 0, 0, 255, 1]], [[2, 0, 0, 300, 0, 0, 255, 1]], [[3, 0, 0, 300, 0, 0, 255, 1]], [[4, 0, 0, 300, 0, 0, 255, 1]], [[5, 0, 0, 300, 0, 0, 255, 1]], [[6, 0, 0, 300, 0, 0, 255, 1]], [[7, 0, 0, 300, 0, 0, 255, 1]], [[8, 0, 0, 300, 0, 0, 255, 1]], [[9, 0, 0, 300, 0, 0, 255, 1]], [[10, 0, 0, 300, 0, 0, 255, 1]], [[11, 0, 0, 300, 0, 0, 255, 1]], [[12, 0, 0, 300, 0, 0, 255, 1]], [[13, 0, 0, 300, 0, 0, 255, 1]], [[14, 0, 0, 300, 0, 0, 255, 1]], [[15, 0, 0, 300, 0, 0, 255, 1]], [[16, 0, 0, 300, 0, 0, 255, 1]], [[17, 0, 0, 300, 0, 0, 255, 1]], [[18, 0, 0, 300, 0, 0, 255, 1]], [[19, 0, 0, 300, 0, 0, 255, 1]], [[20, 0, 0, 300, 0, 0, 255, 1]], [[21, 0, 0, 300, 0, 0, 255, 1]], [[22, 0, 0, 300, 0, 0, 255, 1]], [[23, 0, 0, 300, 0, 0, 255, 1]], [[24, 0, 0, 300, 0, 0, 255, 1]], [[25, 0, 0, 300, 0, 0, 255, 1]], [[26, 0, 0, 300, 0, 0, 255, 1]], [[27, 0, 0, 300, 0, 0, 200, 1]], [[28, 0, 0, 300, 0, 0, 180, 1]], [[29, 0, 0, 300, 0, 0, 150, 1]]], "name": "Glowing Light", "position": 1, "timings": [{"flashColor": [255, 255, 204, 204], "flashDuration": 10, "flashScope": 2, "frame": 0, "se": {"name": "Ice4", "pan": 0, "pitch": 70, "volume": 80}}]}]