{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "Living Room", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0LivingRoom_Day", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 1544, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 1544, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Exit", "note": "<Hitbox Up: 9> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 7, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 12}, {"id": 2, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 207, 0]}, {"code": 231, "indent": 1, "parameters": [90, "8StatUp_SisMom1", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 1, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 232, "indent": 1, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 1, "parameters": [90]}, {"code": 121, "indent": 1, "parameters": [207, 207, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 100, 0]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_MomMaid1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 12, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [10, "4LivingRoom_MomCasual1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 35, 1]}, {"code": 231, "indent": 3, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 208, 1]}, {"code": 231, "indent": 1, "parameters": [12, "4LivingRoom_SisPhone", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 35, 1]}, {"code": 111, "indent": 1, "parameters": [0, 42, 1]}, {"code": 231, "indent": 2, "parameters": [11, "4LivingRoom_AuntReading", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": true, "switch2Id": 3, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 17, 0, 13, 1]}, {"code": 231, "indent": 1, "parameters": [11, "4LivingRoom_AuntWaiting1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [11, "4LivingRoom_AuntReading", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 32, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 320, "indent": 0, "parameters": [7, "Tutor"]}, {"code": 320, "indent": 0, "parameters": [10, "Pupil"]}, {"code": 284, "indent": 0, "parameters": ["BG_Sis", false, false, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]????>\\bust[1]\\bustExp[1, 1]Hey dork!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]????>\\bust[1]\\bustExp[1, 0]Did'ya miss me?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\bust[1]Enter her name!"]}, {"code": 303, "indent": 1, "parameters": [4, 10]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\bust[1]Enter her relationship to you."]}, {"code": 303, "indent": 1, "parameters": [7, 10]}, {"code": 111, "indent": 1, "parameters": [4, 7, 1, "Sister"]}, {"code": 320, "indent": 2, "parameters": [10, "Brother"]}, {"code": 320, "indent": 2, "parameters": [13, "Big Sis"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 320, "indent": 2, "parameters": [13, "I"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\bust[1]Enter your relationship to her."]}, {"code": 303, "indent": 1, "parameters": [10, 10]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\bust[1]You call her \\n[4]. She is your \\n[7] and you are her \\n[10]. Is this correct?"]}, {"code": 102, "indent": 1, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Yes"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "No"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["BG_Aunt", false, false, 0, 0]}, {"code": 320, "indent": 0, "parameters": [6, "Neighbor"]}, {"code": 320, "indent": 0, "parameters": [9, "Neighbor"]}, {"code": 320, "indent": 0, "parameters": [12, "me"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]????>\\bust[1]\\bustExp[1, 1]My, oh my! It's so wonderful to see you dear!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]????>\\bust[1]\\bustExp[1, 0]Have you been well behaved for your \\n[5]?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\bust[1]Enter her name!"]}, {"code": 303, "indent": 1, "parameters": [3, 10]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\bust[1]Enter her relationship to you."]}, {"code": 303, "indent": 1, "parameters": [6, 10]}, {"code": 111, "indent": 1, "parameters": [4, 6, 1, "Aunt"]}, {"code": 320, "indent": 2, "parameters": [9, "<PERSON><PERSON><PERSON><PERSON>"]}, {"code": 320, "indent": 2, "parameters": [12, "Auntie"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\bust[1]Enter your relationship to her."]}, {"code": 303, "indent": 1, "parameters": [9, 10]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\bust[1]You call her \\n[3]. She is your \\n[6] and you are her \\n[9]. Is this correct?"]}, {"code": 102, "indent": 1, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Yes"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "No"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Mom1[BUST][Exp4x3]')"]}, {"code": 284, "indent": 0, "parameters": ["0LivingRoom_Day1", false, false, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 2]Ugh..!\\! What a long drive..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 0]And even then, somehow we made it home early still."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 2]Well I'm glad you made it back home from school safe and sound.\\! Your room is all prepared and cleaned for you."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 6]What's up with you \\n[1]? Your face is all flushed."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 1]Don't act too excited to see your big \\n[7]!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Me?!\\! Oh, uh no I'm totally fine, ahah...\\! I'm happy to see you \\n[4]."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 4]Hmm, alright then weirdo.\\! I'm going to go to my room and get unpacked!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 5]You better not have touched anything in there!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Aunt1[BUST][Exp3x3]')"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 0]Thank you so much for driving \\n[4] back from school \\n[3]. \\!I greatly appreciate "]}, {"code": 401, "indent": 0, "parameters": ["the help."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 1]Oh, it was no problem at all!\\! I'm just glad to help my Sister out with a favor."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 0]And speaking of favors...\\! I'd like to talk to you later about something important if you don't mind?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 4]Talk about something later?!\\! Why do I have a bad feeling about that..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 6]As you always say, it's important that we have each other's back!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 7]Using my own words against me, I see..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 4]And you \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 5]Oh my, oh my! You're growing into such a handsome young man!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Um, thank you \\n[6] \\n[3]."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 7]Oh please. \\!Just call me \\n[3]. *giggle*"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 0]I'll be in your room so we can talk privately sis."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 7]Aha... wonderful."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["\\bustMoveTo[2, 640, 0, 60]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[2]! What are we going to do about... y'know?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 4]THAT?!\\! 'That' was a one time thing, mister."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 7]As you can see, I've got my hands full right now.\\! Um, er, metaphorically speaking."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 2]You'll have to find out how to solve that little problem of yours alone sweetie."]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*sigh*\\! Right before I got to finish too..."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>And now \\n[4] is home from school for the summer so she'll just get in the way."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>And what's \\n[6] \\n[3] want to talk to \\n[2] about?\\! I wonder if I can listen in and see if I can hear their conversation."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [33, 33, 0]}, {"code": 121, "indent": 0, "parameters": [32, 32, 1]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 3]}, {"code": 121, "indent": 0, "parameters": [35, 35, 0]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 136, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Mom1[BUST][Exp4x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 0]How are those chores coming along, \\n[4]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 6]The chores!\\! R-Right, I uh..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 4]You've still been putting them off, haven't you?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 7]I know you're just home for the summer but that doesn't mean I don't expect you to help around."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 0]There are dishes to wash, tidying up your room, laundry..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 7]It's not good to stay in your room all day and play video games, you know?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 2]*sigh*\\! I know, I know..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 2]Maybe I can help with the laundry or something?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 3]That would be lovely!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 0]I'll set out the laundry for you tomorrow morning."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 3]Just remember to separate the lights from the darks!\\! And be sure to use hot water for the towels!\\! But for the delicates, make sure to use cold water so you don't end up "]}, {"code": 401, "indent": 0, "parameters": ["causing extra wrinkles and then there's the timing on the-"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 6]H-Huh?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 2]Y-Ya...\\! Noted, \\n[2]."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).<PERSON><PERSON><PERSON>(550, 0, 200)"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 3]For sheets, I prefer to use the detergent on the top left shelf!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 4]Oh, but then there's \\n[6] \\n[3]'s clothes.\\! She insists on using the drying sheets in the right cabinet."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 0]Are you still listening \\n[4], dear?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 1]\\n[4]?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 7]*sigh*\\! That girl..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 0]Hi there, honey.\\! How long has she been gone?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Well...\\! I think she left sometime after the lesson on \\n[6] \\n[3]'s clothes."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 7]At least she heard the most important bits, I suppose."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 2]She could learn a few things from you helping around the house."]}, {"code": 111, "indent": 0, "parameters": [0, 54, 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 9]It's nice having such a good helper to show my thanks to."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 0]Thank you for keeping up with your chores!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(2).clear()"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [16, 16, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [137, 137, 0]}, {"code": 121, "indent": 0, "parameters": [136, 136, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 138, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 16, 0, 20, 1]}, {"code": 121, "indent": 1, "parameters": [138, 138, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["^^^ This retroactively fixes bug where cutscene didn't have a stop switch"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]Surprise!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh, hey \\n[4]."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]Is that it?!\\! That's all you've got to say?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Uhh, how's it going \\n[4]?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]I told you to watch out for a surprise attack..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh, right!\\! Hah hah, I forgot about that!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Did you ask \\n[6] \\n[3] for advice?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Yes."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]I told her I wanted to play a prank on you and she said to use the element of surprise."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]So surprise \\n[1], it's time for us to fuck.\\! And this time, I'm gonna be on top!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wait, what?!\\! Right here?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]Hah!\\! So my idea DID surprise you!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Well ya...\\! Are you not worried about getting caught?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]That's part of the fun."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Whoa...\\! \\n[4]."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]Now get over here."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]I'm gonna have some fun with you..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 245, "indent": 0, "parameters": [{"name": "River", "volume": 15, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 21]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 140, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Oh!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]My, you startled me honey!\\! I didn't know you were out here too."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Sorry to spook you \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]I was coming to check on your \\n[7] but she ran out of here as fast as she could..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]I'm worried she might be coming down with a cold."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 1]Oh dear!\\! Honey, your face is all red too!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh is it?\\! No need to worry, I feel completely fine!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]I hope there's nothing going around the neighborhood."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Nah, it must just be this summer heat!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Anyway, I'm gonna get going and play my game now!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]Okay, have fun dear!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]Hmm ~ They've both been acting strange recently..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Maybe I'll just have to keep my eyes and ears peeled for what sort of trouble they're "]}, {"code": 401, "indent": 0, "parameters": ["getting into."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [90, "8StatUp_MomSis1", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 7, 0, 14, 8, 0]}, {"code": 122, "indent": 0, "parameters": [16, 16, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [138, 138, 1]}, {"code": 121, "indent": 0, "parameters": [140, 140, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 204, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Mom1[BUST][Exp4x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 3]I don't know, I just thought that-"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 9]Ah!\\! Good morning, dear!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 5]\\n[1]?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Good morning.\\! What are you two chatting about?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 2]It's nothing.\\! Nothing at a-"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 2]I was just asking your \\n[7] if she'd like to join me."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Join you?\\! Where to?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 9]Well, I was going to see if you'd like me to give you a titty fuck."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>What?!\\! Are you serious?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 10]I just thought it would be a nice change of pace."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 4]But I'm afraid your \\n[7] is a little shy..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 5]I told you \\n[2], I'm not shy!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 2]It's just..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 3]. . . They're so much bigger than mine."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 2]Don't worry dear.\\! I'm sure \\n[1] would love for you to use your breasts too."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>. . . \\!Is this really happening?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 7]Do you really think so \\n[2]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 9]Why don't we find out?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(2).clear()"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).clear()"]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 121, "indent": 0, "parameters": [204, 204, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 205, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [7]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Phew!\\! Would you look at the time."]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]We really got carried away there..!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]But I'm so glad we found a way to 'talk' this little issue of ours out."]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]Now you two stay out of trouble, alright?"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Or the next time you're both up to no good, be sure not to leave \\n[11] out of it ~ ♥"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 121, "indent": 0, "parameters": [206, 206, 0]}, {"code": 121, "indent": 0, "parameters": [205, 205, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 195, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Aunt1[BUST][Exp3x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Mom1[BUST][Exp4x3]')"]}, {"code": 241, "indent": 0, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 5]No fair!\\! It's my turn to share time with \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Oh, save it, Sis..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 3]We all know you've been sneaking off and getting extra alone time."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Do you really think \\n[4] and I can't hear what goes on behind the "]}, {"code": 401, "indent": 0, "parameters": ["laundry room door?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]The two of you were listening?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 3]Well, yeah \\n[2]..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 6]Did you really think we couldn't hear you moaning like that?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]W-Well, I..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 0]So I think it's quite clear that I get to spend more time with \\n[1] today."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]I've been dying to have him bend me over and fuck me nice and hard ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 5]\\n[3]!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]WHAT THE HELL?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 6]You mean to tell me that YOU have been fucking him TOO \\n[6] \\n[3]?!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]Aww, you weren't aware darling..?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]\\n[1] has been my little boy-toy for a while now."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 4]There's no way..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 3]So we've all been fucking \\n[1] this whole time?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Yes...\\! All three of us have been."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]And there he is.\\! Right on time."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>H-Hey guys!\\! Why is everyone standing around in the living room?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]You've been fucking \\n[6] \\n[3] too?!\\! Y-You PERV!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>O-Oh...\\! You, uhh ~\\! You found out about that..?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Your \\n[7] doesn't appear to understand how deep and special our \\n[6] and \\n[9] relationship is."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]I was just telling them both that you and I were going to spend some alone time today."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]NO!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 2]I-\\! I mean..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 3]I thought you might want to play through another adventure with me today, right \\n[1]?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]I'm afraid that just won't do..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]Not when I need some help with my yoga practice later today."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]But we're all in disagreement..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 2]Yup.\\! Only one person here can decide the plans then."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]I suppose you both are right about that..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 0]So, \\n[1]."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 0]Three women, fighting for your attention."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Who will you choose today?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["\\c[23]\\n[2]", "\\c[21]\\n[6] \\n[3]", "\\c[10]\\n[4]"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[23]\\n[2]"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 3]Be sure to think your decision over carefully..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]You want to spend time with \\n[12], right?"]}, {"code": 241, "indent": 1, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Right...\\! Of course..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[21]\\n[6] \\n[3]", "\\c[21]\\n[6] \\n[3]", "\\c[21]\\n[6] \\n[3]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]W-Wait a sec!"]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 7]You know there's only one right answer \\n[1]."]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 8]So let's go 'hang out'!"]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You see, I ~"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[10]\\n[4]", "\\c[10]\\n[4]", "\\c[10]\\n[4]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[21]\\n[6] \\n[3]"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 7]You know there's only one right answer \\n[1]."]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 8]So let's go 'hang out'!"]}, {"code": 241, "indent": 1, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Right...\\! Of course..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[10]\\n[4]", "\\c[10]\\n[4]", "\\c[10]\\n[4]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Don't make a hasty deicision, baby!"]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Think carefully about this, now."]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]You want to spend time with \\n[11], right?"]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You see, I ~"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[23]\\n[2]", "\\c[23]\\n[2]", "\\c[23]\\n[2]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[10]\\n[4]"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Think carefully baby!"]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]You want to spend time with \\n[11], right?"]}, {"code": 241, "indent": 1, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Right...\\! Of course..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[23]\\n[2]", "\\c[23]\\n[2]", "\\c[23]\\n[2]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]We all know who you truly want to make love to, dear."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 3]Don't let those two pressure you!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]You want to spend time with \\n[12], right?"]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You see, I ~"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[21]\\n[6] \\n[3]", "\\c[21]\\n[6] \\n[3]", "\\c[21]\\n[6] \\n[3]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 1]This new adventure is my best one yet!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 8]You'll love the sexy quest reward!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]This new pose will require a lot of careful assistance."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]We'll be connected in all sorts of intimate ways..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I-\\! I don't know if I can-"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Don't worry baby.\\! You know you can come to \\n[11]."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 102, "indent": 0, "parameters": [["I don't...", "feel so...", "...good."], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "I don't..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "feel so..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "...good."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [10]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>G-Guys. . .\\! I. . ."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I feel kinda. . .\\! . . . funny ~ "]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Damage2", "volume": 90, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Girls>\\n[1]!"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 14, 0, 0]}, {"code": 121, "indent": 0, "parameters": [214, 214, 0]}, {"code": 121, "indent": 0, "parameters": [195, 195, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 3, "name": "Mom LR Sex Event", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','3zMomLRSex1[4x4]');"]}, {"code": 655, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','3zMomLRSex2[4x4]');"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Having sex out here sounds so dangerous..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Let's be quick about it, okay?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [18, "4LivingRoom_CouchCover", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3zMomLRSex_Sofa", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomLRSex1[4x4]", 0, 0, 620, 320, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 6]}, {"code": 231, "indent": 0, "parameters": [25, "3zMomLRSex_After1", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "3zMomLRSex_After2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh honey...\\! You're so big!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>It's going to be difficult for \\n[11] to hold in her own pleasure!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Right there!\\! Mmh! Right there baby~ ♥"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomLRSex2[4x4]", 0, 0, 620, 320, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 7]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Ohh!\\! You're going to make me cum \\n[1]!\\! I'm cumming from your dick!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'm going to cum too \\n[2]!\\! I can't hold it!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Fill me up baby!\\! Fill \\n[11] up!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 70, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex6", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 620, 320, 100, 100, 0, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 NoLoop"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*pant*\\! That was awesome!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>But aren't you worried that \\n[6] \\n[3] and \\n[4] might have heard us?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*giggle*\\! I'm not sure that I care honey..."]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 235, "indent": 0, "parameters": [18]}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 12, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','3zMomLRSex3[4x4]');"]}, {"code": 655, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','3zMomLRSex4[4x4]');"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Having sex out here sounds so dangerous..."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Let's be quick about it, okay?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [18, "4LivingRoom_CouchCover", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3zMomLRSex_Sofa", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomLRSex3[4x4]", 0, 0, 620, 320, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 6]}, {"code": 231, "indent": 0, "parameters": [25, "3zMomLRSex_After3", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "3zMomLRSex_After4", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh honey...\\! You're so big!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>It's going to be difficult for \\n[11] to hold in her own pleasure!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Right there!\\! Mmh! Right there baby~ ♥"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomLRSex4[4x4]", 0, 0, 620, 320, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 7]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Ohh!\\! You're going to make me cum \\n[1]!\\! I'm cumming from your dick!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'm going to cum too \\n[2]!\\! I can't hold it!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Fill me up baby!\\! Fill \\n[11] up!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 70, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex6", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 620, 320, 100, 100, 0, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 NoLoop"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*pant*\\! That was awesome!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>But aren't you worried that \\n[6] \\n[3] and \\n[4] might have heard us?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*giggle*\\! I'm not sure that I care honey..."]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 235, "indent": 0, "parameters": [18]}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 22, "y": 0}, {"id": 4, "name": "Mom Cleaning (Morning)", "note": "<Hitbox Up: 8> <Hitbox Right: 3>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 15, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 2, 1, 2]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Good morning honey.\\! What did you want to talk about?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]It feels so nice getting so much done in the morning, don't you agree?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(2, \"$gameVariables.value(15)  < 5\")"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "<PERSON><PERSON><PERSON>", "Leave"], 2, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<PERSON><PERSON><PERSON>"]}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 7, 1]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]We have some time.\\! Come and sit down over here."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]I suppose it's early enough to sneak one in.\\! Just keep it down, okay?"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 54, "switch2Valid": true, "variableId": 15, "variableValid": false, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 2, 1, 2]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Were you staring at my ass?\\! You're so troublesome..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]I still expect you to keep up with your chores, you know..?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Talk", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Leave"], 3, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<PERSON><PERSON><PERSON>"]}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 7, 1]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]We have some time.\\! Come and sit down over here."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]I suppose it's early enough to sneak one in.\\! Just keep it down, okay?"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON><PERSON><PERSON><PERSON>"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]A blowjob?!\\! It's so risky out here in the living room..."]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Okay.\\! Let's just be quick about it..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 121, "indent": 1, "parameters": [16, 16, 0]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Leave"]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 55, "switch2Valid": true, "variableId": 15, "variableValid": false, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 2, 1, 2]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Do you like watching \\n[11] clean?\\! I can feel you staring at my ass..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Good morning baby..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 118, "indent": 0, "parameters": ["Start"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(5, \"$gameSwitches.value(128) == false\")"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(6, \"$gameSwitches.value(124) == false\")"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sex", "<PERSON><PERSON><PERSON>", "Change Outfit"], 5, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<PERSON><PERSON><PERSON>"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]We've got some time to kill before the two of them "]}, {"code": 401, "indent": 1, "parameters": ["wake up."]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Would you prefer I keep my top on or off?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["Topless", "<PERSON>rt <PERSON>"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Topless"]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "<PERSON>rt <PERSON>"]}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON><PERSON><PERSON><PERSON>"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]A blowjob?!\\! I was going to be on my knees cleaning anyway..."]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Alright.\\! Pull it out for me so I can get started baby..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 121, "indent": 1, "parameters": [16, 16, 0]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Sex"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(203) == false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(203) == true\")"]}, {"code": 102, "indent": 1, "parameters": [["Floor", "Cabinet", "???", "Cancel"], 3, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Floor"]}, {"code": 355, "indent": 2, "parameters": ["$gameSelfSwitches.setValue([2,3,'C'], true);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Cabinet"]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 2, 1, 3]}, {"code": 111, "indent": 2, "parameters": [1, 40, 0, 3, 0]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfSwitches.setValue([2,14,'B'], true);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$gameSelfSwitches.setValue([2,14,'B'], false);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [201, 201, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "???"]}, {"code": 119, "indent": 2, "parameters": ["Start"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Cancel"]}, {"code": 119, "indent": 2, "parameters": ["Start"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "<PERSON><PERSON><PERSON>"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([2,8,'C'], true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [5, "Change Outfit"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(99) == false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(99) == true\")"]}, {"code": 102, "indent": 1, "parameters": [["Regular Clothes", "<PERSON><PERSON><PERSON>", "Maid Mode!", "???"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Regular Clothes"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Okay.\\! I'll keep these clothes on."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "<PERSON><PERSON><PERSON>"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I don't suppose I mind slipping into something a little more comfortable..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 122, "indent": 2, "parameters": [12, 12, 0, 0, 1]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Maid Mode!"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 1]T-That outfit?!"]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Well, if that's what you'd like to see me wear..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 121, "indent": 2, "parameters": [100, 100, 0]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "???"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 2, "parameters": ["Continue playing through \\n[2]'s story and discover a secret to unlock this outfit!"]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["Leave"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Leave"]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 202, "switch2Valid": true, "variableId": 15, "variableValid": false, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Good morning baby."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Good morning \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Now just where do you think you're going..?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Uhh ~\\! To the kitchen to get some waffles?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]Not until you give your \\n[5] a kiss!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Don't be shy now, come here..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [201, 201, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [10, "4LivingRoom_Mom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 33, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 35, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 55, "switch2Valid": false, "variableId": 12, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 2, 1, 2]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Phew!\\! It's so hot today..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Were you staring at my ass?\\! You're so troublesome..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(4, \"$gameSwitches.value(128) == false\")"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(5, \"$gameSwitches.value(124) == false\")"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Sex)", "<PERSON><PERSON><PERSON>", "Change Outfit", "Leave"], 5, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_MomCasual1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<PERSON><PERSON><PERSON><PERSON>"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]An early morning blowjob?"]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]I'll do anything for my baby!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 121, "indent": 1, "parameters": [16, 16, 0]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 18]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON><PERSON> (Sex)"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([2,3,'C'], true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "<PERSON><PERSON><PERSON>"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([2,8,'C'], true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "Change Outfit"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(99) == false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(99) == true\")"]}, {"code": 102, "indent": 1, "parameters": [["Regular Clothes", "<PERSON><PERSON><PERSON>", "Maid Mode!", "???"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Regular Clothes"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]You prefer my other outfit?\\! If that's what you like, give me one moment honey."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 122, "indent": 2, "parameters": [12, 12, 0, 0, 0]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "<PERSON><PERSON><PERSON>"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]I'll stay in this outfit then.\\! It's quite comfortable."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Maid Mode!"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 1]T-That outfit?!"]}, {"code": 101, "indent": 2, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Well, if that's what you'd like to see me wear..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 121, "indent": 2, "parameters": [100, 100, 0]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "???"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 2, "parameters": ["Continue playing through \\n[2]'s story and discover a secret to unlock this outfit!"]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [5, "Leave"]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_MomCasual1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 100, "switch2Valid": true, "variableId": 12, "variableValid": false, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 2, 1, 2]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom10[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]What?\\! Have you never seen a pretty maid clean before?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom10[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Your eyes seem glued to my outfit..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Talk", "<PERSON><PERSON><PERSON>", "Change Back", "Leave"], 3, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [32]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_MomMaid1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<PERSON><PERSON><PERSON>"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([2,8,'C'], true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Change Back"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom10[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Perhaps this outfit is a bit too much, huh..?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [100, 100, 1]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Leave"]}, {"code": 231, "indent": 1, "parameters": [10, "4LivingRoom_MomMaid1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 13}, {"id": 5, "name": "TV + Aunt (Evening)", "note": "<Hitbox Up: 4> <Hitbox Right: 5>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 24, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Video Games", "Leave"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Video Games"]}, {"code": 231, "indent": 1, "parameters": [20, "5L<PERSON>Bend1[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>Finally!\\! Time to get some practice games in."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [2, "4LivingRoom_MC_Gaming", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 241, "indent": 1, "parameters": [{"name": "Battle3", "volume": 15, "pitch": 110, "pan": 0}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>Hah!\\! Take that! Double kill, baby!"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Thunder6", "volume": 35, "pitch": 140, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>What's the matter?\\! Is your MP running low?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>At this rate, you'll never be able to compete against me!"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Attack2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 250, "indent": 1, "parameters": [{"name": "Attack2", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\n[2]>Don't mind me sweetie.\\! I need to clean around the television for just a moment."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\n[2]>And can you please watch how you speak to your friends on that game?\\! Can't you be a little "]}, {"code": 401, "indent": 1, "parameters": ["nicer to them?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>This guy isn't my friend \\n[2].\\! He's some random dude online."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>W-Wait \\n[2]! \\!You're blocking the TV!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>You're..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\n[2]>I told you I'll be just a moment sweet heart.\\! I'll be real quick!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 249, "indent": 1, "parameters": [{"name": "Defeat2", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<Game>GAME OVER!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<l33t69>(HA!\\! Get destroyed loser!\\! Why don't you have anything to say now, idiot?)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<l33t69>(You suck!\\! Almost as much as your Mom sucking me "]}, {"code": 401, "indent": 1, "parameters": ["off last night!)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\n[2]>Are you being nice to your friend on that headset, \\n[1]?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>...Huh?\\! O-Oh.. yeah..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>[I can't believe what I'm seeing..!\\! I've never known that \\n[2]'s ass was so big..!]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>[M-Maybe I should...]"]}, {"code": 102, "indent": 1, "parameters": [["Do Nothing \\i[31]", "Touch It \\i[30]"], -1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "Do Nothing \\i[31]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>[No, no.\\! I can't do that!]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>[But... I want to feel it so bad!\\! Agh! What the hell is wrong with me?!]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Touch It \\i[30]"]}, {"code": 122, "indent": 2, "parameters": [29, 29, 1, 0, 1]}, {"code": 121, "indent": 2, "parameters": [25, 25, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>[Maybe, I could just...]"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 250, "indent": 2, "parameters": [{"name": "Wind4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 2, "parameters": [40]}, {"code": 250, "indent": 2, "parameters": [{"name": "Wind4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 2, "parameters": [20]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\n[2]>WHAT THE?!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [0, 25, 0]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 6]What do you think you're doing young man?!"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>Ah! I-I'm sorry \\n[2]! It was an accident!"]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 5]That squeeze certainly "]}, {"code": 401, "indent": 2, "parameters": ["didn't feel like an 'accident'."]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 5]Accident or not, you need to watch "]}, {"code": 401, "indent": 2, "parameters": ["where you place your hands mister."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>Sorry \\n[2]... It won't happen again."]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 5]*sigh*\\! I accept your apology, but you can't behave with that sort of mischief."]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 5]Now if you'll need me, I'm going to rest up and take a bath."]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Try not to play your game for too long."]}, {"code": 355, "indent": 2, "parameters": ["$bust(1).clear()"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]Okay honey.\\! I'm all finished!"]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Did you win your game?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>Uhh... No, I lost."]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Aww, I'm sorry.\\! You'll get 'em next time!"]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]\\n[1]? Are you okay?"]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]You look like you've seen a ghost..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>What?\\! Oh ya, I'm fine..."]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Okay then.\\! Be sure to drink some water if you're feeling faint."]}, {"code": 101, "indent": 2, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]I'm going to rest up and go take a bath if you're looking for me."]}, {"code": 355, "indent": 2, "parameters": ["$bust(1).clear()"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>I can't believe that just happened..!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>I don't think I'm really in the mood to play video games right now."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 121, "indent": 1, "parameters": [26, 26, 0]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 24, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Video Games", "Pass Time", "Leave"], 2, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Video Games"]}, {"code": 111, "indent": 1, "parameters": [1, 12, 0, 0, 0]}, {"code": 231, "indent": 2, "parameters": [20, "5L<PERSON>Bend1[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "5L<PERSON><PERSON>end2[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>Time to get some practice battles in."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [2, "4LivingRoom_MC_Gaming", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 241, "indent": 1, "parameters": [{"name": "Battle3", "volume": 15, "pitch": 110, "pan": 0}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>Hah!\\! Take that! Double kill, baby!"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Thunder6", "volume": 35, "pitch": 140, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>What's the matter?\\! Is your MP running low?"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Attack2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 250, "indent": 1, "parameters": [{"name": "Attack2", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 111, "indent": 1, "parameters": [0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\n[2]>Don't mind me sweetie.\\! I just need to clean again for a quick moment."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 232, "indent": 2, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 111, "indent": 2, "parameters": [0, 54, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\n[2]>Almost got it!\\! There's just some pesky dust right behind this spot right here..."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\n[2]>I hope I'm not interrupting your game...\\! I'm sure you don't mind, right baby?"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>[\\n[2]'s ass is so amazing!\\! This is way better than playing my games!]"]}, {"code": 242, "indent": 2, "parameters": [1]}, {"code": 250, "indent": 2, "parameters": [{"name": "Damage2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 249, "indent": 2, "parameters": [{"name": "Defeat2", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<Game>GAME OVER!"]}, {"code": 111, "indent": 2, "parameters": [0, 54, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\n[2]>There!\\! I'm all done sweetie. Thank you for being so patient!"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\n[2]>I'm all finished!\\! Unless you'd like for me to 'clean' a bit more?\\! *giggle*"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 242, "indent": 2, "parameters": [1]}, {"code": 250, "indent": 2, "parameters": [{"name": "Damage2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 249, "indent": 2, "parameters": [{"name": "Victory1", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<Game>YOU WIN!"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>Aw yeah!\\! I finally got a level up!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Pass Time"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 26, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I'm not really in the mood to play my games right now."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 31, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I shouldn't do that right now.\\! I should talk to \\n[2]."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 33, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(1, \"$gameSwitches.value(118) == false\")"]}, {"code": 102, "indent": 0, "parameters": [["Video Games", "\\n[6] \\n[3]", "Pass Time", "Leave"], 3, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Video Games"]}, {"code": 121, "indent": 1, "parameters": [119, 119, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\n[6] \\n[3]"]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [11, "4LivingRoom_AuntReading", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Pass Time"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 42, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Video Games", "Pass Time", "Leave"], 2, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Video Games"]}, {"code": 111, "indent": 1, "parameters": [0, 118, 0]}, {"code": 121, "indent": 2, "parameters": [119, 119, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>A new opponent!\\! You're going down!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 231, "indent": 2, "parameters": [2, "4LivingRoom_MC_Gaming", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 241, "indent": 2, "parameters": [{"name": "Battle3", "volume": 15, "pitch": 110, "pan": 0}]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>Boom!\\! Nice try!"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Thunder6", "volume": 35, "pitch": 140, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>You're no match for me!\\! I'm still at full health!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 250, "indent": 2, "parameters": [{"name": "Attack2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 250, "indent": 2, "parameters": [{"name": "Attack2", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [45]}, {"code": 250, "indent": 2, "parameters": [{"name": "Darkness3", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [20]}, {"code": 242, "indent": 2, "parameters": [3]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 16, 0, 8, 1]}, {"code": 122, "indent": 3, "parameters": [35, 35, 0, 0, 1]}, {"code": 122, "indent": 3, "parameters": [33, 33, 1, 0, 1]}, {"code": 121, "indent": 3, "parameters": [73, 73, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [11]}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [8]}, {"code": 111, "indent": 2, "parameters": [0, 4, 0]}, {"code": 201, "indent": 3, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Pass Time"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 35, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I wonder where \\n[2] and \\n[6] \\n[3] went to?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 33, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["\\n[6] \\n[3]", "Pass Time", "Leave"], 2, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\n[6] \\n[3]"]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [11, "4LivingRoom_AuntReading", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Pass Time"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 82, "switch2Valid": false, "variableId": 17, "variableValid": true, "variableValue": 13}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["\\n[6] \\n[3]", "Pass Time", "Leave"], 2, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\n[6] \\n[3]"]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 2, 1, 2]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [1, 40, 0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I could use a little fun right about now..."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 40, 0, 2, 0]}, {"code": 101, "indent": 2, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Are you looking for something to do, honey?"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(81) === false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(87) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Talk", "Sex", "<PERSON><PERSON> (BJ)", "Date Night", "Leave"], 4, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Talk"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 117, "indent": 2, "parameters": [16]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Sex"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]It's like you read my mind..."]}, {"code": 101, "indent": 2, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Come meet me in the bedroom.\\! But should I keep these clothes on or take it all off?"]}, {"code": 102, "indent": 2, "parameters": [["<PERSON><PERSON>", "Clothed"], -1, 0, 1, 0]}, {"code": 402, "indent": 2, "parameters": [0, "<PERSON><PERSON>"]}, {"code": 121, "indent": 3, "parameters": [16, 16, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "Clothed"]}, {"code": 121, "indent": 3, "parameters": [16, 16, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 10]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 119, "indent": 2, "parameters": ["End"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "<PERSON><PERSON> (BJ)"]}, {"code": 355, "indent": 2, "parameters": ["$gameSelfSwitches.setValue([2,15,'C'], true);"]}, {"code": 119, "indent": 2, "parameters": ["End"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Date Night"]}, {"code": 111, "indent": 2, "parameters": [0, 86, 1]}, {"code": 101, "indent": 3, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]You'd like to have another date tonight?"]}, {"code": 102, "indent": 3, "parameters": [["Yes", "No"], 1, 0, 1, 0]}, {"code": 402, "indent": 3, "parameters": [0, "Yes"]}, {"code": 101, "indent": 4, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]I'm so glad to hear that!"]}, {"code": 101, "indent": 4, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]I can't wait to see you tonight ~ ♥️"]}, {"code": 121, "indent": 4, "parameters": [86, 86, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "No"]}, {"code": 101, "indent": 4, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]That's okay.\\! We can have dinner a different night instead."]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]I've already been making plans for the dinner tonight."]}, {"code": 101, "indent": 3, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]Do you want to postpone it to another night, dear?"]}, {"code": 102, "indent": 3, "parameters": [["Yes", "No"], 1, 0, 1, 0]}, {"code": 402, "indent": 3, "parameters": [0, "Yes"]}, {"code": 101, "indent": 4, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]That's okay.\\! We can have dinner a different night instead."]}, {"code": 121, "indent": 4, "parameters": [86, 86, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "No"]}, {"code": 101, "indent": 4, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Good.\\! I'll see you tonight."]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Leave"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 231, "indent": 1, "parameters": [11, "4LivingRoom_AuntWaiting1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 118, "indent": 1, "parameters": ["End"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Pass Time"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 11}, {"id": 6, "name": "Kitchen", "note": "<Hitbox Up: 10> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 15, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 12}, {"id": 7, "name": "Kitchen", "note": "<Hitbox Up: 4> <Hitbox Right: 4>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Siren>The kitchen is still under construction.\\! I'll be adding new "]}, {"code": 401, "indent": 0, "parameters": ["areas in the house over time!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 15, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 7}, {"id": 8, "name": "<PERSON> <PERSON><PERSON>e <PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [20, "5L<PERSON>Bend1[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Y-You just want to play with my butt?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Okay.\\! Just don't get carried away..!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wow!\\! You have the best ass ever \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>It's a little embarrassing being in this position so please try to hurry baby..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I don't know about that.\\! I want to take my time enjoying this!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh \\n[1] ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's so soft ~\\! Your butt cheeks feel so soft and squishy \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[It's so lewd having him lust over my body with all of this intensity!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[But his excitement...\\! He's so happy admiring my body and that makes me feel so good...]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[Hmmm ~ What should I do now..?]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Squeeze \\i[31]", "Spank \\i[30]"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Squeeze \\i[31]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["*squish*\\! *squish*"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you enjoying yourself baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I could sit here and play with your butt forever \\n[2]!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then keep going baby!\\! Play with \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Spank \\i[30]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Oh!\\! Geez, you're so naughty baby!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Does that turn you on?\\! Spanking \\n[11] like I've been a bad girl?!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You bet it does!\\! I love watching your butt jiggle when I spank you!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then do it again!\\! Spank \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 18, 1]}, {"code": 111, "indent": 1, "parameters": [0, 11, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3MomBendSexA_PreIdle", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3MomBendSexA1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 232, "indent": 1, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you just going to sit there and keep staring baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>You know you can't leave \\n[11] unsatisfied, right?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 118, "indent": 1, "parameters": ["1"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Finish", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 32]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 119, "indent": 2, "parameters": ["End"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Finish"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>*sigh*\\! I suppose it can't be helped..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 232, "indent": 2, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Alright baby.\\! I think that's enough fun for now."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 12, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [20, "5L<PERSON><PERSON>end2[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]W-Why would you want to do something like that?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Fine.\\! But only for a little bit..!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wow!\\! You have the best ass ever \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>It's a little embarrassing being in this position so please try to hurry baby..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I don't know about that.\\! I want to take my time enjoying this!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh \\n[1] ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's so soft ~\\! Your butt cheeks feel so soft and squishy \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[It's so lewd having him lust over my body with all of this intensity!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[But his excitement...\\! He's so happy admiring my body and that makes me feel so good...]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[Hmmm ~ What should I do now..?]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Squeeze \\i[31]", "Spank \\i[30]"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Squeeze \\i[31]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["*squish*\\! *squish*"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you enjoying yourself baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I could sit here and play with your butt forever \\n[2]!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then keep going baby!\\! Play with \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Spank \\i[30]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Oh!\\! Geez, you're so naughty baby!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Does that turn you on?\\! Spanking \\n[11] like I've been a bad girl?!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You bet it does!\\! I love watching your butt jiggle when I spank you!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then do it again!\\! Spank \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 18, 1]}, {"code": 111, "indent": 1, "parameters": [0, 11, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3MomBendSexB_PreIdle", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3MomBendSexB1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 232, "indent": 1, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you just going to sit there and keep staring baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>You know you can't leave \\n[11] unsatisfied, right?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 118, "indent": 1, "parameters": ["1"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Finish", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 33]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 119, "indent": 2, "parameters": ["End"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Finish"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>*sigh*\\! I suppose it can't be helped..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 232, "indent": 2, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Alright baby.\\! I think that's enough fun for now."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 100, "switch1Valid": true, "switch2Id": 1, "switch2Valid": true, "variableId": 12, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [20, "5L<PERSON><PERSON><PERSON>3[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom10[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]You want to enjoy the view while I clean?"]}, {"code": 101, "indent": 0, "parameters": ["Mom10[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I'll allow that.\\! You dirty, dirty boy ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Your ass is the best \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Consider yourself lucky that you have a \\n[5] willing to show it off for you."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>You got that right!\\! I doubt anyone in the neighborhood is as lucky as I am!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*giggle*\\! Oh, baby ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's so soft ~\\! Your butt cheeks feel so soft and squishy \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[It's so lewd having him lust over my body with all of this intensity!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[But his excitement...\\! He's so happy admiring my body and that makes me feel so good...]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[Hmmm ~ What should I do now..?]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Squeeze \\i[31]", "Spank \\i[30]"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Squeeze \\i[31]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["*squish*\\! *squish*"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you enjoying yourself baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I could sit here and play with your butt forever \\n[2]!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then keep going baby!\\! Play with \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Spank \\i[30]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Oh!\\! Geez, you're so naughty baby!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Does that turn you on?\\! Spanking \\n[11] like I've been a bad girl?!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You bet it does!\\! I love watching your butt jiggle when I spank you!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then do it again!\\! Spank \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 11, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3MomBendSexC_PreIdle", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3MomBendSexC1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Are you just going to sit there and keep staring baby?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>You know you can't leave \\n[11] unsatisfied, right?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 118, "indent": 0, "parameters": ["1"]}, {"code": 102, "indent": 0, "parameters": [["Fuck", "Finish", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Fuck"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [21]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 34]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Finish"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>*sigh*\\! I suppose it can't be helped..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [37, 37, 0]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "View"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 119, "indent": 1, "parameters": ["1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 21, "y": 0}, {"id": 9, "name": "<PERSON>s on Phone (Day)", "note": "<Hitbox Up: 2> <Hitbox Right: 3>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 33, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 111, "indent": 0, "parameters": [0, 42, 1]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [12, "4LivingRoom_SisPhone", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 42, 1]}, {"code": 111, "indent": 1, "parameters": [0, 35, 1]}, {"code": 231, "indent": 2, "parameters": [11, "4LivingRoom_AuntReading", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 139, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 111, "indent": 0, "parameters": [0, 42, 1]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]Hey \\n[1], what's up?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(3, \"$gameSwitches.value(206) == false\")"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Sex", "Titfuck (+\\n[2])", "Leave"], 3, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [17]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Sex"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]You're really trying to get us busted, aren't you?"]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]But whatever.\\! I'm down."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 2, 0, 1]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 111, "indent": 1, "parameters": [1, 40, 0, 1, 0]}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 21]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 119, "indent": 1, "parameters": ["End"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Titfuck (+\\n[2])"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]Okay, you go ask her this time!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 30]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 119, "indent": 1, "parameters": ["End"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [12, "4LivingRoom_SisPhone", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 42, 1]}, {"code": 111, "indent": 1, "parameters": [0, 35, 1]}, {"code": 231, "indent": 2, "parameters": [11, "4LivingRoom_AuntReading", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 208, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 10}, {"id": 10, "name": "Mom Sex Sound Effects", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [18]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [14]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [33]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [9]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [22]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [18]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [37]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [9]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [22]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 8}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [8]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 30, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [22]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 23, "y": 0}, {"id": 11, "name": "<PERSON><PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Soft3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Soft4", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Mary_Soft4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Mary_Soft5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [135]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Mary_Soft6", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [140]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Mary_Soft2", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ1", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ2", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf BJ3", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ3", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf BJ5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [110]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex4", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex5", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex6", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 8}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex6", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 10}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 0}, {"id": 12, "name": "Dining Exit", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 26, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 14}, {"id": 13, "name": "<PERSON> Events", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 111, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 111, "indent": 0, "parameters": [0, 42, 1]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]There you are \\n[1]!\\! Guess what I got?!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Did you finish that new scenario for our next adventure?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Well, not quite..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]Admittedly, I've been in need of some inspiration."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]You know, something exciting to get the creative juices flowing!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]So I ordered a new video game and it arrived today!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh yeah?\\! What game did you get?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]Hyper Space Siblings 2!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>The sequel?! \\!No way!\\! I've been wanting to play that so bad!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]I knew you'd be excited!\\! You're free to play it whenever I'm not using it."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]Heck, you can even play it right now and I'll watch!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]I'd be very curious to see what you think of the first level."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [12, "4LivingRoom_SisPhone", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 42, 1]}, {"code": 111, "indent": 1, "parameters": [0, 35, 1]}, {"code": 231, "indent": 2, "parameters": [11, "4LivingRoom_AuntReading", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [16, 16, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [118, 118, 0]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 119, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Yes!\\! I'm so excited to play Hyper Space Siblings 2!"]}, {"code": 111, "indent": 0, "parameters": [0, 42, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>I suppose I've been hogging the couch for a while now..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>I'll give you two some space to play your video games."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>Are you sure you don't wanna watch \\n[6] \\n[3]?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Oh heavens no!\\! All of those twists and turns will make my head spin!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>You two have fun!"]}, {"code": 232, "indent": 1, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [2, "4LivingRoom_MC_Gaming", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Laser1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Laser1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 250, "indent": 0, "parameters": [{"name": "Explosion1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>No way!\\! This is insane!\\! And the graphics are crazy good too!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>It's a ton of fun isn't it?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>It's like I'm an actual commander of a ship!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>And wow is that space queen hot!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Laser1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Laser1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Space Queen!\\! That's it!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>What's it?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*chuckle*\\! You could call it a stroke of genius..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>No clue what you're talking about but okay..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 12]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 121, "indent": 0, "parameters": [119, 119, 1]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 119, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Let's see if I can beat that boss now!"]}, {"code": 111, "indent": 0, "parameters": [0, 42, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Oh, are you playing your game again?\\! I'll give you two some space."]}, {"code": 232, "indent": 1, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [2, "4LivingRoom_MC_Gaming", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Laser1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Laser1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 250, "indent": 0, "parameters": [{"name": "Explosion1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>ARGH!\\! No, no!\\! I totally dodged that!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*giggle*\\! Need any 'help' over there?"]}, {"code": 102, "indent": 0, "parameters": [["<PERSON><PERSON><PERSON><PERSON> (Fantasy)", "<PERSON><PERSON><PERSON><PERSON> (Regular)", "Nah"], 2, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<PERSON><PERSON><PERSON><PERSON> (Fantasy)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>Maybe I could use a little bit of help from the Space Queen again..?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>Nebula?\\! Mmm, I'm sure she doesn't mind helping out."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 12]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<PERSON><PERSON><PERSON><PERSON> (Regular)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>Another blowjob would be kind of cool..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>Mmm, I like that idea."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 15]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Nah"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>I'm good.\\! I'm so close to beating this boss!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>Ooh!\\! I wanna watch!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [35, 35, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [33, 33, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [73, 73, 0]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "Laser1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "Laser1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [119, 119, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 1, "y": 0}, {"id": 14, "name": "Mom LR <PERSON>", "note": "B = <PERSON> Out Repeat", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 201, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomSisCaughtA1[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [25, "3xMomSisCaughtE1[4x4]", 0, 0, 600, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 8]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 4]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomSisCaughtA1[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*Chu*\\! *Chu* ~ Mmmh ~ ♥"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mwah ~ Good morning baby ~ ♥"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 25 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 25 Loop"]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 600, 0, 100, 100, 255, 0, 90, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*yaaaawn* ~ So... sleepy..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*groan* ~ Good morning \\n[2] and..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [26, "3xMomSisCaughtE2[4x4]", 0, 0, 600, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 26 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 26 Loop"]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 600, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 600, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>WHAT THE HELL?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 231, "indent": 0, "parameters": [22, "3xMomSisCaughtB1[4x4]", 0, 0, 600, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Loop"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 600, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 600, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>\\n[4]?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*giggle*\\! Language, miss!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>You know what I've said about swearing."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Well excuse ME for seeing my \\n[5] and \\n[10] making out in the living room!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>He's just giving me a good morning kiss, dear."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Normally that doesn't involve extra tongue!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Ugh!\\! You two might as well be fucking out here!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Hmmm ~\\! That's not such a bad idea, actually..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Have you lost your mind \\n[2]?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Perhaps I have..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Imagine my surprise.\\! Seeing my \\n[8] naked in bed with his older \\n[7]..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Any \\n[5] wouldn't know how to respond to such a sight."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Such a naughty pair, the two of you!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>W-Well, I-\\! UGH!\\! Whatever!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>You two are so gross!\\! I'm out of here!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 600, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Um, I think she's mad at us \\n[2]..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Don't worry baby.\\! She's always a little grouchy in the morning."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Now let's try her idea out.\\! Fuck \\n[11] hard and fast!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 108, "indent": 0, "parameters": ["== SCENE START =="]}, {"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 245, "indent": 0, "parameters": [{"name": "PixieDeepFast", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 6]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 7]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Cum", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Cum"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [21, "3zMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3xMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mmmh ~\\! What a nice warm kiss ~ ♥"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(200) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Finish", "Kiss", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Kiss"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 122, "indent": 2, "parameters": [9, 9, 0, 0, 4]}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 3, "parameters": [9, 9, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 3, "parameters": [9, 9, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [16, 16, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [208, 208, 0]}, {"code": 121, "indent": 0, "parameters": [203, 203, 0]}, {"code": 121, "indent": 0, "parameters": [202, 202, 1]}, {"code": 121, "indent": 0, "parameters": [201, 201, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 201, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 231, "indent": 0, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomSisCaughtA1[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*Chu*\\! *Chu* ~ Mmmh ~ ♥"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mwah ~ Good morning baby ~ ♥"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA1[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA1[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 245, "indent": 0, "parameters": [{"name": "PixieDeepFast", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 6]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 7]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Cum", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Cum"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [21, "3zMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3xMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mmmh ~\\! What a nice warm kiss ~ ♥"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(200) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Finish", "Kiss", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Kiss"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 122, "indent": 2, "parameters": [9, 9, 0, 0, 4]}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 3, "parameters": [9, 9, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 3, "parameters": [9, 9, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [201, 201, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 201, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 231, "indent": 0, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomSisCaughtA1[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [25, "3xMomSisCaughtE1[4x4]", 0, 0, 600, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*Chu*\\! *Chu* ~ Mmmh ~ ♥"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mwah ~ Good morning baby ~ ♥"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 25 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 25 Loop"]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 600, 0, 100, 100, 255, 0, 90, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*yaaaawn* ~ Sleep ~ y..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [26, "3xMomSisCaughtE2[4x4]", 0, 0, 600, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 26 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 26 Loop"]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 600, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 600, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>AGAIN?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Can't you two get a room?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 231, "indent": 0, "parameters": [22, "3xMomSisCaughtB1[4x4]", 0, 0, 600, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Loop"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 600, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 600, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*giggle*\\! Good morning \\n[4]."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Why do the two of you have to be so WEIRD?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Can't I go get some waffles without seeing my \\n[5] and \\n[10] making out?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Aw, dear.\\! I'm afraid we're all out of waffles..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Seriously?!\\! UGH!\\! I'm going back to bed!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 600, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Remind me to put waffles on my grocery list, baby."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>But first, I need you to fuck \\n[11]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 108, "indent": 0, "parameters": ["== SCENE START =="]}, {"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 245, "indent": 0, "parameters": [{"name": "PixieDeepFast", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 6]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 7]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Cum", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Cum"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [21, "3zMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3xMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mmmh ~\\! What a nice warm kiss ~ ♥"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(200) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Finish", "Kiss", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Kiss"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 122, "indent": 2, "parameters": [9, 9, 0, 0, 4]}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomSisCaught_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 3, "parameters": [9, 9, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA3[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 3, "parameters": [9, 9, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [201, 201, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 22, "y": 1}, {"id": 15, "name": "Aunt LR BJ Event", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','3zAuntBJ1[4x4]');"]}, {"code": 655, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','3zAuntBJ2[4x4]');"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Were you reading my mind, love?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Or is your mind just as dirty as mine?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [18, "4LivingRoom_CouchCover", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zAuntBJ1[4x4]", 0, 0, 500, 360, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 12"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Are you in pain dear?\\! Your cock is throbbing!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>My oh my!\\! It's just begging for some relief now, isn't it..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Don't you worry, honey.\\! Your \\n[6] is here to save you ~ ♥"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [21, "3zAuntBJ2[4x4]", 0, 0, 500, 360, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 4]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 231, "indent": 0, "parameters": [25, "3zAuntBJ_Finish1", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "3zAuntBJ_Finish2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[Such a delicious cock!\\! It's so hot inside of my mouth!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[Let's hope no visitors come in and ruin my fun ~ ♥]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[Mmh!\\! I hope he enjoys my lips gliding along his cock ~ ♥]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[These nice plump lips are all yours, honey!]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>The way you're moving your tongue \\n[6] \\n[3]..!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I don't know...\\! I don't know how much longer I can hold on!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[Mmmh, it sounds like he's really struggling to not burst...]"]}, {"code": 111, "indent": 0, "parameters": [4, 12, 1, "Auntie"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>[Too bad darling.\\! Your \\n[12] needs this just as bad as you do!]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>[Too bad darling.\\! I need this just as bad as you do!]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[I want to swallow all of your hot, delicious cum!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 70, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 FinishBJ1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 500, 360, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 NoLoop"]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>*gulp*\\! *smack*"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Aaaah ~ "]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Whoa!\\! Did you really swallow all of my cum \\n[6] \\n[3]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>A lady never wastes a drop, honey ~ ♥"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Now run along before I get hungry for seconds..."]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [24, 24, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [44, 44, 0]}, {"code": 235, "indent": 0, "parameters": [18]}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 1]}, {"code": 201, "indent": 1, "parameters": [0, 2, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 4, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 21, "y": 1}, null, null, null, null, null]}