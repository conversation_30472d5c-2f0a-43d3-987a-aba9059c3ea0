{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "Master Bedroom", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0MomBedroom_Day", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 1544, 0, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 157, 1]}, {"code": 231, "indent": 1, "parameters": [10, "4MomBedroom_MomNight", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 42, 0]}, {"code": 231, "indent": 1, "parameters": [11, "4MomBedroom_Yoga", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 44, 0]}, {"code": 117, "indent": 1, "parameters": [21]}, {"code": 121, "indent": 1, "parameters": [44, 44, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 2, "name": "Exit", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 201, "indent": 0, "parameters": [0, 10, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 13}, {"id": 3, "name": "<PERSON> (Night)", "note": "<Hitbox Up: 3> <Hitbox Right: 6>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": false, "switch2Id": 42, "switch2Valid": false, "variableId": 24, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [0, 125, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]You came to vist just like I asked ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Mmm, you're up late..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Sex", "<PERSON><PERSON><PERSON>", "Leave"], 3, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Your eyes are fixated on my towel, sweetie.\\! You just can't get enough, can you?"]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Sorry \\n[2], I can't help but notice your towel clinging to all of your curves."]}, {"code": 101, "indent": 1, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]I made sure to tie this towel extra tight so you could have a nice view."]}, {"code": 101, "indent": 1, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]But I'm guessing you're ready to take it off for me now, aren't you..?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Sex"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [0, 125, 0]}, {"code": 101, "indent": 2, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Acting naughty during my bath time really got me going, you know..?"]}, {"code": 101, "indent": 2, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]And now that you've gotten \\n[11] all worked up, I'm going to need that cock of yours tonight..."]}, {"code": 101, "indent": 2, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]When you fuck me, would you prefer I keep my hair up or let it down?"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Oh, you'd like to fuck \\n[11] again, is that right?"]}, {"code": 101, "indent": 2, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]I was hoping you'd ask, honey.\\! Would you prefer I keep my hair up or let it down?"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["Hair Up", "Hair Down", "Special"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Hair Up"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 101, "indent": 2, "parameters": ["Mom4[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\bust[1]\\bustExp[1, 9]"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom4[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Do you like what you see, baby?"]}, {"code": 101, "indent": 2, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>You look so sexy \\n[2]!"]}, {"code": 101, "indent": 2, "parameters": ["Mom4[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]I can see you're eager to begin..."]}, {"code": 101, "indent": 2, "parameters": ["Mom4[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I want you inside me baby.\\! I want you to fuck me all night long."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Hair Down"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 101, "indent": 2, "parameters": ["Mom5[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\bust[1]\\bustExp[1, 9]"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom5[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Do you like what you see, baby?"]}, {"code": 101, "indent": 2, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>You look so sexy \\n[2]!"]}, {"code": 101, "indent": 2, "parameters": ["Mom5[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]I can see you're eager to begin..."]}, {"code": 101, "indent": 2, "parameters": ["Mom5[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I want you inside me baby.\\! I want you to fuck me all night long."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Special"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 1]Oh!"]}, {"code": 101, "indent": 2, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]I guess I had never really considered that option before..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 121, "indent": 2, "parameters": [60, 60, 0]}, {"code": 101, "indent": 2, "parameters": ["Mom6[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\bust[1]\\bustExp[1, 9]"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Mom6[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Do you like what you see, baby?"]}, {"code": 101, "indent": 2, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>You look so sexy \\n[2]!"]}, {"code": 101, "indent": 2, "parameters": ["Mom6[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]I can see you're eager to begin..."]}, {"code": 101, "indent": 2, "parameters": ["Mom6[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I want you inside me baby.\\! I want you to fuck me all night long."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 4]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 119, "indent": 1, "parameters": ["<PERSON><PERSON>"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON><PERSON><PERSON>"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([22,5,'C'], true);"]}, {"code": 119, "indent": 1, "parameters": ["<PERSON><PERSON>"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [10, "4MomBedroom_MomNight", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 42, 0]}, {"code": 231, "indent": 1, "parameters": [11, "4MomBedroom_Yoga", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["<PERSON><PERSON>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 157, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 8}, {"id": 4, "name": "Exercise", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 42, "switch1Valid": false, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 42, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>It'd be kind of weird to exercise this late..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 11}, {"id": 5, "name": "<PERSON> <PERSON><PERSON>e <PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [0, 11, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3MomBendSexD_PreIdle", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3MomBendSexD1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]You're so dirty, you know that?"]}, {"code": 101, "indent": 0, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]But I'll let you have fun for a little if you insist ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I love your ass so much \\n[2]!\\! It's so big and amazing!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*giggle*\\! I suppose I'll take that as a compliment..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Of course!\\! Having a big butt is a huge compliment!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh \\n[1] ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's so soft ~\\! Your butt cheeks feel so soft and squishy \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[It's so lewd having him lust over my body with all of this intensity!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[But his excitement...\\! He's so happy admiring my body and that makes me feel so good...]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[Hmmm ~ What should I do now..?]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Squeeze \\i[31]", "Spank \\i[30]"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Squeeze \\i[31]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["*squish*\\! *squish*"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you enjoying yourself baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I could sit here and play with your butt forever \\n[2]!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then keep going baby!\\! Play with \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Spank \\i[30]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Oh!\\! Geez, you're so naughty baby!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Does that turn you on?\\! Spanking \\n[11] like I've been a bad girl?!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You bet it does!\\! I love watching your butt jiggle when I spank you!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then do it again!\\! Spank \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 18, 1]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you just going to sit there and keep staring baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>You know you can't leave \\n[11] unsatisfied, right?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 118, "indent": 1, "parameters": ["1"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Finish", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 35]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 119, "indent": 2, "parameters": ["End"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Finish"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>*sigh*\\! I suppose it can't be helped..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 232, "indent": 2, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 232, "indent": 1, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 250, "indent": 1, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Alright baby.\\! I think that's enough fun for now."]}, {"code": 101, "indent": 1, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Unless you were planning on sticking around for a little longer..?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 1, "y": 0}, null, null, null, null, null]}