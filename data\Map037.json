{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "Master Bedroom", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0MomBedroom_DayYoga", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 1544, 0, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 166, 0]}, {"code": 111, "indent": 1, "parameters": [0, 214, 1]}, {"code": 231, "indent": 2, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 209, 0]}, {"code": 231, "indent": 1, "parameters": [10, "4MomBedroom_Trio2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 214, 0]}, {"code": 231, "indent": 2, "parameters": [10, "4MomBedroom_Trio1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [10, "4MomBedroom_Duo", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 165, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Mom5[BUST][Exp4x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Aunt4[BUST][Exp3x3]')"]}, {"code": 231, "indent": 0, "parameters": [91, "7Night", 1, 0, 1150, 50, 100, 100, 255, 0]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 1]Oh, that was just PERFECT!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 7]I'm so happy we've finally gotten over that senseless hurdle."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 2]Prentending like I wasn't aware of your not-so-secret relationship was a bit tiresome..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 8]Oh!\\! I was just dying to join in on the fun!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 8]It's going to feel a bit strange not having to keep this romance a secret from you anymore \\n[3]."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 6]You know what they say.\\! It's never a good idea to keep secrets."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Man, that was awesome...\\! This is the best summer ever!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 3]I'm sure you're happy about all of this too baby."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 9]You get to fuck your \\n[5] and your \\n[6] any time you want!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 7]I just hope \\n[1] can keep up with our demands ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 6]I'm a woman with needs dear, so you better not forget to fuck me every day!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 1]W-Wait!\\! Don't forget about your \\n[5], \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 8]I still need some private time with you too, you know?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Don't worry!\\! I'll have sex with you two all day, every day!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I just...\\! Am feeling a little..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 5]Aaand he's asleep."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 4]Perhaps we got him a little too excited tonight..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 6]I'd say that's the sign of a job well done."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 0]The two of us should probably get some rest too."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [19, 19, 0]}, {"code": 121, "indent": 0, "parameters": [93, 93, 0]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 8, 8, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [15, 15, 1, 0, 1]}, {"code": 122, "indent": 0, "parameters": [17, 17, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [167, 167, 1]}, {"code": 121, "indent": 0, "parameters": [164, 164, 1]}, {"code": 121, "indent": 0, "parameters": [166, 166, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 121, "indent": 0, "parameters": [165, 165, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, {"id": 2, "name": "Exit", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 166, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 201, "indent": 0, "parameters": [0, 10, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 214, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 13}, {"id": 3, "name": "<PERSON> & Aunt (& Sis)", "note": "<Hitbox Up: 5> <Hitbox Right: 6>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": false, "switch2Id": 42, "switch2Valid": false, "variableId": 24, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon romance;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Mom4[BUST][Exp4x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Aunt4[BUST][Exp3x3]')"]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[2]?!\\! \\n[6] \\n[3]?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 7]Surprised to see us, dear?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Well yeah!\\! You're both sitting here naked!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 5]You see, your \\n[5] and I had a very deep and passionate conversation about you."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 1]But she came up with a great idea!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 1]M-My idea?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 8]\\n[3]..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 7]Don't get all shy on me now!\\! Go on and tell him!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 8]We...\\! We thought that maybe there was enough room for the both of us to share."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Share?\\! Are you talking about me?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 6]Mhmm ~\\! Who else would we be talking about?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 2]Your \\n[5] certainly had to do a lot of convincing for me to come around to the idea..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 1]Convincing?!\\! B-But it was your ide-"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 1]But after thinking about it-\\! I would be selfish to not let you enjoy pleasing both of us!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 8]We thought maybe you would like to have sex with us together..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>T-Together?!\\! Do you mean a threesome?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 9]*giggle*\\! Yes, baby.\\! Sex with both of us, right now."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>You're not joking?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 6]We would never joke about such a thing..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bustExp[3, 7]So stop standing there and come give us both a real good fuck!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [4]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 25]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 168, "switch1Valid": true, "switch2Id": 42, "switch2Valid": false, "variableId": 24, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon romance;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>We've been waiting for you baby ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(4, \"$gameSwitches.value(220) == false\")"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Threesome Sex", "Change Location", "Invite \\n[4]", "Leave"], 4, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Isn't your \\n[5]'s body just stunning?!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Her shape!\\! Those curves!\\! Those wonderful breasts!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>H-Hey...\\! You're embarrassing me..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Mmm, and that adorable blushing face!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Threesome Sex"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [0, 170, 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>It's gotten quite late.\\! Let's wait until tomorrow."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>Then we can be well rested for the next time you fuck both of us!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>We were hoping you would fuck us again!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>Don't keep us in suspense any longer!\\! Your \\n[5] and \\n[6] are desperate to get fucked!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 26]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Change Location"]}, {"code": 102, "indent": 1, "parameters": [["Separate Activities", "<PERSON><PERSON>", "Cancel"], 2, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Separate Activities"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [10]}, {"code": 201, "indent": 2, "parameters": [0, 22, 0, 14, 2, 0]}, {"code": 121, "indent": 2, "parameters": [157, 157, 1]}, {"code": 121, "indent": 2, "parameters": [209, 209, 1]}, {"code": 121, "indent": 2, "parameters": [168, 168, 1]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "<PERSON><PERSON>"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [10]}, {"code": 201, "indent": 2, "parameters": [0, 22, 0, 14, 2, 0]}, {"code": 121, "indent": 2, "parameters": [157, 157, 0]}, {"code": 121, "indent": 2, "parameters": [209, 209, 1]}, {"code": 121, "indent": 2, "parameters": [168, 168, 1]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Cancel"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Invite \\n[4]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 121, "indent": 1, "parameters": [209, 209, 0]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 209, "switch1Valid": true, "switch2Id": 42, "switch2Valid": false, "variableId": 24, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon romance;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>There you are baby ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Foursome Sex", "Change Location", "Leave"], 3, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>It appears you're in quite an interesting situation, \\n[1]."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>Three smokin' hot babes are sitting right in front of you!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>*giggle*\\! So will you be joining us?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Foursome Sex"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [0, 170, 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>It's gotten quite late.\\! Let's wait until tomorrow."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>Then we can be well rested for the next time you fuck both of us!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>Are you ready to go \\n[1]?!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>Mmm ~\\! That big erection says it all..."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>Come lay down baby.\\! Right next to \\n[11] ~ ♥️"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 36]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Change Location"]}, {"code": 102, "indent": 1, "parameters": [["Separate Activities", "Pool (\\n[2] & \\n[3])", "Cancel"], 2, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Separate Activities"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [10]}, {"code": 201, "indent": 2, "parameters": [0, 22, 0, 14, 2, 0]}, {"code": 121, "indent": 2, "parameters": [157, 157, 1]}, {"code": 121, "indent": 2, "parameters": [168, 168, 1]}, {"code": 121, "indent": 2, "parameters": [209, 209, 1]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Pool (\\n[2] & \\n[3])"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [10]}, {"code": 201, "indent": 2, "parameters": [0, 22, 0, 14, 2, 0]}, {"code": 121, "indent": 2, "parameters": [157, 157, 0]}, {"code": 121, "indent": 2, "parameters": [168, 168, 1]}, {"code": 121, "indent": 2, "parameters": [209, 209, 1]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Cancel"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 214, "switch1Valid": true, "switch2Id": 42, "switch2Valid": false, "variableId": 24, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon romance;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [17, 17, 0, 0, 28]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 0, 32]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 2]}, {"code": 121, "indent": 0, "parameters": [220, 220, 0]}, {"code": 111, "indent": 0, "parameters": [0, 199, 1]}, {"code": 356, "indent": 1, "parameters": ["ChangeTitle1 Title_Trio"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Aunt1[BUST][Exp3x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Mom1[BUST][Exp4x3]')"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]Hey honey.\\! Are you feeling better?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Huh?\\! Oh, yeah, I feel fine."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>But what are you all doing in here?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]We've just been discussing a few things..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]We realized we were a bit too pushy demanding attention from you so selfishly."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]We felt that we all owe you an apology, honey."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 2]Yeah, \\n[2] and \\n[6] \\n[3] are right..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 8]But it's tough to blame us all when we live with such a cutie!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]You gave us all quite a scare when you passed out earlier."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]But in that moment, we stopped arguing and instantly rushed to care for you."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 0]It was clear to us that we all love you and want to make you happy."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 1]And so we were all able to put our differences aside!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Really?\\! Well, I'm sorry to scare you guys."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 0]Don't be.\\! We're just glad you're okay."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Now that the secrets are all out, does that mean I can't, y'know..?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 7]Do 'it' with us..?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Weelll ~\\! That is another thing we were discussing too."]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]You see, we've reached a 'compromise'."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 8]We're all yours to share!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Any time you want us ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Really?!\\! Any time?!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I can have sex with all of you still?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]That's right, baby."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Well then what are we waiting for?!\\! Let's do it right now!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 1]Hah!\\! See?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 0]I told you two that news would spark him back up!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]*giggle*\\! Yes, you did \\n[4]."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 3]But he can only fuck one of us at a time.\\! Who goes first?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh no...\\! Not THIS choice again!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 1]The answer is clear as day!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]You should fuck your \\n[5] first."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 1]Really?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]W-Why should I go first..?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]We owe all of our naughty escapades to you!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]If it weren't for that oh, so innocent handjob you were giving him when we showed up..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]They were?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 1]I had no idea you saw!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>You saw that \\n[6] \\n[3]?!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Sadly I was too late..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]But your \\n[5]'s blushed face told me everything I needed to know."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]Your \\n[6]'s intuition has never failed her!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 3]Ew..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]*giggle*\\! And here I thought we were careful enough..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 7]It does make sense though \\n[6] \\n[3]."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 1]\\n[2] started this crazy summer so she gets dibs!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]It's only right."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]I suppose I won't object when you both agree so clearly..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Baby?\\! Will you come fuck \\n[11]?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [4]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 36]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 8}, {"id": 4, "name": "Exercise", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 42, "switch1Valid": false, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 42, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Why would I exercise right now?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 11}, {"id": 5, "name": "Aunt<PERSON>is Sex Event", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 218, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Whew!\\! That was AMAZING!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>My heart feels so incredibly full seeing just how close we all are..."]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>.\\|  .\\|  .\\|  zzz ~ zzZZ ~ ZZZZ"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Uhhh ~\\! \\n[2]..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>*giggle*\\! Oh dear, it looks like she fell asleep!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>She hit the sack already?!\\! But we haven't had our turn yet!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>That's alright.\\! She deserves some rest after such an eventful day."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>But that doesn't mean we have to stop just yet ~ ♥️"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 231, "indent": 0, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 19 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 19 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 9]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedSlow_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>S-So, you knew this whole time \\n[6] \\n[3]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Knew?!\\! Why, I orchestrated so much of it!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Ahh ~\\! Your \\n[2] has always been bad at keeping secrets!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>And I could sense the you and your \\n[10] were up to something naughty too!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Well I'm ~\\! Mh!\\! Glad you did..!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Because now I can get fucked by \\n[1] without having to hide it!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>*giggle*\\! Mmm ~ Isn't it so much fun ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [19, "3zSisAunt_Linda[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3zSisAunt2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Such a naughty boy ~\\! Letting \\n[12] ride your face ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>OH!\\! I felt that!\\! You dirty, dirty \\n[9]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Mmmh ~\\! But don't stop doing that ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [19, "3zSisAunt_Linda[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3zSisAunt2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 11]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedFast_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Ohhh, shit \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Your cock feels so good!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>It's kinda hot knowing you were just inside \\n[2] too ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Mmh, keep going baby!\\! I'm getting close ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [19, "3zSisAunt_Linda[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3zSisAunt2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 3"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Ah! Yes baby!\\! Anh ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Y-You're about to make \\n[12] cum baby!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>M-<PERSON> too \\n[6] \\n[3]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I can feel him s-swelling up!\\! Ngh, fuck ~ !"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Finish", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [19, "3zSisAunt_Linda[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3zSisAunt2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 0]}, {"code": 231, "indent": 1, "parameters": [20, "3xSisAunt3[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3zSisAunt3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Finish1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>. . .\\! That felt so fucking good!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Y-Yes, it did...\\! I'm exhausted..!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I'm just gonna...\\! Rest for a little bit..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Yeah...\\! Me too ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [18]}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 1]}, {"code": 401, "indent": 0, "parameters": ["A few days later..."]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Bell3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Good morning Jenn!\\! So sorry, I'm late!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\n[1] just kept me a little...\\! \"pre-occupied\" this morning!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>How has my summer been?!\\! W-Well, it..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>It's been good!\\! You know, just-"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Just keeping up with all the house chores!"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 43, 0, 14, 0, 0]}, {"code": 121, "indent": 0, "parameters": [214, 214, 1]}, {"code": 121, "indent": 0, "parameters": [218, 218, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 218, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [40]}, {"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 231, "indent": 0, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 19 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 19 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 9]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedSlow_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Hah!\\! \\n[2] just can't hang..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>That just means we have \\n[1] all to ourselves..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I don't mind that!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I'm gonna ride him senseless!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>*giggle*\\! I like that idea ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [19, "3zSisAunt_Linda[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3zSisAunt2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Such a naughty boy ~\\! Letting \\n[12] ride your face ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>OH!\\! I felt that!\\! You dirty, dirty \\n[9]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Mmmh ~\\! But don't stop doing that ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [19, "3zSisAunt_Linda[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3zSisAunt2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 11]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedFast_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Ohhh, shit \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Your cock feels so good!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>It's kinda hot knowing you were just inside \\n[2] too ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Mmh, keep going baby!\\! I'm getting close ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [19, "3zSisAunt_Linda[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3zSisAunt2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 3"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Ah! Yes baby!\\! Anh ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Y-You're about to make \\n[12] cum baby!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>M-<PERSON> too \\n[6] \\n[3]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I can feel him s-swelling up!\\! Ngh, fuck ~ !"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Finish", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [18, "3xSisAunt_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [19, "3xSisAunt_<PERSON>[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3xSisAunt2[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [19, "3zSisAunt_Linda[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3zSisAunt2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 0]}, {"code": 231, "indent": 1, "parameters": [20, "3xSisAunt3[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3zSisAunt3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Finish1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>. . .\\! That felt so fucking good!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Y-Yes, it did...\\! I'm exhausted..!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I'm just gonna...\\! Rest for a little bit..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Yeah...\\! Me too ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [18]}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 14, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [19, 19, 0]}, {"code": 121, "indent": 0, "parameters": [218, 218, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 1, "y": 0}, null, {"id": 7, "name": "Mom Sex Sound Effects", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [43]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [32]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 130, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [21]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 140, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [9]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 140, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 25, "y": 0}, {"id": 8, "name": "<PERSON><PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Soft3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Soft4", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Mary_Soft4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Mary_Soft5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [135]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Mary_Soft6", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [140]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Mary_Soft2", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ1", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ2", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf BJ3", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ3", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf BJ5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [110]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex4", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex5", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex6", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 8}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex6", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 9}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Soft3", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Soft4", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Soft1", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 10}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Loud3", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Loud1", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Soft1", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 11}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Loud2", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Short2", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Loud3", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 12}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 0}, null, null]}