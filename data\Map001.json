{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6019, 6017, 6017, 6017, 6017, 6017, 6017, 6017, 6017, 6017, 6021, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6016, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 6016, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6016, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 6016, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6016, 3234, 3220, 3220, 3220, 3220, 3220, 3220, 3220, 3236, 6028, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6016, 3216, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3224, 6990, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6016, 3216, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3224, 6026, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6016, 3216, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3224, 6016, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6016, 3240, 3228, 3228, 3228, 3228, 3228, 3228, 3228, 3238, 6016, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 6025, 6017, 6017, 6017, 6017, 6017, 6017, 6017, 6017, 6017, 6023, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 0, 292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 228, 0, 0, 0, 130, 0, 240, 185, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 236, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["1Mom1", 7, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["Hey there big boy..."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 6}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [1, "2Bedroom_Day", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [3, "3Bedroom_Door", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [10, 10, 0, 0, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["*Yaaaawn*\\! Whoops, must have overslept again..."]}, {"code": 101, "indent": 0, "parameters": ["1Mom1", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["Good morning."]}, {"code": 102, "indent": 0, "parameters": [["Explore", "Talk"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Explore"]}, {"code": 201, "indent": 1, "parameters": [0, 2, 7, 6, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Talk"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 1}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 2, 15, 8, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 19, "y": 7}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 4, 6, 6, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 18, "y": 6}, {"id": 5, "name": "EV005", "note": "click_activate!", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [99, "1Msg_Box", 1, 0, 640, 600, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["1Mom1", 7, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["Hey there big boy..."]}, {"code": 101, "indent": 0, "parameters": ["1Mom1", 7, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["We're going to do a dialogue test in order to see how much I "]}, {"code": 401, "indent": 0, "parameters": ["can fit on the screen at one time."]}, {"code": 101, "indent": 0, "parameters": ["", 7, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["And of course, we have to try the dialogue out without a bust on the screen.  Who knows, this might be the preferred method of speaking."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 6}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "3HCMomHJ_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [11, "3HCMomHJ1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 7, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [11, "3HCMomHJ2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 7, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [11, "3HCMomHJ3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 7, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [11, "3HCMomHJ4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 7, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 5}, {"id": 7, "name": "Siren Message", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Siren>Hey! Thank you so much for playing this short build of House Chores!\\! I wanted to leave a message "]}, {"code": 401, "indent": 0, "parameters": ["in case players had a few questions."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Siren>The build you played just now was more of a demo.\\! I plan to add a few "]}, {"code": 401, "indent": 0, "parameters": ["activities, minigames, & quests to add more variety to House Chores."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Siren>If you played the build released in 2019, you may notice not too much was "]}, {"code": 401, "indent": 0, "parameters": ["added in this update. That's because of two reasons."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Siren>One- I ran into some snags in development which halted art for a little bit. For the most part, I think that's all figured out right now!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Siren>And two- If you didn't notice, this update is on an entirely new engine!\\! This one is "]}, {"code": 401, "indent": 0, "parameters": ["much easier for me to work with and will mean I can do more fun & creative stuff in House Chores."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Siren>There's quite a lot that I want to polish & add but I want to "]}, {"code": 401, "indent": 0, "parameters": ["thank you for being part of the journey <3"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Siren>Thank you for your support and patience for House Chores! "]}, {"code": 401, "indent": 0, "parameters": ["If you'd like to support development or stay in the loop on updates, "]}, {"code": 401, "indent": 0, "parameters": ["check out the website at "]}, {"code": 401, "indent": 0, "parameters": ["www.patreon.com/sirensparadise"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 13}, {"id": 8, "name": "Scene Player Shortcut", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 231, "indent": 0, "parameters": [50, "3MomBJ_A2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [50, "3MomBJ_A3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 121, "indent": 0, "parameters": [15, 15, 0]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 13}, {"id": 9, "name": "Siren Message", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 19, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["For finishing up Beta 0.2.9, I'm showing a sneak peek at one of the new "]}, {"code": 401, "indent": 0, "parameters": ["events that will appear in Beta 0.3! Currently, it does not feature "]}, {"code": 401, "indent": 0, "parameters": ["dialogue."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Would you like to see it now or wait for the update in early June?"]}, {"code": 102, "indent": 0, "parameters": [["See it Now", "Wait"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "See it Now"]}, {"code": 231, "indent": 1, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3zMomBJ[4x4]", 0, 0, 600, 233, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 3]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Cumshot", "volume": 70, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 NoLoop"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [21]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Wait"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 14}, {"id": 10, "name": "Scene Player Shortcut", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 231, "indent": 0, "parameters": [50, "3zAuntHJ[4x4]", 0, 0, 480, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 13}, {"id": 11, "name": "Test", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomBathPai_Water", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomBathPai1[4x4]", 0, 0, 640, 120, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["setcursor: cursor2 "]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([3,7,'A'], true);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 10}, {"id": 12, "name": "Test", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomKitchenBang1[4x4]", 0, 0, 700, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 10}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 121, "indent": 0, "parameters": [15, 15, 0]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 10}, {"id": 14, "name": "Test", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 28, 13, 14, 8, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["MC", 2], "indent": null}]}, {"code": 211, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 8}, {"id": 15, "name": "Test", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 29, 14, 10, 2, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC1", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["MC1", 4], "indent": null}]}, {"code": 211, "indent": 0, "parameters": [0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 8}, {"id": 16, "name": "Costume Test", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["Choose \\n[2]'s outfit!"]}, {"code": 102, "indent": 1, "parameters": [["Regular", "Casual"], -2, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Regular"]}, {"code": 231, "indent": 2, "parameters": [20, "9CostumeLinda1", 0, 0, 150, 25, 100, 100, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["Is this the outfit you want her to wear?"]}, {"code": 102, "indent": 2, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "Yes"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "No"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Casual"]}, {"code": 231, "indent": 2, "parameters": [20, "9CostumeLinda2", 0, 0, 150, 25, 100, 100, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["Is this the outfit you want her to wear?"]}, {"code": 102, "indent": 2, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "Yes"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "No"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 403, "indent": 1, "parameters": [6, null]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 10}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["<PERSON> Adjusted"]}, {"code": 122, "indent": 0, "parameters": [32, 32, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [33, 33, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 0, 20]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 8}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 201, "indent": 0, "parameters": [0, 41, 13, 14, 8, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC1", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["MC1", 5], "indent": null}]}, {"code": 211, "indent": 0, "parameters": [1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 8}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [7]}, {"code": 201, "indent": 0, "parameters": [0, 21, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [176, 176, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 8}, {"id": 20, "name": "Gallery Unlock", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Aunt", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 0"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 1"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 2"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 3"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 4"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 5"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 6"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 7"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 8"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 9"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 10"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 11"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 12"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 13"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 14"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 15"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 16"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 17"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 18"]}, {"code": 356, "indent": 0, "parameters": ["enable_picture 19"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 6}, {"id": 21, "name": "Costume Test", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 231, "indent": 0, "parameters": [20, "5EmilyTease1[4x4]", 0, 0, 150, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "5EmilyTease1a[4x4]", 0, 0, 150, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [20, "5EmilyTease2[4x4]", 0, 0, 150, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "5EmilyTease2a[4x4]", 0, 0, 150, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 121, "indent": 0, "parameters": [96, 96, 0]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 16]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 8}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 121, "indent": 0, "parameters": [218, 218, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 10}, {"id": 23, "name": "Costume Test", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 121, "indent": 0, "parameters": [32, 32, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 117, "indent": 0, "parameters": [11]}, {"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Aunt1[BUST][Exp3x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Mom1[BUST][Exp4x3]')"]}, {"code": 241, "indent": 0, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [90, "8StatUp_Group", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 5]No fair!\\! It's my turn to share time with \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Oh, save it, Sis..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 3]We all know you've been sneaking off and getting extra alone time."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Do you really think \\n[4] and I can't hear what goes on behind the "]}, {"code": 401, "indent": 0, "parameters": ["laundry room door?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]The two of you were listening?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 3]Well, yeah \\n[2]..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 6]Did you really think we couldn't hear you moaning like that?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]W-Well, I..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 0]So I think it's quite clear that I get to spend more time with \\n[1] today."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]I've been dying to have him bend me over and fuck me nice and hard ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 5]\\n[3]!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]WHAT THE HELL?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 6]You mean to tell me that YOU have been fucking him TOO \\n[6] \\n[3]?!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]Aww, you weren't aware darling..?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]\\n[1] has been my little boy-toy for a while now."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 4]There's no way..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 3]So we've all been fucking \\n[1] this whole time?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Yes...\\! All three of us have been."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]And there he is.\\! Right on time."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>H-Hey guys!\\! Why is everyone standing around in the living room?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]You've been fucking \\n[6] \\n[3] too?!\\! Y-You PERV!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>O-Oh...\\! You, uhh ~\\! You found out about that..?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Your \\n[7] doesn't appear to understand how deep and special our relationship is."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]I was just telling them both that you and I were going to spend some alone time today."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]NO!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 2]I-\\! I mean..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 3]I thought you might want to play through another campaign with me today, right \\n[1]?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]I'm afraid that just won't do..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]Not when I need some help with my yoga practice later today."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]But we're all in disagreement..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 2]Yup.\\! Only one person here can decide the plans then."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]I suppose you both are right about that..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 0]So, \\n[1]."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 0]Three women, fighting for your attention."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Who will you choose today?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["\\c[23]\\n[2]", "\\c[21]\\n[6] \\n[3]", "\\c[10]\\n[4]"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[23]\\n[2]"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 3]Be sure to think your decision over carefully..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]You want to spend time with \\n[12], right?"]}, {"code": 241, "indent": 1, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Right...\\! Of course..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[21]\\n[6] \\n[3]", "\\c[21]\\n[6] \\n[3]", "\\c[21]\\n[6] \\n[3]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 5]W-Wait a sec!"]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 7]You know there's only one right answer \\n[1]."]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 8]So let's go 'hang out'!"]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You see, I ~"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[10]\\n[4]", "\\c[10]\\n[4]", "\\c[10]\\n[4]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[21]\\n[6] \\n[3]"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 7]You know there's only one right answer \\n[1]."]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 8]So let's go 'hang out'!"]}, {"code": 241, "indent": 1, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Right...\\! Of course..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[10]\\n[4]", "\\c[10]\\n[4]", "\\c[10]\\n[4]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[10]\\n[4]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Don't make a hasty deicision, baby!"]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Think carefully about this, now."]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]You want to spend time with \\n[11], right?"]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You see, I ~"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[23]\\n[2]", "\\c[23]\\n[2]", "\\c[23]\\n[2]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[10]\\n[4]"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Think carefully baby!"]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]You want to spend time with \\n[11], right?"]}, {"code": 241, "indent": 1, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Right...\\! Of course..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[23]\\n[2]", "\\c[23]\\n[2]", "\\c[23]\\n[2]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[23]\\n[2]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]We all know who you truly want to make love to, dear."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 3]Don't let those two pressure you!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]You want to spend time with \\n[12], right?"]}, {"code": 101, "indent": 1, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You see, I ~"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 102, "indent": 1, "parameters": [["\\c[21]\\n[6] \\n[3]", "\\c[21]\\n[6] \\n[3]", "\\c[21]\\n[6] \\n[3]"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\c[21]\\n[6] \\n[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 1]This new adventure is my best one yet!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[2]\\bustExp[2, 8]You'll love the sexy quest reward!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]This new pose will require a lot of careful assistance."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]We'll be connected in all sorts of intimate ways..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I-\\! I don't know if I can-"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Don't worry baby.\\! You know you can come to \\n[11]."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 102, "indent": 0, "parameters": [["I don't...", "feel so...", "...good."], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "I don't..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "feel so..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "...good."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [10]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>G-Guys. . .\\! I. . ."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I feel kinda. . .\\! . . . funny ~ "]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Damage2", "volume": 90, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Girls>\\n[1]!"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 14, 0, 0]}, {"code": 121, "indent": 0, "parameters": [214, 214, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 216, 0]}, {"code": 111, "indent": 1, "parameters": [0, 166, 0]}, {"code": 111, "indent": 2, "parameters": [0, 217, 1]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 231, "indent": 3, "parameters": [90, "8StatUp_Group", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 3, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 3, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 3, "parameters": [15]}, {"code": 250, "indent": 3, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 250, "indent": 3, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 3, "parameters": ["A very important event will take place in the Living Room!\\! Go "]}, {"code": 401, "indent": 3, "parameters": ["and see it!"]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 232, "indent": 3, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 3, "parameters": [90]}, {"code": 121, "indent": 3, "parameters": [217, 217, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 12}]}