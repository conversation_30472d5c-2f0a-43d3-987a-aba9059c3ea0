{"airship": {"bgm": {"name": "Ship3", "pan": 0, "pitch": 100, "volume": 90}, "characterIndex": 3, "characterName": "Vehicle", "startMapId": 0, "startX": 0, "startY": 0}, "armorTypes": ["", "General <PERSON><PERSON>", "Magic Armor", "Light Armor", "Heavy Armor", "Small Shield", "Large Shield"], "attackMotions": [{"type": 0, "weaponImageId": 0}, {"type": 1, "weaponImageId": 1}, {"type": 1, "weaponImageId": 2}, {"type": 1, "weaponImageId": 3}, {"type": 1, "weaponImageId": 4}, {"type": 1, "weaponImageId": 5}, {"type": 1, "weaponImageId": 6}, {"type": 2, "weaponImageId": 7}, {"type": 2, "weaponImageId": 8}, {"type": 2, "weaponImageId": 9}, {"type": 0, "weaponImageId": 10}, {"type": 0, "weaponImageId": 11}, {"type": 0, "weaponImageId": 12}], "battleBgm": {"name": "Battle1", "pan": 0, "pitch": 100, "volume": 90}, "battleback1Name": "Grassland", "battleback2Name": "Grassland", "battlerHue": 0, "battlerName": "Dragon", "boat": {"bgm": {"name": "Ship1", "pan": 0, "pitch": 100, "volume": 90}, "characterIndex": 0, "characterName": "Vehicle", "startMapId": 0, "startX": 0, "startY": 0}, "currencyUnit": "$", "defeatMe": {"name": "Defeat1", "pan": 0, "pitch": 100, "volume": 90}, "editMapId": 8, "elements": ["", "Physical", "Fire", "Ice", "Thunder", "Water", "Earth", "Wind", "Light", "Darkness"], "equipTypes": ["", "Weapon", "Shield", "Head", "Body", "Accessory"], "gameTitle": "House Chores", "gameoverMe": {"name": "Gameover1", "pan": 0, "pitch": 100, "volume": 90}, "locale": "en_US", "magicSkills": [1], "menuCommands": [false, false, false, false, false, true], "optDisplayTp": true, "optDrawTitle": false, "optExtraExp": false, "optFloorDeath": false, "optFollowers": true, "optSideView": false, "optSlipDeath": false, "optTransparent": true, "partyMembers": [1], "ship": {"bgm": {"name": "Ship2", "pan": 0, "pitch": 100, "volume": 90}, "characterIndex": 1, "characterName": "Vehicle", "startMapId": 0, "startX": 0, "startY": 0}, "skillTypes": ["", "Magic", "Special"], "sounds": [{"name": "choice_confirm_02", "pan": 0, "pitch": 150, "volume": 90}, {"name": "choice_confirm_01", "pan": 0, "pitch": 100, "volume": 90}, {"name": "choice_cancel_02", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Buzzer1", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Equip1", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Save", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Load", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Battle1", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Run", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Attack3", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Damage4", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Collapse1", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Collapse2", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Collapse3", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Damage5", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Collapse4", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Recovery", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Miss", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Evasion1", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Evasion2", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Reflection", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Shop1", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Item3", "pan": 0, "pitch": 100, "volume": 90}, {"name": "Item3", "pan": 0, "pitch": 100, "volume": 90}], "startMapId": 17, "startX": 0, "startY": 12, "switches": ["", "Morning", "Day", "Evening", "Night", "Temp Time Slot", "Midnight", "", "", "<Global Meta><PERSON><PERSON> Qual Changed", "Debug", "<Global Meta>Low Quality Mode", "(Old) LQ Toggle Visible", "(Old) Low Quality Mode", "Android Version", "Cheat Test", "Alternate Scene Toggle", "Cum Out Toggle", "<Global Meta> Message Transparency", "Long Wake", "Scene Loaded In", "M Dialogue Advance", "Bedroom Clean Start", "BR Clean Finish", "TV Unlocked", "M's <PERSON><PERSON>?", "Disable Video Games", "Fap Unlocked", "<PERSON> Bath Peeked", "Extra Cutscene", "Mom Soap Emptied", "Need Talk to Mom", "Sis & Aunt Intro Cutscene", "House Occupied", "Dishes Clean", "Mom & Aunt Talking", "Mom Bathroom Fondle Event", "M Points Chk <PERSON>gger", "M Clean Pt Chk Trigger", "M Dishes Scene", "A Dialogue Advance", "<PERSON> Mat Purchased", "Yoga Mat Arrived", "Daily Exercise", "Aunt Points Chk <PERSON>", "Aunt <PERSON><PERSON><PERSON><PERSON><PERSON>", "Aunt Peeped/Caught", "Aunt MB Notification", "A Affection Pt Trigger", "A Talk Chk Trigger", "Mom Lingerie Quest", "Mom Lingerie Complete", "Mom Bath Paizuri Complete", "Mom & Aunt Talking 2", "Mom Naughty", "<PERSON> Sex Achieved", "Mom Night Sex Tired", "Mom Base Story Notification", "<PERSON><PERSON><PERSON> Yoga Unlocked", "Aunt TF After Cutscene", "Special Scene Toggle", "Present Mom <PERSON>", "Present Sis Room", "Present Kitchen", "Holiday Quest Complete", "First Present", "Holiday Quest Started", "Dining Game Unlocked", "RPG Puzzle Activated", "RPG Oracle Appear", "<PERSON><PERSON>", "S Dialogue Advance", "Daily Game Played", "Sis Pts Chk Trigger", "RPG Puzzle Complete", "Dark Queen FJ Unlocked", "Sis Heart Tutorial", "Sis Creative Met", "Sis Affection Met", "Mom Shower Fuck Complete", "Movie Available", "Aunt Sex Achieved", "Aunt Sex Cutscene Active", "Aunt Base Story Notification", "Aunt Sex 2 Cutscene Chk", "Aunt Private Show Waiting", "Aunt Date Active", "Aunt Date Sex Complete", "Date Work Question", "Date Travel Question", "Date TV Question", "Aunt Date Sex Tired", "Aunt Movie Reminder", "M & A Threesome Notification", "Valencia Event Unlocked", "Post Valencia Cutscene", "Sis Sex Bonus On", "", "", "Maid Outfit Unlocked", "MAID MODE!", "Mom Affection Met", "Mom Clean Pt Met", "<PERSON> Triggered", "Aunt Affection Met", "Aunt Exercise Pt Met", "Aunt <PERSON><PERSON> Triggered", "Sis Affection Met", "Sis Creative Pt Met", "<PERSON><PERSON>ed", "<PERSON>s Sex Achieved", "Sis VG Cutscene Trigger", "Sis Regular View", "RPG Boss Visible", "RPG Defense Option", "RPG Mom Appears", "RPG Boss Complete", "Tomb Quest Available", "Space Game Available", "Space Game Cutscene", "Space BJ Unlocked", "<PERSON>-Sex Cutscene", "Sis Base Story Notification", "<PERSON><PERSON><PERSON>", "<PERSON> Unlocked", "Mom Evening Event Active", "Mom Changing Costume Tomorrow", "Mom Costume Cutscene Trigger", "<PERSON><PERSON><PERSON>", "<PERSON> Laundry Cutscene Unlock", "Aunt Alternate Dialogue Toggle", "Aunt Heart Explanation", "Spring Egg Laundry", "Spring Egg Kitchen", "Spring Egg Bathroom", "Spring Quest Complete", "Sis/Mom Lecture Ctscn", "<PERSON>", "Sis Ambush Cutscene", "<PERSON><PERSON> Sex Unlocked", "Couch Post Sex Cutscene", "<PERSON>locked", "Mom Kitchen Sex", "Mom Fap Activity BJ", "Mom Fap Activity Sex", "Mom Bed Sex", "Mom Bath Sex Unlocked", "Post Morning Sex Cutscene", "Mom Bath Second Cutscene", "<PERSON> Cutscene", "Mom Bath First Cutscene", "Mom Wakeup Sex", "<PERSON>", "Mom Bed & Chair Unlocked", "Sunscreen Available", "Sunscreen Found", "Aunt <PERSON><PERSON>ni <PERSON>n", "Mom & <PERSON> Night Pool", "M & A Pool Ctscn", "M & A Pool Peeped", "Aunt Massage Scene Complete", "Aunt Fap Activity Sex", "<PERSON> Caught Linda Event", "<PERSON> Caught Event Trigger", "M & A Threesome Event", "M & A Post Threesome", "M & A Threesome Achieved", "M & A Talking @ Night", "M & A Threesome Location", "Voyeur Kiss Watched", "Threesome Tired", "Nightmare Event", "<PERSON> Cutscene", "Dragon King Injured", "Dragon Boss MP Out", "Dragon Boss Defeated", "<PERSON>", "<PERSON> Unlocked", "<PERSON> Regular Wakeup", "<PERSON>up", "<PERSON>", "0.2.2 Check", "0.3 Check", "0.5 Check", "0.5.1 Check", "0.8.1 Check", "0.8.2 Check", "0.12.2 Check", "1.0.0 Check", "", "", "", "", "", "", "Check LR Argument", "Credits Viewing", "Game Complete Notification", "<Global Meta> Game Complete", "<Global Meta> Auto Title Disabled", "Alternate <PERSON><PERSON> Toggle", "Linda LR Fingering", "Mom/Sis Morning Event", "Cabinet Sex Unlocked", "Mom/Sis Argument", "Mom/Sis Post Pai Cutscene", "<PERSON>/<PERSON><PERSON> <PERSON>i Unlocked", "Sis 3Some Trigger", "<PERSON><PERSON>", "M & A & S Foursome Location", "<PERSON>/<PERSON><PERSON>", "<PERSON>/<PERSON>s Sex Trigger", "Valencia Visited", "Post Aunt <PERSON> Sex", "4-Some Event", "Mom/Sis Post Sex Cutscene", "Mom/<PERSON>s Sex Unlocked", "4-Some Notification", "Aunt/Sis Event Active", "LR 4-Some Argument", "4-Some Complete"], "terms": {"basic": ["Level", "Lv", "HP", "HP", "MP", "MP", "TP", "TP", "EXP", "EXP"], "commands": ["Fight", "Escape", "Attack", "Guard", "<PERSON><PERSON>", "Skill", "Equip", "Status", "Formation", "Save", "Game End", "Options", "Weapon", "Armor", "Key Item", "Equip", "Optimize", "Clear", "New Game", "Continue", null, "To Title", "Cancel", null, "Buy", "<PERSON>ll"], "params": ["Max HP", "Max MP", "Attack", "Defense", "<PERSON><PERSON>", "M.Defense", "Agility", "Luck", "Hit", "Evasion"], "messages": {"actionFailure": "There was no effect on %1!", "actorDamage": "%1 took %2 damage!", "actorDrain": "%1 was drained of %2 %3!", "actorGain": "%1 gained %2 %3!", "actorLoss": "%1 lost %2 %3!", "actorNoDamage": "%1 took no damage!", "actorNoHit": "Miss! %1 took no damage!", "actorRecovery": "%1 recovered %2 %3!", "alwaysDash": "Always Dash", "bgmVolume": "BGM Volume", "bgsVolume": "BGS Volume", "buffAdd": "%1's %2 went up!", "buffRemove": "%1's %2 returned to normal!", "commandRemember": "Command Remember", "counterAttack": "%1 counterattacked!", "criticalToActor": "A painful blow!!", "criticalToEnemy": "An excellent hit!!", "debuffAdd": "%1's %2 went down!", "defeat": "%1 was defeated.", "emerge": "%1 emerged!", "enemyDamage": "%1 took %2 damage!", "enemyDrain": "%1 was drained of %2 %3!", "enemyGain": "%1 gained %2 %3!", "enemyLoss": "%1 lost %2 %3!", "enemyNoDamage": "%1 took no damage!", "enemyNoHit": "Miss! %1 took no damage!", "enemyRecovery": "%1 recovered %2 %3!", "escapeFailure": "However, it was unable to escape!", "escapeStart": "%1 has started to escape!", "evasion": "%1 evaded the attack!", "expNext": "To Next %1", "expTotal": "Current %1", "file": "File", "levelUp": "%1 is now %2 %3!", "loadMessage": "Load which file?", "magicEvasion": "%1 nullified the magic!", "magicReflection": "%1 reflected the magic!", "meVolume": "ME Volume", "obtainExp": "%1 %2 received!", "obtainGold": "%1\\G found!", "obtainItem": "%1 found!", "obtainSkill": "%1 learned!", "partyName": "%1's Party", "possession": "Possession", "preemptive": "%1 got the upper hand!", "saveMessage": "Save to which file?", "seVolume": "SE Volume", "substitute": "%1 protected %2!", "surprise": "%1 was surprised!", "useItem": "%1 uses %2!", "victory": "%1 was victorious!"}}, "testBattlers": [{"actorId": 1, "equips": [1, 1, 2, 3, 0], "level": 1}, {"actorId": 2, "equips": [2, 1, 2, 3, 0], "level": 1}, {"actorId": 3, "equips": [3, 0, 2, 3, 4], "level": 1}, {"actorId": 4, "equips": [4, 0, 2, 3, 4], "level": 1}], "testTroopId": 4, "title1Name": "Title", "title2Name": "Logo", "titleBgm": {"name": "Happy - syncop<PERSON>", "pan": 0, "pitch": 100, "volume": 70}, "variables": ["", "Day Count", "", "Var2 is Time of Day", "", "", "Naughty Scenes:", "Scene Player", "Scene Sound Effects", "<PERSON>an Sound Effects", "Room Count", "", "<PERSON> Costume", "<PERSON><PERSON>", "M Dialogue (Casual)", "M <PERSON>", "S Dialogue", "A Dialogue", "M Affection Points", "M Special Events", "M Points Icon Show", "Picked Up Clothes Ct", "M Clean Pt", "A Exercise Pt", "A Affection Pt", "A Points Icon Show", "A Special Events", "Mom Picked Up Clothes Ct", "Paizuri Variation", "Mom Corruption Lvl", "M Holiday Dialogue", "Presents Opened", "S Special Events", "S Creative Pt", "S Affection Pt", "S Points Icon Show", "Aunt Lust Lvl", "Aunt Date Points", "Spring Eggs Found", "", "RNG Variable", "RPG Orb #1", "RPG Orb #2", "RPG Orb #3", "RPG Orb #4", "Julie TV Choice", "Sis Boss Fight Ultimate", "Aunt Dialogue (Bikini)", "Nightmare Cutscenes", "Dragon Boss Fight Ultimate", "<PERSON>/<PERSON><PERSON>", "", "", "", "", "", "", "", "<Global Meta>Message Style.", "Current Map", "<Global Meta>Message Transp."], "versionId": 18475905, "victoryMe": {"name": "Victory1", "pan": 0, "pitch": 100, "volume": 90}, "weaponTypes": ["", "<PERSON>gger", "Sword", "<PERSON><PERSON>l", "Axe", "Whip", "<PERSON><PERSON>", "Bow", "Crossbow", "Gun", "Claw", "Glove", "Spear"], "windowTone": [0, 0, 0, 0], "hasEncryptedImages": true, "hasEncryptedAudio": true, "encryptionKey": "d41d8cd98f00b204e9800998ecf8427e"}