// Generated by RPG Maker.
// Do not edit this file directly.
var $plugins =
[
{"name":"MadeWithMv","status":false,"description":"Show a Splash Screen \"Made with MV\" and/or a Custom Splash Screen before going to main screen.","parameters":{"Show Made With MV":"false","Made with MV Image":"MadeWithMv","Show Custom Splash":"true","Custom Image":"Disclaimer","Fade Out Time":"60","Fade In Time":"60","Wait Time":"360"}},
{"name":"GALV_AnimatedSplashScreens","status":true,"description":"(v.1.1) Set up animated splash screens that show before the title screen.","parameters":{"----- SPLASH SCREENS -----":"","Splash Images":"Disclaimer,270,8,0","Splash Background":"#333","Splash Skip":"ONE"}},
{"name":"YEP_CoreEngine","status":true,"description":"v1.31 Needed for the majority of Yanfly Engine Scripts. Also\ncontains bug fixes found inherently in RPG Maker.","parameters":{"---Screen---":"","Screen Width":"1280","Screen Height":"720","Scale Battlebacks":"true","Scale Title":"true","Scale Game Over":"true","Open Console":"false","Reposition Battlers":"true","GameFont Load Timer":"0","Update Real Scale":"false","Collection Clear":"true","---Gold---":"","Gold Max":"99999999","Gold Font Size":"20","Gold Icon":"28","Gold Overlap":"A lotta","---Items---":"","Default Max":"99","Quantity Text Size":"20","---Parameters---":"","Max Level":"99","Actor MaxHP":"9999","Actor MaxMP":"9999","Actor Parameter":"999","Enemy MaxHP":"999999","Enemy MaxMP":"9999","Enemy Parameter":"999","---Battle---":"","Animation Rate":"4","Flash Target":"false","Show Events Transition":"true","Show Events Snapshot":"true","---Map Optimization---":"","Refresh Update HP":"true","Refresh Update MP":"true","Refresh Update TP":"false","---Font---":"","Chinese Font":"SimHei, Heiti TC, sans-serif","Korean Font":"Dotum, AppleGothic, sans-serif","Default Font":"GameFont, Verdana, Arial, Courier New","Font Size":"28","Text Align":"left","---Windows---":"","Digit Grouping":"true","Line Height":"41","Icon Width":"32","Icon Height":"32","Face Width":"144","Face Height":"144","Window Padding":"18","Text Padding":"6","Window Opacity":"192","Gauge Outline":"true","Gauge Height":"18","Menu TP Bar":"true","---Window Colors---":"","Color: Normal":"0","Color: System":"16","Color: Crisis":"17","Color: Death":"18","Color: Gauge Back":"19","Color: HP Gauge 1":"20","Color: HP Gauge 2":"21","Color: MP Gauge 1":"22","Color: MP Gauge 2":"23","Color: MP Cost":"23","Color: Power Up":"24","Color: Power Down":"25","Color: TP Gauge 1":"28","Color: TP Gauge 2":"29","Color: TP Cost Color":"29"}},
{"name":"YEP_MessageCore","status":true,"description":"v1.18 Adds more features to the Message Window to customized\nthe way your messages appear and functions.","parameters":{"---General---":"","Default Rows":"3","Default Width":"840","Face Indent":"Window_Base._faceWidth + 24","Fast Forward Key":"pagedown","Enable Fast Forward":"true","Word Wrapping":"true","Description Wrap":"false","Word Wrap Space":"false","Tight Wrap":"false","---Font---":"","Font Name":"GameFont","Font Name CH":"SimHei, Heiti TC, sans-serif","Font Name KR":"Dotum, AppleGothic, sans-serif","Font Size":"32","Font Size Change":"12","Font Changed Max":"96","Font Changed Min":"12","Font Outline":"3","Maintain Font":"false","---Name Box---":"","Name Box Buffer X":"-5","Name Box Buffer Y":"20","Name Box Padding":"this.standardPadding() * 4","Name Box Color":"0","Name Box Clear":"true","Name Box Added Text":"\\fs[48]","Name Box Auto Close":"false"}},
{"name":"YEP_SaveCore","status":true,"description":"v1.06 Alter the save menu for a more aesthetic layout\nand take control over the file system's rules.","parameters":{"---General---":"","Max Files":"40","Saved Icon":"231","Empty Icon":"230","Return After Saving":"false","Auto New Index":"true","---Action Window---":"","Load Command":"Load","Save Command":"Save","Delete Command":"Delete","---Help Window---":"","Select Help":"Please select a file slot.","Load Help":"Loads the data from the saved game.","Save Help":"Saves the current progress in your game.","Delete Help":"Deletes all data from this save file.","---Delete---":"","Delete Filename":"Damage2","Delete Volume":"100","Delete Pitch":"150","Delete Pan":"0","---Info Window---":"","Show Game Title":"true","Invalid Game Text":"This save is for a different game.","Empty Game Text":"Empty","Map Display Name":"true","Party Display":"2","Party Y Position":"this.lineHeight() + Window_Base._faceHeight","Show Actor Names":"true","Name Font Size":"20","Show Actor Level":"false","Level Font Size":"20","Level Format":"\\c[16]%1 \\c[0]%3","Data Font Size":"20","Data Column 1":"playtime","Data Column 2":"save count","Data Column 3":"location","Data Column 4":"center text: variable 2","---Vocabulary---":"","Map Location":"","Playtime":"Playtime:","Save Count":"Total Saves:","Gold Count":"%1:","---Technical---":"","Save Mode":"auto","Local Config":"config.rpgsave","Local Global":"global.rpgsave","Local Save":"file%1.rpgsave","Web Config":"RPG %1 Config","Web Global":"RPG %1 Global","Web Save":"RPG %1 File%2","---Confirmation---":"","Load Confirmation":"true","Load Text":"Do you wish to load this save file?","Save Confirmation":"true","Save Text":"Do you wish to overwrite this save file?","Delete Confirmation":"true","Delete Text":"Do you wish to delete this save file?","Confirm Yes":"Yes","Confirm No":"No"}},
{"name":"YEP_MainMenuManager","status":true,"description":"v1.03 This plugin allows you to manage the various aspects\nof your main menu.","parameters":{"---General---":"","Hide Actor Window":"false","Hide Gold Window":"true","Blurry Background":"true","---Command---":"","Command Alignment":"center","Command Position":"","Command Columns":"1","Command Rows":"Math.min(10, Math.ceil(this.maxItems() / this.maxCols()))","Command Width":"240","---Menu Items---":"","---Menu 1---":"","Menu 1 Name":"'Profile'","Menu 1 Symbol":"common event","Menu 1 Show":"true","Menu 1 Enabled":"true","Menu 1 Ext":"44","Menu 1 Main Bind":"this.callCommonEvent.bind(this)","Menu 1 Actor Bind":"","---Menu 2---":"","Menu 2 Name":"'Gallery'","Menu 2 Symbol":"common event","Menu 2 Show":"true","Menu 2 Enabled":"true","Menu 2 Ext":"49","Menu 2 Main Bind":"this.callCommonEvent.bind(this)","Menu 2 Actor Bind":"","---Menu 3---":"","Menu 3 Name":"'Custom'","Menu 3 Symbol":"common event","Menu 3 Show":"true","Menu 3 Enabled":"true","Menu 3 Ext":"43","Menu 3 Main Bind":"this.callCommonEvent.bind(this)","Menu 3 Actor Bind":"","---Menu 4---":"","Menu 4 Name":"","Menu 4 Symbol":"","Menu 4 Show":"","Menu 4 Enabled":"","Menu 4 Ext":"","Menu 4 Main Bind":"","Menu 4 Actor Bind":"","---Menu 5---":"","Menu 5 Name":"","Menu 5 Symbol":"","Menu 5 Show":"","Menu 5 Enabled":"","Menu 5 Ext":"","Menu 5 Main Bind":"","Menu 5 Actor Bind":"","---Menu 6---":"","Menu 6 Name":"","Menu 6 Symbol":"","Menu 6 Show":"","Menu 6 Enabled":"","Menu 6 Ext":"","Menu 6 Main Bind":"","Menu 6 Actor Bind":"","---Menu 7---":"","Menu 7 Name":"","Menu 7 Symbol":"","Menu 7 Show":"","Menu 7 Enabled":"","Menu 7 Ext":"","Menu 7 Main Bind":"","Menu 7 Actor Bind":"","---Menu 8---":"","Menu 8 Name":"","Menu 8 Symbol":"","Menu 8 Show":"","Menu 8 Enabled":"","Menu 8 Ext":"","Menu 8 Main Bind":"","Menu 8 Actor Bind":"","---Menu 9---":"","Menu 9 Name":"","Menu 9 Symbol":"","Menu 9 Show":"","Menu 9 Enabled":"","Menu 9 Ext":"","Menu 9 Main Bind":"","Menu 9 Actor Bind":"","---Menu 10---":"","Menu 10 Name":"","Menu 10 Symbol":"","Menu 10 Show":"","Menu 10 Enabled":"","Menu 10 Ext":"","Menu 10 Main Bind":"","Menu 10 Actor Bind":"","---Menu 11---":"","Menu 11 Name":"","Menu 11 Symbol":"","Menu 11 Show":"","Menu 11 Enabled":"","Menu 11 Ext":"","Menu 11 Main Bind":"","Menu 11 Actor Bind":"","---Menu 12---":"","Menu 12 Name":"","Menu 12 Symbol":"","Menu 12 Show":"","Menu 12 Enabled":"","Menu 12 Ext":"","Menu 12 Main Bind":"","Menu 12 Actor Bind":"","---Menu 13---":"","Menu 13 Name":"","Menu 13 Symbol":"","Menu 13 Show":"","Menu 13 Enabled":"","Menu 13 Ext":"","Menu 13 Main Bind":"","Menu 13 Actor Bind":"","---Menu 14---":"","Menu 14 Name":"","Menu 14 Symbol":"","Menu 14 Show":"","Menu 14 Enabled":"","Menu 14 Ext":"","Menu 14 Main Bind":"","Menu 14 Actor Bind":"","---Menu 15---":"","Menu 15 Name":"","Menu 15 Symbol":"","Menu 15 Show":"","Menu 15 Enabled":"","Menu 15 Ext":"","Menu 15 Main Bind":"","Menu 15 Actor Bind":"","---Menu 16---":"","Menu 16 Name":"","Menu 16 Symbol":"","Menu 16 Show":"","Menu 16 Enabled":"","Menu 16 Ext":"","Menu 16 Main Bind":"","Menu 16 Actor Bind":"","---Menu 17---":"","Menu 17 Name":"","Menu 17 Symbol":"","Menu 17 Show":"","Menu 17 Enabled":"","Menu 17 Ext":"","Menu 17 Main Bind":"","Menu 17 Actor Bind":"","---Menu 18---":"","Menu 18 Name":"","Menu 18 Symbol":"","Menu 18 Show":"","Menu 18 Enabled":"","Menu 18 Ext":"","Menu 18 Main Bind":"","Menu 18 Actor Bind":"","---Menu 19---":"","Menu 19 Name":"","Menu 19 Symbol":"","Menu 19 Show":"","Menu 19 Enabled":"","Menu 19 Ext":"","Menu 19 Main Bind":"","Menu 19 Actor Bind":"","---Menu 20---":"","Menu 20 Name":"","Menu 20 Symbol":"","Menu 20 Show":"","Menu 20 Enabled":"","Menu 20 Ext":"","Menu 20 Main Bind":"","Menu 20 Actor Bind":"","---Menu 21---":"","Menu 21 Name":"","Menu 21 Symbol":"","Menu 21 Show":"","Menu 21 Enabled":"","Menu 21 Ext":"","Menu 21 Main Bind":"","Menu 21 Actor Bind":"","---Menu 22---":"","Menu 22 Name":"","Menu 22 Symbol":"","Menu 22 Show":"","Menu 22 Enabled":"","Menu 22 Ext":"","Menu 22 Main Bind":"","Menu 22 Actor Bind":"","---Menu 23---":"","Menu 23 Name":"","Menu 23 Symbol":"","Menu 23 Show":"","Menu 23 Enabled":"","Menu 23 Ext":"","Menu 23 Main Bind":"","Menu 23 Actor Bind":"","---Menu 24---":"","Menu 24 Name":"","Menu 24 Symbol":"","Menu 24 Show":"","Menu 24 Enabled":"","Menu 24 Ext":"","Menu 24 Main Bind":"","Menu 24 Actor Bind":"","---Menu 25---":"","Menu 25 Name":"","Menu 25 Symbol":"","Menu 25 Show":"","Menu 25 Enabled":"","Menu 25 Ext":"","Menu 25 Main Bind":"","Menu 25 Actor Bind":"","---Menu 26---":"","Menu 26 Name":"","Menu 26 Symbol":"","Menu 26 Show":"","Menu 26 Enabled":"","Menu 26 Ext":"","Menu 26 Main Bind":"","Menu 26 Actor Bind":"","---Menu 27---":"","Menu 27 Name":"","Menu 27 Symbol":"","Menu 27 Show":"","Menu 27 Enabled":"","Menu 27 Ext":"","Menu 27 Main Bind":"","Menu 27 Actor Bind":"","---Menu 28---":"","Menu 28 Name":"","Menu 28 Symbol":"","Menu 28 Show":"","Menu 28 Enabled":"","Menu 28 Ext":"","Menu 28 Main Bind":"","Menu 28 Actor Bind":"","---Menu 29---":"","Menu 29 Name":"","Menu 29 Symbol":"","Menu 29 Show":"","Menu 29 Enabled":"","Menu 29 Ext":"","Menu 29 Main Bind":"","Menu 29 Actor Bind":"","---Menu 30---":"","Menu 30 Name":"","Menu 30 Symbol":"","Menu 30 Show":"","Menu 30 Enabled":"","Menu 30 Ext":"","Menu 30 Main Bind":"","Menu 30 Actor Bind":"","---Menu 31---":"","Menu 31 Name":"","Menu 31 Symbol":"","Menu 31 Show":"","Menu 31 Enabled":"","Menu 31 Ext":"","Menu 31 Main Bind":"","Menu 31 Actor Bind":"","---Menu 32---":"","Menu 32 Name":"","Menu 32 Symbol":"","Menu 32 Show":"","Menu 32 Enabled":"","Menu 32 Ext":"","Menu 32 Main Bind":"","Menu 32 Actor Bind":"","---Menu 33---":"","Menu 33 Name":"","Menu 33 Symbol":"","Menu 33 Show":"","Menu 33 Enabled":"","Menu 33 Ext":"","Menu 33 Main Bind":"","Menu 33 Actor Bind":"","---Menu 34---":"","Menu 34 Name":"","Menu 34 Symbol":"","Menu 34 Show":"","Menu 34 Enabled":"","Menu 34 Ext":"","Menu 34 Main Bind":"","Menu 34 Actor Bind":"","---Menu 35---":"","Menu 35 Name":"","Menu 35 Symbol":"","Menu 35 Show":"","Menu 35 Enabled":"","Menu 35 Ext":"","Menu 35 Main Bind":"","Menu 35 Actor Bind":"","---Menu 36---":"","Menu 36 Name":"","Menu 36 Symbol":"","Menu 36 Show":"","Menu 36 Enabled":"","Menu 36 Ext":"","Menu 36 Main Bind":"","Menu 36 Actor Bind":"","---Menu 37---":"","Menu 37 Name":"","Menu 37 Symbol":"","Menu 37 Show":"","Menu 37 Enabled":"","Menu 37 Ext":"","Menu 37 Main Bind":"","Menu 37 Actor Bind":"","---Menu 38---":"","Menu 38 Name":"","Menu 38 Symbol":"","Menu 38 Show":"","Menu 38 Enabled":"","Menu 38 Ext":"","Menu 38 Main Bind":"","Menu 38 Actor Bind":"","---Menu 39---":"","Menu 39 Name":"","Menu 39 Symbol":"","Menu 39 Show":"","Menu 39 Enabled":"","Menu 39 Ext":"","Menu 39 Main Bind":"","Menu 39 Actor Bind":"","---Menu 40---":"","Menu 40 Name":"","Menu 40 Symbol":"","Menu 40 Show":"","Menu 40 Enabled":"","Menu 40 Ext":"","Menu 40 Main Bind":"","Menu 40 Actor Bind":"","---Menu 41---":"","Menu 41 Name":"","Menu 41 Symbol":"","Menu 41 Show":"","Menu 41 Enabled":"","Menu 41 Ext":"","Menu 41 Main Bind":"","Menu 41 Actor Bind":"","---Menu 42---":"","Menu 42 Name":"","Menu 42 Symbol":"","Menu 42 Show":"","Menu 42 Enabled":"","Menu 42 Ext":"","Menu 42 Main Bind":"","Menu 42 Actor Bind":"","---Menu 43---":"","Menu 43 Name":"","Menu 43 Symbol":"","Menu 43 Show":"","Menu 43 Enabled":"","Menu 43 Ext":"","Menu 43 Main Bind":"","Menu 43 Actor Bind":"","---Menu 44---":"","Menu 44 Name":"","Menu 44 Symbol":"","Menu 44 Show":"","Menu 44 Enabled":"","Menu 44 Ext":"","Menu 44 Main Bind":"","Menu 44 Actor Bind":"","---Menu 45---":"","Menu 45 Name":"","Menu 45 Symbol":"","Menu 45 Show":"","Menu 45 Enabled":"","Menu 45 Ext":"","Menu 45 Main Bind":"","Menu 45 Actor Bind":"","---Menu 46---":"","Menu 46 Name":"","Menu 46 Symbol":"","Menu 46 Show":"","Menu 46 Enabled":"","Menu 46 Ext":"","Menu 46 Main Bind":"","Menu 46 Actor Bind":"","---Menu 47---":"","Menu 47 Name":"","Menu 47 Symbol":"","Menu 47 Show":"","Menu 47 Enabled":"","Menu 47 Ext":"","Menu 47 Main Bind":"","Menu 47 Actor Bind":"","---Menu 48---":"","Menu 48 Name":"","Menu 48 Symbol":"","Menu 48 Show":"","Menu 48 Enabled":"","Menu 48 Ext":"","Menu 48 Main Bind":"","Menu 48 Actor Bind":"","---Menu 49---":"","Menu 49 Name":"","Menu 49 Symbol":"","Menu 49 Show":"","Menu 49 Enabled":"","Menu 49 Ext":"","Menu 49 Main Bind":"","Menu 49 Actor Bind":"","---Menu 50---":"","Menu 50 Name":"","Menu 50 Symbol":"","Menu 50 Show":"","Menu 50 Enabled":"","Menu 50 Ext":"","Menu 50 Main Bind":"","Menu 50 Actor Bind":"","---Menu 51---":"","Menu 51 Name":"","Menu 51 Symbol":"","Menu 51 Show":"","Menu 51 Enabled":"","Menu 51 Ext":"","Menu 51 Main Bind":"","Menu 51 Actor Bind":"","---Menu 52---":"","Menu 52 Name":"","Menu 52 Symbol":"","Menu 52 Show":"","Menu 52 Enabled":"","Menu 52 Ext":"","Menu 52 Main Bind":"","Menu 52 Actor Bind":"","---Menu 53---":"","Menu 53 Name":"","Menu 53 Symbol":"","Menu 53 Show":"","Menu 53 Enabled":"","Menu 53 Ext":"","Menu 53 Main Bind":"","Menu 53 Actor Bind":"","---Menu 54---":"","Menu 54 Name":"","Menu 54 Symbol":"","Menu 54 Show":"","Menu 54 Enabled":"","Menu 54 Ext":"","Menu 54 Main Bind":"","Menu 54 Actor Bind":"","---Menu 55---":"","Menu 55 Name":"","Menu 55 Symbol":"","Menu 55 Show":"","Menu 55 Enabled":"","Menu 55 Ext":"","Menu 55 Main Bind":"","Menu 55 Actor Bind":"","---Menu 56---":"","Menu 56 Name":"","Menu 56 Symbol":"","Menu 56 Show":"","Menu 56 Enabled":"","Menu 56 Ext":"","Menu 56 Main Bind":"","Menu 56 Actor Bind":"","---Menu 57---":"","Menu 57 Name":"","Menu 57 Symbol":"","Menu 57 Show":"","Menu 57 Enabled":"","Menu 57 Ext":"","Menu 57 Main Bind":"","Menu 57 Actor Bind":"","---Menu 58---":"","Menu 58 Name":"","Menu 58 Symbol":"","Menu 58 Show":"","Menu 58 Enabled":"","Menu 58 Ext":"","Menu 58 Main Bind":"","Menu 58 Actor Bind":"","---Menu 59---":"","Menu 59 Name":"","Menu 59 Symbol":"","Menu 59 Show":"","Menu 59 Enabled":"","Menu 59 Ext":"","Menu 59 Main Bind":"","Menu 59 Actor Bind":"","---Menu 60---":"","Menu 60 Name":"","Menu 60 Symbol":"","Menu 60 Show":"","Menu 60 Enabled":"","Menu 60 Ext":"","Menu 60 Main Bind":"","Menu 60 Actor Bind":"","---Menu 61---":"","Menu 61 Name":"","Menu 61 Symbol":"","Menu 61 Show":"","Menu 61 Enabled":"","Menu 61 Ext":"","Menu 61 Main Bind":"","Menu 61 Actor Bind":"","---Menu 62---":"","Menu 62 Name":"","Menu 62 Symbol":"","Menu 62 Show":"","Menu 62 Enabled":"","Menu 62 Ext":"","Menu 62 Main Bind":"","Menu 62 Actor Bind":"","---Menu 63---":"","Menu 63 Name":"","Menu 63 Symbol":"","Menu 63 Show":"","Menu 63 Enabled":"","Menu 63 Ext":"","Menu 63 Main Bind":"","Menu 63 Actor Bind":"","---Menu 64---":"","Menu 64 Name":"","Menu 64 Symbol":"","Menu 64 Show":"","Menu 64 Enabled":"","Menu 64 Ext":"","Menu 64 Main Bind":"","Menu 64 Actor Bind":"","---Menu 65---":"","Menu 65 Name":"","Menu 65 Symbol":"","Menu 65 Show":"","Menu 65 Enabled":"","Menu 65 Ext":"","Menu 65 Main Bind":"","Menu 65 Actor Bind":"","---Menu 66---":"","Menu 66 Name":"","Menu 66 Symbol":"","Menu 66 Show":"","Menu 66 Enabled":"","Menu 66 Ext":"","Menu 66 Main Bind":"","Menu 66 Actor Bind":"","---Menu 67---":"","Menu 67 Name":"","Menu 67 Symbol":"","Menu 67 Show":"","Menu 67 Enabled":"","Menu 67 Ext":"","Menu 67 Main Bind":"","Menu 67 Actor Bind":"","---Menu 68---":"","Menu 68 Name":"","Menu 68 Symbol":"","Menu 68 Show":"","Menu 68 Enabled":"","Menu 68 Ext":"","Menu 68 Main Bind":"","Menu 68 Actor Bind":"","---Menu 69---":"","Menu 69 Name":"","Menu 69 Symbol":"","Menu 69 Show":"","Menu 69 Enabled":"","Menu 69 Ext":"","Menu 69 Main Bind":"","Menu 69 Actor Bind":"","---Menu 70---":"","Menu 70 Name":"","Menu 70 Symbol":"","Menu 70 Show":"","Menu 70 Enabled":"","Menu 70 Ext":"","Menu 70 Main Bind":"","Menu 70 Actor Bind":"","---Menu 71---":"","Menu 71 Name":"","Menu 71 Symbol":"","Menu 71 Show":"","Menu 71 Enabled":"","Menu 71 Ext":"","Menu 71 Main Bind":"","Menu 71 Actor Bind":"","---Menu 72---":"","Menu 72 Name":"","Menu 72 Symbol":"","Menu 72 Show":"","Menu 72 Enabled":"","Menu 72 Ext":"","Menu 72 Main Bind":"","Menu 72 Actor Bind":"","---Menu 73---":"","Menu 73 Name":"","Menu 73 Symbol":"","Menu 73 Show":"","Menu 73 Enabled":"","Menu 73 Ext":"","Menu 73 Main Bind":"","Menu 73 Actor Bind":"","---Menu 74---":"","Menu 74 Name":"","Menu 74 Symbol":"","Menu 74 Show":"","Menu 74 Enabled":"","Menu 74 Ext":"","Menu 74 Main Bind":"","Menu 74 Actor Bind":"","---Menu 75---":"","Menu 75 Name":"","Menu 75 Symbol":"","Menu 75 Show":"","Menu 75 Enabled":"","Menu 75 Ext":"","Menu 75 Main Bind":"","Menu 75 Actor Bind":"","---Menu 76---":"","Menu 76 Name":"","Menu 76 Symbol":"","Menu 76 Show":"","Menu 76 Enabled":"","Menu 76 Ext":"","Menu 76 Main Bind":"","Menu 76 Actor Bind":"","---Menu 77---":"","Menu 77 Name":"","Menu 77 Symbol":"","Menu 77 Show":"","Menu 77 Enabled":"","Menu 77 Ext":"","Menu 77 Main Bind":"","Menu 77 Actor Bind":"","---Menu 78---":"","Menu 78 Name":"","Menu 78 Symbol":"","Menu 78 Show":"","Menu 78 Enabled":"","Menu 78 Ext":"","Menu 78 Main Bind":"","Menu 78 Actor Bind":"","---Menu 79---":"","Menu 79 Name":"","Menu 79 Symbol":"","Menu 79 Show":"","Menu 79 Enabled":"","Menu 79 Ext":"","Menu 79 Main Bind":"","Menu 79 Actor Bind":"","---Menu 80---":"","Menu 80 Name":"","Menu 80 Symbol":"","Menu 80 Show":"","Menu 80 Enabled":"","Menu 80 Ext":"","Menu 80 Main Bind":"","Menu 80 Actor Bind":"","---Menu 81---":"","Menu 81 Name":"'Common Event 1'","Menu 81 Symbol":"common event","Menu 81 Show":"false","Menu 81 Enabled":"true","Menu 81 Ext":"1","Menu 81 Main Bind":"this.callCommonEvent.bind(this)","Menu 81 Actor Bind":"","---Menu 82---":"","Menu 82 Name":"'Common Event 2'","Menu 82 Symbol":"common event","Menu 82 Show":"false","Menu 82 Enabled":"true","Menu 82 Ext":"2","Menu 82 Main Bind":"this.callCommonEvent.bind(this)","Menu 82 Actor Bind":"","---Menu 83---":"","Menu 83 Name":"'Common Event 3'","Menu 83 Symbol":"common event","Menu 83 Show":"false","Menu 83 Enabled":"true","Menu 83 Ext":"3","Menu 83 Main Bind":"this.callCommonEvent.bind(this)","Menu 83 Actor Bind":"","---Menu 84---":"","Menu 84 Name":"","Menu 84 Symbol":"","Menu 84 Show":"","Menu 84 Enabled":"","Menu 84 Ext":"","Menu 84 Main Bind":"","Menu 84 Actor Bind":"","---Menu 85---":"","Menu 85 Name":"","Menu 85 Symbol":"","Menu 85 Show":"","Menu 85 Enabled":"","Menu 85 Ext":"","Menu 85 Main Bind":"","Menu 85 Actor Bind":"","---Menu 86---":"","Menu 86 Name":"","Menu 86 Symbol":"","Menu 86 Show":"","Menu 86 Enabled":"","Menu 86 Ext":"","Menu 86 Main Bind":"","Menu 86 Actor Bind":"","---Menu 87---":"","Menu 87 Name":"","Menu 87 Symbol":"","Menu 87 Show":"","Menu 87 Enabled":"","Menu 87 Ext":"","Menu 87 Main Bind":"","Menu 87 Actor Bind":"","---Menu 88---":"","Menu 88 Name":"","Menu 88 Symbol":"","Menu 88 Show":"","Menu 88 Enabled":"","Menu 88 Ext":"","Menu 88 Main Bind":"","Menu 88 Actor Bind":"","---Menu 89---":"","Menu 89 Name":"","Menu 89 Symbol":"","Menu 89 Show":"","Menu 89 Enabled":"","Menu 89 Ext":"","Menu 89 Main Bind":"","Menu 89 Actor Bind":"","---Menu 90---":"","Menu 90 Name":"TextManager.options","Menu 90 Symbol":"options","Menu 90 Show":"this.needsCommand('options')","Menu 90 Enabled":"this.isOptionsEnabled()","Menu 90 Ext":"","Menu 90 Main Bind":"this.commandOptions.bind(this)","Menu 90 Actor Bind":"","---Menu 91---":"","Menu 91 Name":"","Menu 91 Symbol":"","Menu 91 Show":"","Menu 91 Enabled":"","Menu 91 Ext":"","Menu 91 Main Bind":"","Menu 91 Actor Bind":"","---Menu 92---":"","Menu 92 Name":"","Menu 92 Symbol":"","Menu 92 Show":"","Menu 92 Enabled":"","Menu 92 Ext":"","Menu 92 Main Bind":"","Menu 92 Actor Bind":"","---Menu 93---":"","Menu 93 Name":"","Menu 93 Symbol":"","Menu 93 Show":"","Menu 93 Enabled":"","Menu 93 Ext":"","Menu 93 Main Bind":"","Menu 93 Actor Bind":"","---Menu 94---":"","Menu 94 Name":"","Menu 94 Symbol":"","Menu 94 Show":"","Menu 94 Enabled":"","Menu 94 Ext":"","Menu 94 Main Bind":"","Menu 94 Actor Bind":"","---Menu 95---":"","Menu 95 Name":"TextManager.save","Menu 95 Symbol":"save","Menu 95 Show":"this.needsCommand('save')","Menu 95 Enabled":"this.isSaveEnabled()","Menu 95 Ext":"","Menu 95 Main Bind":"this.commandSave.bind(this)","Menu 95 Actor Bind":"","---Menu 96---":"","Menu 96 Name":"","Menu 96 Symbol":"","Menu 96 Show":"","Menu 96 Enabled":"","Menu 96 Ext":"","Menu 96 Main Bind":"","Menu 96 Actor Bind":"","---Menu 97---":"","Menu 97 Name":"","Menu 97 Symbol":"","Menu 97 Show":"","Menu 97 Enabled":"","Menu 97 Ext":"","Menu 97 Main Bind":"","Menu 97 Actor Bind":"","---Menu 98---":"","Menu 98 Name":"","Menu 98 Symbol":"","Menu 98 Show":"","Menu 98 Enabled":"","Menu 98 Ext":"","Menu 98 Main Bind":"","Menu 98 Actor Bind":"","---Menu 99---":"","Menu 99 Name":"'Debug'","Menu 99 Symbol":"debug","Menu 99 Show":"$gameTemp.isPlaytest()","Menu 99 Enabled":"true","Menu 99 Ext":"","Menu 99 Main Bind":"this.commandDebug.bind(this)","Menu 99 Actor Bind":"","---Menu 100---":"","Menu 100 Name":"TextManager.gameEnd","Menu 100 Symbol":"gameEnd","Menu 100 Show":"true","Menu 100 Enabled":"this.isGameEndEnabled()","Menu 100 Ext":"","Menu 100 Main Bind":"this.commandGameEnd.bind(this)","Menu 100 Actor Bind":""}},
{"name":"YEP_OptionsCore","status":false,"description":"v1.02 Expand the Options Menu into a more elegant looking menu\nwith more customization potential.","parameters":{"---Categories---":"","OptionsCategories":"[\"{\\\"Name\\\":\\\"\\\\\\\\i[87]General\\\",\\\"---Settings---\\\":\\\"\\\",\\\"HelpDesc\\\":\\\"\\\\\\\"General settings that alter the way the game behaves.\\\\\\\"\\\",\\\"OptionsList\\\":\\\"[\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]Fullscreen\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Toggle between fullscreen or windowed mode.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"fullscreen\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value) Graphics._cancelFullScreen();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nelse Graphics._requestFullScreen();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nGraphics._requestFullScreen();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nGraphics._cancelFullScreen();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"if (config[symbol] !== undefined) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = !!config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (ConfigManager[symbol]) Graphics._requestFullScreen();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nelse Graphics._cancelFullScreen();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]Always Dash\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Player walks when OFF. Player dashes when ON.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nHolding SHIFT switches between walking and dashing.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"alwaysDash\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]Command Remember\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Game remembers the last command selected during battle.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"commandRemember\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]ATB Speed\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Determines how fast the ATB Gauge fills up during battle.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"atbSpeed\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"if (Imported.YEP_X_BattleSysATB) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = $gameSystem.getBattleSystem() === 'atb';\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = value / 10;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(13);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(5);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(this.statusText(index), titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 10) value = 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 10) value = 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value < 1) value = 10;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]Enemy Difficulty\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Determines the level strength of enemies.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"difficultySlider\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"if (Imported.YEP_X_DifficultySlider) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = $gameSystem.showDifficultySlider();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = value / Yanfly.Param.DSliderMaxDif;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(28);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(29);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(this.statusText(index), titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += Yanfly.Param.DSliderChange;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > Yanfly.Param.DSliderMaxDif) value = Yanfly.Param.DSliderMinDif;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(Yanfly.Param.DSliderMinDif, Yanfly.Param.DSliderMaxDif);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += Yanfly.Param.DSliderChange;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(Yanfly.Param.DSliderMinDif, Yanfly.Param.DSliderMaxDif);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= Yanfly.Param.DSliderChange;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(Yanfly.Param.DSliderMinDif,\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nYanfly.Param.DSliderMaxDif);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]Message Speed\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Changes the speed text is displayed during messages.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"messageSpeed\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = Imported.YEP_X_MessageSpeedOpt;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = ((value) / 10).clamp(0, 1);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 10) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  var gaugeColor1 = this.textColor(14);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  var gaugeColor2 = this.textColor(6);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  var gaugeColor1 = this.textColor(20);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  var gaugeColor2 = this.textColor(21);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(this.statusText(index), titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 11) value = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 11);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 11);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 11);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]Quest Window\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Show a window displaying the currently active\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nquest on the screen while exploring.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"mapQuestWindow\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = Imported.YEP_X_MapQuestWindow;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"none\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\"]\\\"}\",\"{\\\"Name\\\":\\\"\\\\\\\\i[80]Audio\\\",\\\"---Settings---\\\":\\\"\\\",\\\"HelpDesc\\\":\\\"\\\\\\\"Adjust the audio settings for the game.\\\\\\\"\\\",\\\"OptionsList\\\":\\\"[\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[80]Master Volume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Adjusts the overall volume of the game.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"masterVolume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = value / 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(22);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(23);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(this.statusText(index), titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 100) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  value = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var value = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value !== undefined) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = Number(value).clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[80]BGM Volume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Adjusts the volume of the background music.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"bgmVolume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = value / 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(30);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(31);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(this.statusText(index), titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 100) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  value = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var value = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value !== undefined) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = Number(value).clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[80]BGS Volume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Adjusts the volume of the background sound effects.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"bgsVolume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = value / 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(30);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(31);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(this.statusText(index), titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 100) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  value = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var value = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value !== undefined) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = Number(value).clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[80]ME Volume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Adjusts the volume of the melody effects\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nsuch as fanfares.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"meVolume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = value / 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(30);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(31);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(this.statusText(index), titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 100) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  value = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var value = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value !== undefined) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = Number(value).clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[80]SE Volume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Adjusts the volume of the sound effects.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"seVolume\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = value / 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(30);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(31);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(this.statusText(index), titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value > 100) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  value = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= this.volumeOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var value = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value !== undefined) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = Number(value).clamp(0, 100);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  ConfigManager[symbol] = 100;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"none\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\"]\\\"}\",\"{\\\"Name\\\":\\\"\\\\\\\\i[302]Visual\\\",\\\"---Settings---\\\":\\\"\\\",\\\"HelpDesc\\\":\\\"\\\\\\\"Settings that adjust the visual properties of the game.\\\\\\\"\\\",\\\"OptionsList\\\":\\\"[\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[309]Window Tone: Red\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Changes the window tone's \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]red\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] value.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nHold SHIFT while pressing LEFT/RIGHT to adjust more.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"windowToneRed\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[0];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = (value + 255) / 510;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(10);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(value, titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $dataSystem.windowTone[0];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'red');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[0];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar offset = this.windowToneOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (Input.isPressed('shift')) offset *= 10;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += offset;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'red');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[0];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar offset = this.windowToneOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (Input.isPressed('shift')) offset *= 10;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= offset;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'red');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = !!config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[311]Window Tone: Green\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Changes the window tone's \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[3]green\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] value.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nHold SHIFT while pressing LEFT/RIGHT to adjust more.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"windowToneGreen\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[1];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = (value + 255) / 510;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(3);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(11);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(value, titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $dataSystem.windowTone[1];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'green');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[1];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar offset = this.windowToneOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (Input.isPressed('shift')) offset *= 10;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += offset;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'green');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[1];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar offset = this.windowToneOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (Input.isPressed('shift')) offset *= 10;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= offset;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'green');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = !!config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[312]Window Tone: Blue\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Changes the window tone's \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[1]blue\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] value.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nHold SHIFT while pressing LEFT/RIGHT to adjust more.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"windowToneBlue\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[2];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar rate = (value + 255) / 510;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor1 = this.textColor(1);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar gaugeColor2 = this.textColor(9);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsGauge(index, rate, gaugeColor1, gaugeColor2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(value, titleWidth, rect.y, statusWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $dataSystem.windowTone[2];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'blue');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[2];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar offset = this.windowToneOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (Input.isPressed('shift')) offset *= 10;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += offset;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'blue');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = $gameSystem.windowTone()[2];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar offset = this.windowToneOffset();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (Input.isPressed('shift')) offset *= 10;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= offset;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeWindowTone(symbol, value, 'blue');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = !!config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"none\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"if (Imported.YEP_StaticTilesOption) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else if (Imported.YEP_BattleAniSpeedOpt) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else if (Imported.YEP_X_ActSeqPack3) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else if (Imported.YEP_SynchFpsOption) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[302]Animated Tiles\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Turns animated tiles ON or OFF.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"animateTiles\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = Imported.YEP_StaticTilesOption;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[302]Battle Animation Speed\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Changes the speed of battle animations.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"battleAniSpeed\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = Imported.YEP_BattleAniSpeedOpt;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar quarterWidth = statusWidth / 4;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(value === 4);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar text = this.battleAnimationSpeedText(4);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(text, titleWidth + quarterWidth * 0, rect.y, quarterWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(value === 3);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar text = this.battleAnimationSpeedText(3);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(text, titleWidth + quarterWidth * 1, rect.y, quarterWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(value === 2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar text = this.battleAnimationSpeedText(2);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(text, titleWidth + quarterWidth * 2, rect.y, quarterWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(value === 1);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar text = this.battleAnimationSpeedText(1);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawText(text, titleWidth + quarterWidth * 3, rect.y, quarterWidth, 'center');\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (value <= 0) value = 4;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(1, 4);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue -= 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(1, 4);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue += 1;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvalue = value.clamp(1, 4);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[302]Battle Camera\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"If ON, the camera in battle will move around.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nIf OFF, the camera in battle will be locked in place.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"battleCamera\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = Imported.YEP_X_ActSeqPack3;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[302]Synch Monitor FPS\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Turn this ON if your monitor runs above 60 FPS\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nto synchronize the game to run at 60 FPS.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"synchFps\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = Imported.YEP_SynchFpsOption;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"none\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\"]\\\"}\",\"{\\\"Name\\\":\\\"\\\\\\\\i[83]Controls\\\",\\\"---Settings---\\\":\\\"\\\",\\\"HelpDesc\\\":\\\"\\\\\\\"Change the way you can control the game.\\\\\\\"\\\",\\\"OptionsList\\\":\\\"[\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[83]Gamepad Config\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Configure the game's gamepad settings.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"gamepadConfig\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"if (Imported.GamepadConfig && Input.isControllerConnected()) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = !Utils.isMobileDevice();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.playOkSound();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nSceneManager.push(Scene_GamepadConfig);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[83]Keyboard Config\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Configure the game's keyboard settings.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"keyConfig\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"if (Imported.YEP_KeyboardConfig) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = !Utils.isMobileDevice();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n  show = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.playOkSound();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nSceneManager.push(Scene_KeyConfig);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"none\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\"]\\\"}\",\"{\\\"Name\\\":\\\"\\\\\\\\i[79]Custom\\\",\\\"---Settings---\\\":\\\"\\\",\\\"HelpDesc\\\":\\\"\\\\\\\"Custom options to get a personal experience.\\\\\\\"\\\",\\\"OptionsList\\\":\\\"[\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]Low Quality Mode\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"If NSFW scenes show up as a black screen. Enabling Low Quality mode should help fix this issue.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"lowqualityMode\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar lqmode = $gameSwitches.value(13);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (lqmode) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n $gameSwitches.setValue(13, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n $gameSwitches.setValue(13, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n$gameSwitches.setValue(13, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n$gameSwitches.setValue(13, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[87]Text Box Transparency\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Settings---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"HelpDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Adjusts message box visibility to show as semi-transparent or full.\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Symbol\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"messageBoxTransp\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ShowHide\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"show = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Enable\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"enabled = true;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"Ext\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ext = 0;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"---Functions---\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"MakeCommandCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"this.addCommand(name, symbol, enabled, ext);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DrawItemCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var rect = this.itemRectForText(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar statusWidth = this.statusWidth();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar titleWidth = rect.width - statusWidth;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.resetTextColor();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changePaintOpacity(this.isCommandEnabled(index));\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsName(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.drawOptionsOnOff(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProcessOkCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, !value);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar mb = $gameSwitches.value(18);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nif (mb) {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n $gameSwitches.setValue(18, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n} else {\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n $gameSwitches.setValue(18, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorRightCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n$gameSwitches.setValue(18, true);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"CursorLeftCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"var index = this.index();\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar symbol = this.commandSymbol(index);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nvar value = this.getConfigValue(symbol);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nthis.changeValue(symbol, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\n$gameSwitches.setValue(18, false);\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"DefaultConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"SaveConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"config[symbol] = ConfigManager[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"LoadConfigCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ConfigManager[symbol] = config[symbol];\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}\\\\\\\"]\\\"}\"]","---Options Menu---":"","AllCommand":"\\i[160]All","AllHelpDesc":"\"A list of all of the game's settings.\"","ExitCommand":"\\i[16]Exit","ExitHelpDesc":"\"Exit the Options Menu.\"","---Options Settings---":"","CategoryWidth":"240","StatusWidth":"400","VolumeOffset":"10","WindowToneOffset":"5"}},
{"name":"YEP_PictureCommonEvents","status":false,"description":"v1.06 Causes common events to run when certain pictures\nare clicked while on the map.","parameters":{"---General---":"","Enable Touch Move":"true","Hide Message":"true","---Picture 1---":"","Picture 1 Click":"0","Picture 1 Repeat":"0","Picture 1 Hold":"0","Picture 1 Release":"0","---Picture 2---":"","Picture 2 Click":"0","Picture 2 Repeat":"0","Picture 2 Hold":"0","Picture 2 Release":"0","---Picture 3---":"","Picture 3 Click":"0","Picture 3 Repeat":"0","Picture 3 Hold":"0","Picture 3 Release":"0","---Picture 4---":"","Picture 4 Click":"0","Picture 4 Repeat":"0","Picture 4 Hold":"0","Picture 4 Release":"0","---Picture 5---":"","Picture 5 Click":"0","Picture 5 Repeat":"0","Picture 5 Hold":"0","Picture 5 Release":"0","---Picture 6---":"","Picture 6 Click":"0","Picture 6 Repeat":"0","Picture 6 Hold":"0","Picture 6 Release":"0","---Picture 7---":"","Picture 7 Click":"0","Picture 7 Repeat":"0","Picture 7 Hold":"0","Picture 7 Release":"0","---Picture 8---":"","Picture 8 Click":"0","Picture 8 Repeat":"0","Picture 8 Hold":"0","Picture 8 Release":"0","---Picture 9---":"","Picture 9 Click":"0","Picture 9 Repeat":"0","Picture 9 Hold":"0","Picture 9 Release":"0","---Picture 10---":"","Picture 10 Click":"0","Picture 10 Repeat":"0","Picture 10 Hold":"0","Picture 10 Release":"0","---Picture 11---":"","Picture 11 Click":"11","Picture 11 Repeat":"0","Picture 11 Hold":"0","Picture 11 Release":"0","---Picture 12---":"","Picture 12 Click":"0","Picture 12 Repeat":"0","Picture 12 Hold":"0","Picture 12 Release":"0","---Picture 13---":"","Picture 13 Click":"0","Picture 13 Repeat":"0","Picture 13 Hold":"0","Picture 13 Release":"0","---Picture 14---":"","Picture 14 Click":"0","Picture 14 Repeat":"0","Picture 14 Hold":"0","Picture 14 Release":"0","---Picture 15---":"","Picture 15 Click":"0","Picture 15 Repeat":"0","Picture 15 Hold":"0","Picture 15 Release":"0","---Picture 16---":"","Picture 16 Click":"0","Picture 16 Repeat":"0","Picture 16 Hold":"0","Picture 16 Release":"0","---Picture 17---":"","Picture 17 Click":"0","Picture 17 Repeat":"0","Picture 17 Hold":"0","Picture 17 Release":"0","---Picture 18---":"","Picture 18 Click":"0","Picture 18 Repeat":"0","Picture 18 Hold":"0","Picture 18 Release":"0","---Picture 19---":"","Picture 19 Click":"0","Picture 19 Repeat":"0","Picture 19 Hold":"0","Picture 19 Release":"0","---Picture 20---":"","Picture 20 Click":"0","Picture 20 Repeat":"0","Picture 20 Hold":"0","Picture 20 Release":"0","---Picture 21---":"","Picture 21 Click":"0","Picture 21 Repeat":"0","Picture 21 Hold":"0","Picture 21 Release":"0","---Picture 22---":"","Picture 22 Click":"0","Picture 22 Repeat":"0","Picture 22 Hold":"0","Picture 22 Release":"0","---Picture 23---":"","Picture 23 Click":"0","Picture 23 Repeat":"0","Picture 23 Hold":"0","Picture 23 Release":"0","---Picture 24---":"","Picture 24 Click":"0","Picture 24 Repeat":"0","Picture 24 Hold":"0","Picture 24 Release":"0","---Picture 25---":"","Picture 25 Click":"0","Picture 25 Repeat":"0","Picture 25 Hold":"0","Picture 25 Release":"0","---Picture 26---":"","Picture 26 Click":"0","Picture 26 Repeat":"0","Picture 26 Hold":"0","Picture 26 Release":"0","---Picture 27---":"","Picture 27 Click":"0","Picture 27 Repeat":"0","Picture 27 Hold":"0","Picture 27 Release":"0","---Picture 28---":"","Picture 28 Click":"0","Picture 28 Repeat":"0","Picture 28 Hold":"0","Picture 28 Release":"0","---Picture 29---":"","Picture 29 Click":"0","Picture 29 Repeat":"0","Picture 29 Hold":"0","Picture 29 Release":"0","---Picture 30---":"","Picture 30 Click":"0","Picture 30 Repeat":"0","Picture 30 Hold":"0","Picture 30 Release":"0","---Picture 31---":"","Picture 31 Click":"0","Picture 31 Repeat":"0","Picture 31 Hold":"0","Picture 31 Release":"0","---Picture 32---":"","Picture 32 Click":"0","Picture 32 Repeat":"0","Picture 32 Hold":"0","Picture 32 Release":"0","---Picture 33---":"","Picture 33 Click":"0","Picture 33 Repeat":"0","Picture 33 Hold":"0","Picture 33 Release":"0","---Picture 34---":"","Picture 34 Click":"0","Picture 34 Repeat":"0","Picture 34 Hold":"0","Picture 34 Release":"0","---Picture 35---":"","Picture 35 Click":"0","Picture 35 Repeat":"0","Picture 35 Hold":"0","Picture 35 Release":"0","---Picture 36---":"","Picture 36 Click":"0","Picture 36 Repeat":"0","Picture 36 Hold":"0","Picture 36 Release":"0","---Picture 37---":"","Picture 37 Click":"0","Picture 37 Repeat":"0","Picture 37 Hold":"0","Picture 37 Release":"0","---Picture 38---":"","Picture 38 Click":"0","Picture 38 Repeat":"0","Picture 38 Hold":"0","Picture 38 Release":"0","---Picture 39---":"","Picture 39 Click":"0","Picture 39 Repeat":"0","Picture 39 Hold":"0","Picture 39 Release":"0","---Picture 40---":"","Picture 40 Click":"0","Picture 40 Repeat":"0","Picture 40 Hold":"0","Picture 40 Release":"0","---Picture 41---":"","Picture 41 Click":"0","Picture 41 Repeat":"0","Picture 41 Hold":"0","Picture 41 Release":"0","---Picture 42---":"","Picture 42 Click":"0","Picture 42 Repeat":"0","Picture 42 Hold":"0","Picture 42 Release":"0","---Picture 43---":"","Picture 43 Click":"0","Picture 43 Repeat":"0","Picture 43 Hold":"0","Picture 43 Release":"0","---Picture 44---":"","Picture 44 Click":"0","Picture 44 Repeat":"0","Picture 44 Hold":"0","Picture 44 Release":"0","---Picture 45---":"","Picture 45 Click":"0","Picture 45 Repeat":"0","Picture 45 Hold":"0","Picture 45 Release":"0","---Picture 46---":"","Picture 46 Click":"0","Picture 46 Repeat":"0","Picture 46 Hold":"0","Picture 46 Release":"0","---Picture 47---":"","Picture 47 Click":"0","Picture 47 Repeat":"0","Picture 47 Hold":"0","Picture 47 Release":"0","---Picture 48---":"","Picture 48 Click":"0","Picture 48 Repeat":"0","Picture 48 Hold":"0","Picture 48 Release":"0","---Picture 49---":"","Picture 49 Click":"0","Picture 49 Repeat":"0","Picture 49 Hold":"0","Picture 49 Release":"0","---Picture 50---":"","Picture 50 Click":"0","Picture 50 Repeat":"0","Picture 50 Hold":"0","Picture 50 Release":"0","---Picture 51---":"","Picture 51 Click":"0","Picture 51 Repeat":"0","Picture 51 Hold":"0","Picture 51 Release":"0","---Picture 52---":"","Picture 52 Click":"0","Picture 52 Repeat":"0","Picture 52 Hold":"0","Picture 52 Release":"0","---Picture 53---":"","Picture 53 Click":"0","Picture 53 Repeat":"0","Picture 53 Hold":"0","Picture 53 Release":"0","---Picture 54---":"","Picture 54 Click":"0","Picture 54 Repeat":"0","Picture 54 Hold":"0","Picture 54 Release":"0","---Picture 55---":"","Picture 55 Click":"0","Picture 55 Repeat":"0","Picture 55 Hold":"0","Picture 55 Release":"0","---Picture 56---":"","Picture 56 Click":"0","Picture 56 Repeat":"0","Picture 56 Hold":"0","Picture 56 Release":"0","---Picture 57---":"","Picture 57 Click":"0","Picture 57 Repeat":"0","Picture 57 Hold":"0","Picture 57 Release":"0","---Picture 58---":"","Picture 58 Click":"0","Picture 58 Repeat":"0","Picture 58 Hold":"0","Picture 58 Release":"0","---Picture 59---":"","Picture 59 Click":"0","Picture 59 Repeat":"0","Picture 59 Hold":"0","Picture 59 Release":"0","---Picture 60---":"","Picture 60 Click":"0","Picture 60 Repeat":"0","Picture 60 Hold":"0","Picture 60 Release":"0","---Picture 61---":"","Picture 61 Click":"0","Picture 61 Repeat":"0","Picture 61 Hold":"0","Picture 61 Release":"0","---Picture 62---":"","Picture 62 Click":"0","Picture 62 Repeat":"0","Picture 62 Hold":"0","Picture 62 Release":"0","---Picture 63---":"","Picture 63 Click":"0","Picture 63 Repeat":"0","Picture 63 Hold":"0","Picture 63 Release":"0","---Picture 64---":"","Picture 64 Click":"0","Picture 64 Repeat":"0","Picture 64 Hold":"0","Picture 64 Release":"0","---Picture 65---":"","Picture 65 Click":"0","Picture 65 Repeat":"0","Picture 65 Hold":"0","Picture 65 Release":"0","---Picture 66---":"","Picture 66 Click":"0","Picture 66 Repeat":"0","Picture 66 Hold":"0","Picture 66 Release":"0","---Picture 67---":"","Picture 67 Click":"0","Picture 67 Repeat":"0","Picture 67 Hold":"0","Picture 67 Release":"0","---Picture 68---":"","Picture 68 Click":"0","Picture 68 Repeat":"0","Picture 68 Hold":"0","Picture 68 Release":"0","---Picture 69---":"","Picture 69 Click":"0","Picture 69 Repeat":"0","Picture 69 Hold":"0","Picture 69 Release":"0","---Picture 70---":"","Picture 70 Click":"0","Picture 70 Repeat":"0","Picture 70 Hold":"0","Picture 70 Release":"0","---Picture 71---":"","Picture 71 Click":"0","Picture 71 Repeat":"0","Picture 71 Hold":"0","Picture 71 Release":"0","---Picture 72---":"","Picture 72 Click":"0","Picture 72 Repeat":"0","Picture 72 Hold":"0","Picture 72 Release":"0","---Picture 73---":"","Picture 73 Click":"0","Picture 73 Repeat":"0","Picture 73 Hold":"0","Picture 73 Release":"0","---Picture 74---":"","Picture 74 Click":"0","Picture 74 Repeat":"0","Picture 74 Hold":"0","Picture 74 Release":"0","---Picture 75---":"","Picture 75 Click":"0","Picture 75 Repeat":"0","Picture 75 Hold":"0","Picture 75 Release":"0","---Picture 76---":"","Picture 76 Click":"0","Picture 76 Repeat":"0","Picture 76 Hold":"0","Picture 76 Release":"0","---Picture 77---":"","Picture 77 Click":"0","Picture 77 Repeat":"0","Picture 77 Hold":"0","Picture 77 Release":"0","---Picture 78---":"","Picture 78 Click":"0","Picture 78 Repeat":"0","Picture 78 Hold":"0","Picture 78 Release":"0","---Picture 79---":"","Picture 79 Click":"0","Picture 79 Repeat":"0","Picture 79 Hold":"0","Picture 79 Release":"0","---Picture 80---":"","Picture 80 Click":"13","Picture 80 Repeat":"0","Picture 80 Hold":"0","Picture 80 Release":"0","---Picture 81---":"","Picture 81 Click":"0","Picture 81 Repeat":"0","Picture 81 Hold":"0","Picture 81 Release":"0","---Picture 82---":"","Picture 82 Click":"0","Picture 82 Repeat":"0","Picture 82 Hold":"0","Picture 82 Release":"0","---Picture 83---":"","Picture 83 Click":"0","Picture 83 Repeat":"0","Picture 83 Hold":"0","Picture 83 Release":"0","---Picture 84---":"","Picture 84 Click":"0","Picture 84 Repeat":"0","Picture 84 Hold":"0","Picture 84 Release":"0","---Picture 85---":"","Picture 85 Click":"0","Picture 85 Repeat":"0","Picture 85 Hold":"0","Picture 85 Release":"0","---Picture 86---":"","Picture 86 Click":"0","Picture 86 Repeat":"0","Picture 86 Hold":"0","Picture 86 Release":"0","---Picture 87---":"","Picture 87 Click":"0","Picture 87 Repeat":"0","Picture 87 Hold":"0","Picture 87 Release":"0","---Picture 88---":"","Picture 88 Click":"0","Picture 88 Repeat":"0","Picture 88 Hold":"0","Picture 88 Release":"0","---Picture 89---":"","Picture 89 Click":"0","Picture 89 Repeat":"0","Picture 89 Hold":"0","Picture 89 Release":"0","---Picture 90---":"","Picture 90 Click":"0","Picture 90 Repeat":"0","Picture 90 Hold":"0","Picture 90 Release":"0","---Picture 91---":"","Picture 91 Click":"0","Picture 91 Repeat":"0","Picture 91 Hold":"0","Picture 91 Release":"0","---Picture 92---":"","Picture 92 Click":"0","Picture 92 Repeat":"0","Picture 92 Hold":"0","Picture 92 Release":"0","---Picture 93---":"","Picture 93 Click":"0","Picture 93 Repeat":"0","Picture 93 Hold":"0","Picture 93 Release":"0","---Picture 94---":"","Picture 94 Click":"0","Picture 94 Repeat":"0","Picture 94 Hold":"0","Picture 94 Release":"0","---Picture 95---":"","Picture 95 Click":"0","Picture 95 Repeat":"0","Picture 95 Hold":"0","Picture 95 Release":"0","---Picture 96---":"","Picture 96 Click":"0","Picture 96 Repeat":"0","Picture 96 Hold":"0","Picture 96 Release":"0","---Picture 97---":"","Picture 97 Click":"0","Picture 97 Repeat":"0","Picture 97 Hold":"0","Picture 97 Release":"0","---Picture 98---":"","Picture 98 Click":"0","Picture 98 Repeat":"0","Picture 98 Hold":"0","Picture 98 Release":"0","---Picture 99---":"","Picture 99 Click":"0","Picture 99 Repeat":"0","Picture 99 Hold":"0","Picture 99 Release":"0","---Picture 100---":"","Picture 100 Click":"0","Picture 100 Repeat":"0","Picture 100 Hold":"0","Picture 100 Release":"0"}},
{"name":"YEP_EventHitboxResize","status":true,"description":"v1.00 Allows you to make larger event hitboxes, making the event\nable to be triggered from multiple tiles.","parameters":{}},
{"name":"YEP_ExternalLinks","status":false,"description":"v1.01 Link back to your home page through the title screen\nand also be able to link your players from within the game.","parameters":{"Home Page URL":"https://www.patreon.com/SirensParadise","Home Page Text":"Home Page","Popup Blocker Notice":"The link was blocked by a pop-up blocker."}},
{"name":"YEP_EventClickTrigger","status":true,"description":"v1.00 Lets the player click on designated notetagged events to\ntrigger them from afar!","parameters":{}},
{"name":"TDDPMouseSystemEx","status":false,"description":"1.8.2(fix v.1.2) Custom mouse cursors, highlight menu items on hover, custom event mouse interaction and much more! See Help.                      id:TDDP_MouseSystemEx","parameters":{"---Custom Cursor---":"","Use Custom Cursor?":"false","Custom Cursor Image":"cursor1","Custom Cursors Folder":"img/cursors/","---Auto Change Cursors---":"","Show Text Cursor":"","Transfer Cursor":"","Change Gold Cursor":"","Change Items Cursor":"","Change Weapons Cursor":"","Change Armors Cursor":"","Battle Processing Cursor":"","---Auto Change Icons---":"","Show Text Icon":"","Transfer Icon":"","Change Gold Icon":"","Change Items Icon":"","Change Weapons Icon":"","Change Armors Icon":"","Battle Processing Icon":"","---Hover Select---":"","Highlight On Hover":"true","Hover SE Cooldown":"4","---Customizeable Notetags---":"","No Auto Cursor Notetag":"no_auto_cursor!","No Auto Icon Notetag":"no_auto_icon!","Click Notetag":"click_activate!","Hover Notetag":"hover_activate!","Leave Notetag":"leave_activate!","---Mouse Icons---":"","Hide Cursor":"false","Icon Offset X":"25","Icon Offset Y":"14","---Mouse Icon Tags---":"","Icon Tag 1":"quest: 191","Icon Tag 2":"chest: 210","Icon Tag 3":"door: 82","Icon Tag 4":"world_map: 190","Icon Tag 5":"potion: 176","Icon Tag 6":"poison: 177","Icon Tag 7":"activity: 73","Icon Tag 8":"notebook: 187","Icon Tag 9":"letter: 192","Icon Tag 10":"talk: 4","Icon Tag 11":"romance: 7","Icon Tag 12":"sleep: 8","Icon Tag 13":"key: 195","Icon Tag 14":"key: 195","Icon Tag 15":"key: 195"}},
{"name":"Mimosa_MouseCursor","status":true,"description":"| Mimosa Mouse Cursor : Version - 1.1.1 | This plugin allows you to change the cursor of your mouse while in the game window!.","parameters":{"enabled":"true","defaultCursor":"{\"name\":\"default\",\"cursorImage\":\"cursor\",\"cursorAnchor\":\"{\\\"x\\\":0,\\\"y\\\":0}\",\"clickImage\":\"\"}","battleCursor":"{\"name\":\"default\",\"cursorImage\":\"\",\"cursorAnchor\":\"{\\\"x\\\":0,\\\"y\\\":0}\",\"cursorFrames\":\"1\",\"cursorSpeed\":\"40\",\"clickImage\":\"\",\"clickFrames\":\"1\",\"clickSpeed\":\"40\"}","menuCursor":"{\"name\":\"default\",\"cursorImage\":\"\",\"cursorAnchor\":\"{\\\"x\\\":0,\\\"y\\\":0}\",\"cursorFrames\":\"1\",\"cursorSpeed\":\"40\",\"clickImage\":\"\",\"clickFrames\":\"1\",\"clickSpeed\":\"40\"}","presets":"[]","detectMouseOut":"false"}},
{"name":"MOG_TitlePictureCom","status":true,"description":"(v1.6 *) Adiciona comandos em imagens no lugar da janela.","parameters":{"-> Main <<<<<<<<<<<<<<<<<<<<<<<":"","Animation Mode":"0","Left & Right Input":"true","Com Fade-In Duration":"3","Slide X-Axis":"0","Slide Y-Axis":"50","":"","-> Smart Background  <<<<<<<<<<<<<<<<<<<<<<<":"","Smart Background":"false","Background X-Axis":"0","Background Y-Axis":"0","Background Fade-In Duration":"90","-> Title Sprite <<<<<<<<<<<<<<<<<<<<<<<":"","Title Sprite":"false","Title Sprite X-Axis":"300","Title Sprite Y-Axis":"150","Fade-In Duration":"40","Zoom Effect":"true","Zoom Speed":"40","-> Cursor <<<<<<<<<<<<<<<<<<<<<<<":"","Cursor X-Axis":"0","Cursor Y-Axis":"5","Cursor Visible":"false","Cursor Wave Animation":"true","Cursor Rotation Animation":"true","Cursor Rotation Speed":"0.05","-> Commands <<<<<<<<<<<<<<<<<<<<<<<":"","Command Pos 1":"550,325","Command Pos 2":"640,390","Command Pos 3":"730,455","Command Pos 4":"820,520","Command Pos 5":"910,585","Command Pos 6":"0,635","Command Pos 7":"0,192","Command Pos 8":"0,224","Command Pos 9":"0,256","Command Pos 10":"0,288"}},
{"name":"GALV_MessageBusts","status":false,"description":"(v.2.6) Displays a bust image instead of selected face image","parameters":{"Bust Priority":"0","Bust Position":"1","Text X Offset":"390","Filename Append":""}},
{"name":"Olivia_AnimatedPictures","status":true,"description":"<AnimatedPictures> for RPG Maker MV version 1.6.1.","parameters":{"":"","ATTENTION!!!":"READ THE HELP FILE","Loop by Default":"true","Wait Frames Default":"4"}},
{"name":"MrTS_EmptyMenu","status":true,"description":"Removes actor data and moves command window.","parameters":{"Show Gold Window":"False","Command X":"Graphics.boxWidth/2 - this._commandWindow.width/2","Command Y":"Graphics.boxHeight/2 - this._commandWindow.height/2","Gold X":"this._commandWindow.x","Gold Y":"this._commandWindow.y + this._commandWindow.height"}},
{"name":"GALV_DiagonalMovement","status":true,"description":"(v.1.5) Just a basic diagonal movement plugin. Written for compatibility with other Galv plugins.","parameters":{"Diagonal Mouse":"true","Diagonal Charset":"true","Diagonal Speed":"90","Block Diagonal":"false"}},
{"name":"GALV_TitleCommands","status":true,"description":"Allows you to customize what commands appear in the title screen menu.","parameters":{"Default Commands":"New Game,new,always|Continue,con,isSave|Options,opt,always|Credits,credits,always|Quit,quit,always","Mod Condition":"","Mod Commands":""}},
{"name":"Galv_ImageCache","status":true,"description":"(v.1.1) Pre-cache images that cause issues when loading during gameplay","parameters":{"Folder 1":"animations|","Folder 2":"battlebacks1|","Folder 3":"battlebacks2|","Folder 4":"characters|","Folder 5":"enemies|","Folder 6":"faces|","Folder 7":"parallaxes|","Folder 8":"pictures|","Folder 9":"sv_actors|","Folder 10":"sv_enemies|","Folder 11":"system|","Folder 12":"tilesets|","Folder 13":"titles1|","Folder 14":"titles2|","Folder 15":"","Folder 16":"","Folder 17":"","Folder 18":"","Folder 19":"","Folder 20":"","Folder 21":"","Folder 22":"","Folder 23":"","Folder 24":"","Folder 25":""}},
{"name":"GALV_RollCredits","status":true,"description":"(v.1.5) A plugin that calls a new scene to display scrolling information located in an external text file.","parameters":{"Folder":"data","Skippable":"true","Block Skipping":"true","Title Menu":"Credits","Title Credits Music":"Happy - syncopika"}},
{"name":"Irina_VisualNovelBusts","status":true,"description":"<Irina_VisualNovelBusts> for RPG Maker MV version 1.6.2.","parameters":{"":"","BustDefaults":"","Bust0Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"0.8\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"(Window_Base._faceWidth + 24) / 2 + this.standardPadding()\",\"ScreenY\":\"this.height\"}","Bust1Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (1 / 2)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust2Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (3 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust3Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (9 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust4Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (4 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust5Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (5 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust6Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (7 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust7Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (8 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust8Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (9 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust9Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (10 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","Bust10Default":"{\"AnchorX\":\"0.5\",\"AnchorY\":\"1\",\"ScaleX\":\"1\",\"ScaleY\":\"1\",\"ScreenX\":\"Graphics.boxWidth * (11 / 12)\",\"ScreenY\":\"Graphics.boxHeight\"}","BustSettings":"","BustClearDuration":"20","BustAutoClear":"true","BustExpressionList":"[\"Angry\",\"Surprised\",\"Sad\"]","BustFadeDuration":"8","BustMoveDuration":"20","BustMoveType":"Linear","BustOpacityDuration":"20","BustScaleDuration":"20","BustSlideDistance":"144","BustSlideDuration":"20","BustToneDuration":"20","BustDimValues":"-68, -68, 0, 68","BustLightValues":"0, 0, 0, 0"}},
{"name":"HIME_LargeChoices","status":true,"description":"v1.2 - Combines multiple show choice commands into a single,\r\nlarge list.","parameters":{}},
{"name":"HIME_HiddenChoiceConditions","status":true,"description":"Allows you to hide choices with a simple event call","parameters":{}},
{"name":"Mimosa_MouseCursorPlus","status":false,"description":"| Mimosa Mouse Cursor : Version - 1.3.0 | This plugin allows you to change the cursor of your mouse while in the game window!.","parameters":{"enabled":"true","defaultCursor":"{\"name\":\"default\",\"cursorImage\":\"cursor\",\"cursorAnchor\":\"{\\\"x\\\":0,\\\"y\\\":0}\",\"cursorFrames\":\"1\",\"cursorSpeed\":\"40\",\"clickImage\":\"\",\"clickFrames\":\"1\",\"clickSpeed\":\"40\"}","battleCursor":"{\"name\":\"default\",\"cursorImage\":\"\",\"cursorAnchor\":\"{\\\"x\\\":0,\\\"y\\\":0}\",\"cursorFrames\":\"1\",\"cursorSpeed\":\"40\",\"clickImage\":\"\",\"clickFrames\":\"1\",\"clickSpeed\":\"40\"}","menuCursor":"{\"name\":\"default\",\"cursorImage\":\"\",\"cursorAnchor\":\"{\\\"x\\\":0,\\\"y\\\":0}\",\"cursorFrames\":\"1\",\"cursorSpeed\":\"40\",\"clickImage\":\"\",\"clickFrames\":\"1\",\"clickSpeed\":\"40\"}","presets":"[\"{\\\"name\\\":\\\"cursor2\\\",\\\"cursorImage\\\":\\\"cursor2\\\",\\\"cursorAnchor\\\":\\\"{\\\\\\\"x\\\\\\\":0,\\\\\\\"y\\\\\\\":0}\\\",\\\"cursorFrames\\\":\\\"1\\\",\\\"cursorSpeed\\\":\\\"40\\\",\\\"clickImage\\\":\\\"\\\",\\\"clickFrames\\\":\\\"1\\\",\\\"clickSpeed\\\":\\\"40\\\"}\"]","pixelPrecision":"true","detectMouseOut":"false"}},
{"name":"Olivia_MetaControls","status":true,"description":"<MetaControls> for RPG Maker MV version 1.6.1.","parameters":{"":"","ATTENTION!!!":"READ THE HELP FILE","Common Event on New Game":"0","Common Event on Load":"0","Variables on Save":"","Map ID":"0","Map X":"0","Map Y":"0"}},
{"name":"YKNR_SaveThumbnail","status":true,"description":"セーブファイルにサムネイル用の画像を保存し、\nセーブ/ロード画面でファイル毎にサムネイルを表示する","parameters":{"AutoSnapForThumbnail":"true","FastLoadingData":"true","SnapSettings":"","ThumbQuality":"80","ThumbSaveWidth":"330","ThumbSaveHeight":"186","ShowInSavefileList":"false","ThumbItemPosX":"rect.x + rect.width - width;","ThumbItemPosY":"rect.y + 5","ThumbItemScale":"1.00","OtherWindowClass":"Window_SaveInfo","ThumbOWPosX":"rect.x + rect.width - width;","ThumbOWPosY":"rect.y + 5","ThumbOWScale":"1.00"}},
{"name":"YEP_X_MessageSpeedOpt","status":true,"description":"v1.00 (Requires YEP_MessageCore.js) Let your places adjust the\nspeed the message window displays text.","parameters":{"OptionsCommand":"Message Speed","DefaultSpeed":"8","InstantText":"Instant"}},
{"name":"kz_CGallery","status":true,"description":"v1.01 - CGモード","parameters":{"Columns per Page":"5","Rows per Page":"4","Total Gallery Slot":"20","List Picture Width":"180","List Picture Height":"110","List Picture Spacing":"9","Split ThumbNail":"false","List Overhead X":"170","List Overhead Y":"125","File Directory":"img/pictures/","File Name":"Pic_","CG Text":"[]","Text X":"10","Text Y":"10","Page X":"10","Page Y":"10","Left Arrow":"Left","Left Arrow X":"-50","Left Arrow Y":"270","Right Arrow":"Right","Right Arrow X":"1000","Right Arrow Y":"270","Text Width":"10","Text Height":"10","Cursor Padding":"3"}},
{"name":"YEP_DynamicTitleImages","status":true,"description":"v1.00 Change title images depending on how far along your game\nthe player has progressed.","parameters":{"RequireSaveGame":"false"}}
];
