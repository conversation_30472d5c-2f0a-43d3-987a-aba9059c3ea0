[null, {"id": 1, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Bat", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Bat", "note": "", "params": [200, 0, 30, 30, 30, 30, 30, 30]}, {"id": 2, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Slime", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Slime", "note": "", "params": [250, 0, 30, 30, 30, 30, 30, 30]}, {"id": 3, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Orc", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Orc", "note": "", "params": [300, 0, 30, 30, 30, 30, 30, 30]}, {"id": 4, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Minotaur", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Minotaur", "note": "", "params": [500, 0, 30, 30, 30, 30, 30, 30]}]