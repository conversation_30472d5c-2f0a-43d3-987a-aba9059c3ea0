{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "My Room", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0Bedroom_Day", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 3, "width": 27, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Exit", "note": "<Hitbox Up: 10> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 23, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 7, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 214, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 10, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 12}, {"id": 2, "name": "Wake Up Events (+More)", "note": "SS A: <PERSON>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 65, 0]}, {"code": 231, "indent": 1, "parameters": [15, "Present3", 0, 0, 936, 601, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 64, 0]}, {"code": 108, "indent": 1, "parameters": ["Old: Present3 Upper Left (971,408)"]}, {"code": 231, "indent": 1, "parameters": [15, "4Bedroom_TrophyHDay", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 212, 0]}, {"code": 231, "indent": 1, "parameters": [14, "4Bedroom_TrophyVDay", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 94, 0]}, {"code": 231, "indent": 2, "parameters": [14, "4Bedroom_Apple", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 44, 0]}, {"code": 117, "indent": 1, "parameters": [21]}, {"code": 121, "indent": 1, "parameters": [44, 44, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 19, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 65, 0]}, {"code": 231, "indent": 1, "parameters": [15, "Present3", 0, 0, 936, 601, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 64, 0]}, {"code": 231, "indent": 1, "parameters": [15, "4Bedroom_TrophyHDay", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 212, 0]}, {"code": 231, "indent": 1, "parameters": [14, "4Bedroom_TrophyVDay", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 94, 0]}, {"code": 231, "indent": 2, "parameters": [14, "4Bedroom_Apple", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "4Bedroom_SleepMorning", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "4Bedroom_GroggyMorning", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>*yawn*\\! Time to get up..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [0, 57, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 1, "parameters": ["Congratulations on building your relationship with \\n[2] to climactic heights!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 1, "parameters": ["But the fun isn't over yet!\\! \\n[2] has plenty more interactions for you to discover!"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 0"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 1"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 2"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 3"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 4"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 5"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 6"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 7"]}, {"code": 121, "indent": 1, "parameters": [57, 57, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 83, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 1, "parameters": ["Congratulations on building your relationship with \\n[6] \\n[3] to climactic heights!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 1, "parameters": ["But even more exciting moments are still to be had!\\! Continue interacting "]}, {"code": 401, "indent": 1, "parameters": ["with \\n[6] \\n[3] to see more!"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 8"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 9"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 10"]}, {"code": 121, "indent": 1, "parameters": [83, 83, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 122, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 1, "parameters": ["Congratulations on building your relationship with \\n[4] to climactic heights!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 1, "parameters": ["But the adventure with \\n[4] still continues!\\! More interactions await you around the house!"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 11"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 12"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 13"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 14"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 15"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 16"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 17"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 18"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 19"]}, {"code": 121, "indent": 1, "parameters": [122, 122, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 93, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 1, "parameters": ["Congratulations on achieving a threesome with both \\n[2] and \\n[6] \\n[3]!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 1, "parameters": ["Always eager for fun, you can now ask \\n[6] \\n[3] to join \\n[2] around the house at night!"]}, {"code": 121, "indent": 1, "parameters": [93, 93, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 197, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 1, "parameters": ["Congratulations on completing House Chores!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 1, "parameters": ["But the summer fun isn't over quite yet!\\! Stay tuned for the epilogue update releasing at a later date!"]}, {"code": 121, "indent": 1, "parameters": [197, 197, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 44, 0]}, {"code": 117, "indent": 1, "parameters": [21]}, {"code": 121, "indent": 1, "parameters": [44, 44, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 121, "indent": 0, "parameters": [19, 19, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 127, "switch1Valid": true, "switch2Id": 1, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 199, 1]}, {"code": 356, "indent": 1, "parameters": ["ChangeTitle1 Title_Casual"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "4Bedroom_SleepMorning", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "4Bedroom_GroggyMorning", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [10, "9DreamBG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 11, 1]}, {"code": 231, "indent": 1, "parameters": [20, "3MomBendSexA1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3MomBendSexA_PreIdle", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 180, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Don't be shy baby ~\\! grab my ass!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh \\n[2]!\\! Your butt is the best!\\! So bouncy and soft ~ I'm in heaven!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mmm, that's it baby!\\! Squeeze me!\\! Spank me!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh ya!\\! How's that feel \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh yes baby!\\! Keep spanking \\n[11]!\\! I want you to play with my ass all day!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hah hah!\\! This is the greatest day ever \\n[2]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>*yawn*\\! What?!\\! No!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>*sigh*\\! It was just a dream..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Still, that was really hot getting to just play with \\n[2]'s butt like that."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I have to go tell her about it!\\! Maybe she'll let me do it just like in my dream!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0LivingRoom_Day1", false, false, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hey, \\n[2], guess what?!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wait-\\! She's not here..?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Maybe she's doing something in the kitchen instead..?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Kitchen_Day", false, false, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[2]?\\! Are you in here?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>She's not here either?\\! Where in the world could she be?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Dining_Day", false, false, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Helloooo..?\\! \\n[2]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>I'm in here sweetie!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I heard her!\\! Is she in the laundry room?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Laundry_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "5L<PERSON><PERSON>end2[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>There you are, \\n[2]!\\! I wanted to tell you som-"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I...\\! Uhh..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>You had something you wanted to tell me?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Give me just a second sweetheart while I finish putting these clothes in the washing machine."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]*phew*\\! All done!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Alright, what is it that you wanted to tell me?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[2]...\\! That outfit..!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]Have I caught you off guard honey?"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Today is laundry day so I needed something to put on temporarily ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]I hope you don't mind me showing a little more skin than usual..."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Don't mind?!\\! \\n[2]! You look so hot!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]*giggle*\\! I'm glad you're easy to please!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Now what is it that you wanted to tell me about?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Huh?\\! Err, I think I forgot..."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Well no matter.\\! When you remember, just come and tell me, okay?"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]If you need me, I'll be out in the living room cleaning."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Today's laundry day so I have quite a list of things to get to..."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]I would appreciate it greatly if you could help with the laundry."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]If you help, maybe I can find a way to show my gratitude ~ ♥️"]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 122, "indent": 0, "parameters": [19, 19, 0, 0, 4]}, {"code": 201, "indent": 0, "parameters": [0, 30, 0, 14, 0, 0]}, {"code": 121, "indent": 0, "parameters": [127, 127, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 214, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [2]}, {"code": 111, "indent": 0, "parameters": [0, 65, 0]}, {"code": 231, "indent": 1, "parameters": [15, "Present3", 0, 0, 936, 601, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 64, 0]}, {"code": 231, "indent": 1, "parameters": [15, "4Bedroom_TrophyHDay", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 212, 0]}, {"code": 231, "indent": 1, "parameters": [14, "4Bedroom_TrophyVDay", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 94, 0]}, {"code": 231, "indent": 2, "parameters": [14, "4Bedroom_Apple", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "4Bedroom_SleepMorning", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "4Bedroom_GroggyMorning", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>*yawn*\\! Uhh ~\\! What happened..?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>Huh?\\! Someone left a note for me."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Book2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["\\n[1], we left your favorite snacks and drinks on your desk."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["If you're feeling better, could you please come and visit us in \\n[2]'s room?"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Book1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>\\n[2]'s room?\\! I wonder if they're still waiting for me in there."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 151, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>.\\| .\\| .\\| *yawn*"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[What's that feeling..?\\! Am I...\\! Am I dreaming..?]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>...\\n[2]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mmmh ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>I'm sorry to wake you baby."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Ohh ~ I just couldn't resist..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Continue", "Switch", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Continue"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Switch"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 111, "indent": 3, "parameters": [0, 16, 0]}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [200, 200, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [0, 16, 0]}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [200, 200, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 122, "indent": 3, "parameters": [9, 9, 0, 0, 1]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>W-Whoa!\\! Baby ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>Let \\n[11] take care of things..."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 7]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Good morning baby ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 121, "indent": 0, "parameters": [147, 147, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 151, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>.\\| .\\| .\\| *yawn*"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[What's that feeling..?\\! Am I...\\! Am I dreaming..?]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>...\\n[2]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mmmh ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>I'm sorry to wake you baby."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Ohh ~ I just couldn't resist that morning wood..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Continue", "Switch", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Continue"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Switch"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 111, "indent": 3, "parameters": [0, 16, 0]}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [200, 200, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [0, 16, 0]}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [200, 200, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 122, "indent": 3, "parameters": [9, 9, 0, 0, 1]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>W-Whoa!\\! Baby ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>Let \\n[11] take care of things..."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 7]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Good morning baby ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 121, "indent": 0, "parameters": [200, 200, 1]}, {"code": 121, "indent": 0, "parameters": [151, 151, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 147, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 199, 1]}, {"code": 356, "indent": 1, "parameters": ["ChangeTitle1 Title_Naughty"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Good mooorning \\n[1] ~ ♫"]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 1]\\n[3]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).move<PERSON><PERSON>(-200, 0, 60)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 1]There you are <PERSON><PERSON>!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 0]My goodness, I've been looking everywhere for you."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 5]I should have thought to check \\n[1]'s room much sooner!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 1]But first I have to address the completely obvious question in the room."]}, {"code": 111, "indent": 0, "parameters": [0, 200, 0]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Why are you in partially undressed and laying under \\n[1]..?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 2]Why are you in partially undressed and straddled on top of \\n[1]..?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Hah!\\! Well, you see..."]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]When you knocked on the door, I got startled and tripped!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]My clothes got all twisted up when \\n[1] caught my fall!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]Silly me!\\! Always so clumsy..!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]Well I suppose that's as good of an excuse as any!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 1]Don't mind me then!\\! I'll be on my way."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 7]And \\n[1].\\! You probably shouldn't stare at your \\n[5]'s breasts like that..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 1]Its quite inappropriate dear!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Huh?!\\! Oh, yup!\\! Sorry about that!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).move<PERSON><PERSON>(+550, 0, 60)"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).move<PERSON><PERSON>(+200, 0, 60)"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Nice work \\n[2]!\\! I don't think she suspected a thing!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Do you really think so baby?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Definitely not!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]That's a relief!\\! I thought our cover was blown!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Maybe I should get my clothes back on and get back to cleaning, huh?"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]We'll continue this later ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [15, 15, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 121, "indent": 0, "parameters": [151, 151, 1]}, {"code": 121, "indent": 0, "parameters": [200, 200, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 121, "indent": 0, "parameters": [147, 147, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 95, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [160]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\n[1] ~ ♫"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Wake up, honey ~ !"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*yawn* ~\\! \\n[2]?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]I wasn't expecting to find you still asleep!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]I thought about letting you sleep in but I didn't want you to feel groggy."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Thanks \\n[2].\\! I guess I just got really sleepy all of a sudden..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]You've had quite an eventful summer.\\! I'm not surprised you're a little worn out."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1,0]Be sure to stay well rested baby.\\! If you need me, I'll be folding laundry."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wait, \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Yes?\\! What is it, \\n[1]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Are we still in Valencia?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Our neighborhood?\\! Valencia Springs?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]Of course we are, honey."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Right...\\! Yeah."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]What makes you ask a question like that?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh nothing.\\! I guess I just had a strange dream or something."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]*giggle*\\! You must have been sleeping very heavily, huh?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Once you're up, why don't you come join me later?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Perhaps we can share another golden apple together ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["You can now revisit Valencia by taking a bite out of the golden apple!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["Congratulations to \\n[2] for winning the hearts of many!"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 121, "indent": 0, "parameters": [95, 95, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, {"id": 3, "name": "VR Headset", "note": "<Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>The Sup3r VR!\\! That cost me all of my savings to buy."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>And now I'm too broke to even buy a game for it..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 6}, {"id": 4, "name": "Sleep", "note": "<Hitbox Up: 3> <Hitbox Right: 7>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's too early to go to sleep now."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 153, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Sex With \\n[2]", "Back"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Sex With \\n[2]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [145, 145, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Back"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 214, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>I'm not tired at all at the moment."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 13}, {"id": 5, "name": "Sleep", "note": "<Hitbox Up: 1> <Hitbox Right: 5>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's too early to go to sleep now."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 153, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Sex With \\n[2]", "Back"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Sex With \\n[2]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [145, 145, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Back"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 214, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>I'm not tired at all at the moment."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 9}, {"id": 6, "name": "Curtains", "note": "<Hitbox Up: 6> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I've always preferred keeping my window closed.\\! The sunlight glare is awful when I'm gaming!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 8}, {"id": 7, "name": "Fap Activity", "note": "<Hitbox Up: 1> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 27, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon quest;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 111, "indent": 0, "parameters": [0, 11, 0]}, {"code": 355, "indent": 1, "parameters": ["Galv.CACHE.load('pictures','3ChibiMomSexS[3x3]');"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["Galv.CACHE.load('pictures','3ChibiMomSex[4x4]');"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["To pass the time, you can 'relieve' some stress at your desk."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["As others 'inspire' you to think about them, you can unlock more scenes to fap to."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["But your imagination isn't anywhere near as nice as the real deal!\\! Interact with others to see if you can get lucky!"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I can't believe I'm about to fap to my own \\n[5]..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>But I just can't stop thinking about it!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>About me sitting behind her big juicy butt and just..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [0, 11, 0]}, {"code": 231, "indent": 1, "parameters": [20, "3ChibiMomSexS[3x3]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3ChibiMomSex[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 4"]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Ramming my cock inside of her..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hearing her scream my name in ecstasy, like-"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\n[2]>\\n[1]! Oh baby, fuck me!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\n[2]>Fuck me harder \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh \\n[2]! \\!I-I think I'm going to cum soon!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]\\n[1]. You haven't happened to see my-"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 4]Oh my..!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[2]?! \\!W-What are you doing?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 7]I'm so sorry sweetie!\\! I'll step outside and give you some privacy..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>...\\| ...\\| ..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>This is the worst day of my life..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 284, "indent": 0, "parameters": ["0Hallway_Day", false, false, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["Mom2[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]\\bustExp[1, 7]"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 231, "indent": 0, "parameters": [90, "8StatUp_Mom2", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Sexual activities will increase \\n[2]'s Passion!\\! "]}, {"code": 401, "indent": 0, "parameters": ["Pink and Red hearts indicate "]}, {"code": 401, "indent": 0, "parameters": ["her mood increasing."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Balancing romance and activities are the key "]}, {"code": 401, "indent": 0, "parameters": ["to House Chores!\\! If you see an \" ! \" that means a special event has been "]}, {"code": 401, "indent": 0, "parameters": ["unlocked somewhere!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Be mindful when deciding how to respond to others as well.\\! Certain choices may impact "]}, {"code": 401, "indent": 0, "parameters": ["how they react to you as you develop your relationship."]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 7]There's no mistaking it.\\! I heard him saying my name..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 4]He was really masturbating to the thought of me..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 4]But most importantly..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 7]Why did I walk in on him like that?\\! I knew he was in there jacking off!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 7]And yet for some reason I..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 8]Felt kind of excited about that..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 201, "indent": 0, "parameters": [0, 11, 0, 5, 8, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>What a day...\\! I should probably just call it a night now and get some sleep."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 121, "indent": 0, "parameters": [26, 26, 1]}, {"code": 121, "indent": 0, "parameters": [31, 31, 0]}, {"code": 121, "indent": 0, "parameters": [21, 21, 0]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 355, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','3ChibiMomSex[4x4]');"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(2, \"$gameVariables.value(19)  < 2\")"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(3, \"$gameSwitches.value(81) == false\")"]}, {"code": 102, "indent": 0, "parameters": [["Fap (\\n[2])", "\\n[2] Visit", "\\n[3] Visit", "Pass Time", "Leave"], 4, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Fap (\\n[2])"]}, {"code": 111, "indent": 1, "parameters": [0, 11, 0]}, {"code": 355, "indent": 2, "parameters": ["Galv.CACHE.load('pictures','3ChibiMomSexS[3x3]');"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["Galv.CACHE.load('pictures','3ChibiMomSex[4x4]');"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I just can't stop thinking about \\n[2]!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Thinking about me sitting behind her big juicy butt and just..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 111, "indent": 1, "parameters": [0, 11, 0]}, {"code": 231, "indent": 2, "parameters": [20, "3ChibiMomSexS[3x3]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3ChibiMomSex[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 20 Speed 4"]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Ramming my cock inside of her..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Hearing her scream my name in ecstasy, like-"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\n[2]>\\n[1]! Oh baby, fuck me!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\n[2]>Fuck me harder \\n[1]!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Oh \\n[2]! \\!I-I think I'm going to cum!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, true]}, {"code": 250, "indent": 1, "parameters": [{"name": "Cumshot", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 11, 0, 6, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\n[2] Visit"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(153) === false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(153) === true\")"]}, {"code": 102, "indent": 1, "parameters": [["<PERSON><PERSON><PERSON><PERSON>", "Sex", "???", "Cancel"], 3, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "<PERSON><PERSON><PERSON><PERSON>"]}, {"code": 121, "indent": 2, "parameters": [143, 143, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Sex"]}, {"code": 121, "indent": 2, "parameters": [144, 144, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "???"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["Continue progressing through \\n[2]'s story to unlock!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Cancel"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\n[3] Visit"]}, {"code": 121, "indent": 1, "parameters": [161, 161, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Pass Time"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 11, 0, 6, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 31, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I shouldn't do that right now.\\! I should talk to \\n[2]."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 214, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>Now's really not the time for that."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 8}, {"id": 8, "name": "Updates", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 33, 0]}, {"code": 121, "indent": 1, "parameters": [31, 31, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 4, 0]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["Your save file has been updated to function in Beta 0.2.9. However, I "]}, {"code": 401, "indent": 0, "parameters": ["highly encourage a new playthrough as some aspects of dialogue & story might "]}, {"code": 401, "indent": 0, "parameters": ["not appear in the intended order."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["As House Chores moves farther along in development, the need to "]}, {"code": 401, "indent": 0, "parameters": ["adjust old saves should happen less frequently!"]}, {"code": 121, "indent": 0, "parameters": [181, 181, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 181, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 33, 0]}, {"code": 121, "indent": 1, "parameters": [35, 35, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [182, 182, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 182, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 235, "indent": 0, "parameters": [98]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 111, "indent": 0, "parameters": [0, 25, 0]}, {"code": 122, "indent": 1, "parameters": [29, 29, 1, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [6, 6, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [183, 183, 0]}, {"code": 121, "indent": 0, "parameters": [12, 12, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 183, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 14, 1]}, {"code": 121, "indent": 1, "parameters": [12, 12, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [184, 184, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 184, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 14, 1]}, {"code": 121, "indent": 1, "parameters": [12, 12, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 121, "indent": 1, "parameters": [12, 12, 1]}, {"code": 121, "indent": 1, "parameters": [13, 13, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [185, 185, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 185, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 17, 0, 20, 0]}, {"code": 122, "indent": 1, "parameters": [17, 17, 0, 0, 19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [187, 187, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 187, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 55, 0]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 0"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 1"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 2"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 3"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 4"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 5"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 6"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 7"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 81, 0]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 8"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 9"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 10"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 110, 0]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 11"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 12"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 13"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 14"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 15"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 16"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 17"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 18"]}, {"code": 356, "indent": 1, "parameters": ["enable_picture 19"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [188, 188, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 188, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 0}, {"id": 9, "name": "Misc Dialogue/Notifications", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 31, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*sigh*\\! Yesterday was so embarassing..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Still, It's probably best that I apologize to \\n[2] before she grounds me for the rest "]}, {"code": 401, "indent": 0, "parameters": ["of the summer."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 0}, {"id": 10, "name": "<PERSON><PERSON>", "note": "<Hitbox Right: 2> <Hitbox Up: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>My own signed poster by none other than <PERSON><PERSON>!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>She's so hot when she dances on stage!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 2}, {"id": 11, "name": "Mom BJ/Sex Sound Effects", "note": "1-3 BJ | 5-7 Sex", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [43]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [21]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [18]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [37]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [9]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [22]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 22, "y": 0}, {"id": 12, "name": "Holiday Event", "note": "<Hitbox Right: 1> <Hitbox Down: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 64, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["Would you like to go to the Festive Living Room?"]}, {"code": 102, "indent": 0, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Yes"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 201, "indent": 1, "parameters": [0, 24, 0, 14, 8, 0]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "No"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 64, "switch1Valid": true, "switch2Id": 214, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>I shouldn't get distracted right now."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 8}, {"id": 13, "name": "<PERSON><PERSON>", "note": "<PERSON>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Soft3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Soft3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Soft4", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Mary_Soft4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Mary_Soft5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [135]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Mary_Soft6", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [140]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Mary_Soft2", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ1", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ2", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf BJ3", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ3", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf BJ5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [110]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex4", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex5", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex6", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 8}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex6", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 10}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 0}, {"id": 14, "name": "Holiday Present Start", "note": "<Hitbox Right: 1> <Hitbox Down: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 65, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's so neatly wrapped...\\! Let's see what's inside."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [15, 0, 0, 0, 936, 601, 100, 100, 0, 0, 40, true]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Happy Holidays from yours truly ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["I have hidden special gifts for you around the house."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["If you find all three, you'll receive your extra special present ~ ♥️"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>An extra special present...\\! Wrapped in holiday gift wrapping..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>... Santa Clause?!?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 121, "indent": 0, "parameters": [66, 66, 0]}, {"code": 121, "indent": 0, "parameters": [61, 61, 0]}, {"code": 121, "indent": 0, "parameters": [62, 62, 0]}, {"code": 121, "indent": 0, "parameters": [63, 63, 0]}, {"code": 121, "indent": 0, "parameters": [65, 65, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 12}, {"id": 15, "name": "Present Special Dialogues", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 65, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Huh?!\\! What is that gift on the ground?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>That hasn't always been here, right..?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 64, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*yawn*\\! Wait."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Did last night really happen?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 140, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 140, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Good morning, baby!\\! I thought you might have slept in..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Oh, uhh, good morning \\n[2]."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]If you need me, I'll be in the living room."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]And thank you again for my present last night.\\! I loved it very much!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["You can now revisit the Living Room (Festive) by interacting "]}, {"code": 401, "indent": 0, "parameters": ["with the present on your shelf!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Happy Holidays!"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 0}, {"id": 16, "name": "Aunt Sex Cutscene", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 21, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]\\n[1]... I..."]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 2, 1]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I don't think we should continue doing these things anymore..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Wait, what?!\\! Why not \\n[6] \\n[3]?"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I'm worried that I'm just too much trouble for you."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I'm needy...\\! Demanding...\\! Insufferable."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]At least, that's what I've been called before."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]After a while, you'll get annoyed with me too..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]...\\! I don't think you're any of those things."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You're exciting and spontaneous!\\! And, well- I've always thought you were so beautiful..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]You thought I was beautiful?!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I mean, I still do..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]\\n[1]...\\! You're so sweet..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]But I'm your \\n[6].\\! I just think I'll only cause you more trouble."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>N-No!\\! I like trouble!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I want to know how you truly feel about me."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Huh?\\! What do you mean \\n[6] \\n[3]?"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]What do you see in me?\\! At this point in my life I'm nothing but an old bother to the people around me..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]This 'fun' we've been having...\\! I know that one day you'll be sick of me too."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>What are you talking about?!\\! I love you \\n[6] \\n[3]."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]Wait, you WHAT?!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I love you!\\! You've always been so kind to me."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You're someone I've always looked up to.\\! And to be honest..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I always had a little crush on you..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]You had a crush on me..?!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]\\n[1], that's the sweetest thing anyone's ever told me."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]But I'm your \\n[6].\\! I don't know if we should really keep this up any longer."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>N-No!\\! I don't want this to stop!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I mean I-\\! I just really like the things we've been doing this summer..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]*giggle*\\! Judging by that reaction, I can tell..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]. . . *sigh*"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 3]Ugh, I don't know why I'm even getting all worked up."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]A little love never hurt anyone, right honey?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Maybe we can keep this 'summer fling' up a little longer, don't you agree?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>YES!\\! <PERSON>rgh, um, yeah, I agree..!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]Oh ho ho!\\! Your eagerness doesn't lie!"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 2, 1]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]So you think I'm beautiful, huh?"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]How about you come show me how you really feel ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]So you've had a crush on me, huh?"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]How about I let your wildest dreams come true then ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Should I keep this outfit on, or take it all off?"]}, {"code": 102, "indent": 0, "parameters": [["<PERSON><PERSON>", "Clothed"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<PERSON><PERSON>"]}, {"code": 121, "indent": 1, "parameters": [16, 16, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Clothed"]}, {"code": 121, "indent": 1, "parameters": [16, 16, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 121, "indent": 0, "parameters": [82, 82, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 21, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]\\n[1]... I..."]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 2, 1]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I don't think we should continue doing these things anymore..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Wait, what?!\\! Why not \\n[6] \\n[3]?"]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I'm worried that I'm just too much trouble for you."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I'm needy...\\! Demanding...\\! Insufferable."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]At least, that's what I've been called before."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]After a while, you'll get annoyed with me too..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]...\\! I don't think you're any of those things."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You're exciting and spontaneous!\\! And, well- I've always thought you were so beautiful..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]You thought I was beautiful?!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I mean, I still do..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]\\n[1]...\\! You're so sweet..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]But I'm your \\n[6].\\! I just think I'll only cause you more trouble."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>N-No!\\! I like trouble!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I want to know how you truly feel about me."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Huh?\\! What do you mean \\n[6] \\n[3]?"]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]What do you see in me?\\! At this point in my life I'm nothing but an old bother to the people around me..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]This 'fun' we've been having...\\! I know that one day you'll be sick of me too."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>What are you talking about?!\\! I love you \\n[6] \\n[3]."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]Wait, you WHAT?!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I love you!\\! You've always been so kind to me."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You're someone I've always looked up to.\\! And to be honest..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I always had a little crush on you..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]You had a crush on me..?!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]\\n[1], that's the sweetest thing anyone's ever told me."]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]But I'm your \\n[6].\\! I don't know if we should really keep this up any longer."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>N-No!\\! I don't want this to stop!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I mean I-\\! I just really like the things we've been doing this summer..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]*giggle*\\! Judging by that reaction, I can tell..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]. . . *sigh*"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 3]Ugh, I don't know why I'm even getting all worked up."]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]A little love never hurt anyone, right honey?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Maybe we can keep this 'summer fling' up a little longer, don't you agree?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>YES!\\! <PERSON>rgh, um, yeah, I agree..!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]Oh ho ho!\\! Your eagerness doesn't lie!"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 2, 1]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]So you think I'm beautiful, huh?"]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]How about you come show me how you really feel ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]So you've had a crush on me, huh?"]}, {"code": 101, "indent": 1, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]How about I let your wildest dreams come true then ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 11]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 121, "indent": 0, "parameters": [82, 82, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 21, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Aunt1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]\\bustExp[1, 5] \\^"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Wow!\\! That sure gave me quite a thrill..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Did I do alright..?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Oh honey, you were wonderful..!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'll make sure I won't tell anyone either!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I've always had a lot of trouble kissing and not telling..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wait!\\! You're not gonna tell \\n[2] are you?!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Well, keeping this little secret of ours does appear to make things a little more exciting."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Phew..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]You'll just have to promise that you keep fucking me like that from here on out!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hah!\\! You can count on that \\n[6] \\n[3]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 1, 0]}, {"code": 117, "indent": 1, "parameters": [10]}, {"code": 121, "indent": 1, "parameters": [19, 19, 0]}, {"code": 201, "indent": 1, "parameters": [0, 12, 0, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 11, 0, 14, 8, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 201, "indent": 2, "parameters": [0, 12, 0, 8, 8, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [84, 84, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 21, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Aunt2[BUST][Exp3x3]')"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]\\bustExp[1, 5] \\^"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Wow!\\! That sure gave me quite a thrill..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Did I do alright..?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Oh honey, you were wonderful..!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'll make sure I won't tell anyone either!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I've always had a lot of trouble kissing and not telling..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wait!\\! You're not gonna tell \\n[2] are you?!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Well, keeping this little secret of ours does appear to make things a little more exciting."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Phew..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]You'll just have to promise that you keep fucking me like that from here on out!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hah!\\! You can count on that \\n[6] \\n[3]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 12, 0, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 12, 0, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [84, 84, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 84, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 17, "name": "Invite Activities", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 143, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [0, 55, 0]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Do you have a moment baby?"]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I thought maybe you'd like a blowjob from \\n[11]?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Of course!\\! I love your blowjobs \\n[2]!"]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Mmm ~ good.\\! Then sit back and let \\n[11] take care of you ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]H-Hey sweetie, am I interrupting you?"]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]I just thought that maybe I could help you out while the others aren't around."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Really?\\! Ya, that would be awesome."]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Okay.\\! Just sit tight and I'll take care of you."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [16, 16, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3xMomBJ_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomBJ[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 4]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [0, 55, 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>[This hot, thick cock...]"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>[I really am a naughty \\n[5] sucking on it with so much intensity!]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>[I can't believe I'm doing this...]"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>[I just couldn't stop thinking about sucking him off...]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Continue", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Continue"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomBJ_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBJ[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBJ[4x4]", 0, 0, 600, 233, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Continue", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Continue"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomBJ_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBJ[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBJ[4x4]", 0, 0, 600, 233, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Finish", "Zoom In", "Zoom Out", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomBJ_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBJ[4x4]", 0, 0, 400, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBJ[4x4]", 0, 0, 600, 233, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [0, 55, 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Ahhh ~ !\\! \\n[2] ~ !"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>[Here he cums!\\! Empty it all out baby!]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>[I think he's almost there!\\! I can feel him!]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "<PERSON><PERSON>_Finish", "volume": 80, "pitch": 90, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 12"]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [0, 55, 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>[Mmm ~\\! I was craving a mouthful of his cum ~ ♥️]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>[Wow..!\\! It's really quite delicious ~ ♥️]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 11, 0, 6, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [143, 143, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 144, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Hey baby, I hope I'm not interrupting anything..."]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 140, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]I just thought you could use a little treat."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 118, "indent": 0, "parameters": ["Start"]}, {"code": 121, "indent": 0, "parameters": [200, 200, 1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3zMomChairSex1[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3xMomChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3xMomChairSex1[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [2, "D", 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Baby, you're incredible ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "C", 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>H-How can you still keep going..?!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "B", 0]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>*pant*\\! Incredible..!"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[23]\\N[2]>Mmm, I'd love another round ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[23]\\N[2]>Ohh ~ Are you enjoying your \\n[5]'s tits baby?"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(200) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(200) === false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(5, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Kiss", "Ride", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Kiss"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomChairKiss1[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomChairKiss1[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 245, "indent": 2, "parameters": [{"name": "MilfMedKissBJ_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Ride"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomChairSex1[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomChairSex1[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 246, "indent": 2, "parameters": [2]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomChairSex1[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomChairKiss1[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomChairSex1[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomChairKiss1[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [5, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [22, "3zMomChairSex2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xMomChairKiss2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [22, "3xMomChairSex2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 5]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 111, "indent": 0, "parameters": [0, 200, 1]}, {"code": 245, "indent": 1, "parameters": [{"name": "PixieDeepSlow", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(200) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(200) === false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(5, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Kiss", "Ride", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Kiss"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairKiss2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 245, "indent": 2, "parameters": [{"name": "MilfMedKissBJ_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Ride"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairSex2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairSex2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 245, "indent": 2, "parameters": [{"name": "PixieDeepSlow", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairSex2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairKiss2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairSex2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [5, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 6]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(200) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(200) === false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(5, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Kiss", "Ride", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Kiss"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairKiss2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 245, "indent": 2, "parameters": [{"name": "MilfMedKissBJ_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Ride"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairSex2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairSex2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 245, "indent": 2, "parameters": [{"name": "PixieDeepSlow", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairKiss2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairSex2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairSex2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [5, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 7]}, {"code": 111, "indent": 0, "parameters": [0, 200, 1]}, {"code": 245, "indent": 1, "parameters": [{"name": "PixieDeepFast", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(200) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(200) === false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(5, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Cum", "Kiss", "Ride", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Cum"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Kiss"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairKiss2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 245, "indent": 2, "parameters": [{"name": "MilfMedKissBJ_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Ride"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairSex2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairSex2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 245, "indent": 2, "parameters": [{"name": "MilfMedFast_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairKiss2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3xMomChairSex2[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [22, "3zMomChairSex2[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [5, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomChairKiss3[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3zMomChairSex3[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomChair<PERSON>iss3[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3xMomChairSex3[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [0, 200, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>_Finish", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Finish1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 111, "indent": 0, "parameters": [0, 200, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "MilfMedKissBJ_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>[He stuffed me full of cum ~ ♥️]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>Oh baby.\\! You completely filled me up ~ ♥️"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 1]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>[I can still feel him cumming inside me ~ ♥️]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>Mmm, I can still feel you cumming inside ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "C", 1]}, {"code": 111, "indent": 3, "parameters": [0, 200, 0]}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[23]\\N[2]>[He's still going ~ ♥️]"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[23]\\N[2]>I'm glad you've enjoyed my gift ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "C", 1]}, {"code": 111, "indent": 4, "parameters": [0, 200, 0]}, {"code": 101, "indent": 5, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 5, "parameters": ["\\n<\\c[23]\\N[2]>[My body doesn't want to stop getting fucked by him ~ ♥️]"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 101, "indent": 5, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 5, "parameters": ["\\n<\\c[23]\\N[2]>You don't ever tired out do you ~ ♥️"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [0, 200, 0]}, {"code": 101, "indent": 5, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 5, "parameters": ["\\n<\\c[23]\\N[2]>[. . . ~ ♥️]"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 101, "indent": 5, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 5, "parameters": ["\\n<\\c[23]\\N[2]>. . . ♥️"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(200) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(200) === false\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(5, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(6, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Keep Going!", "Finish", "Kiss", "Ride", "Zoom In", "Zoom Out"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Keep Going!"]}, {"code": 111, "indent": 2, "parameters": [2, "A", 1]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "B", 1]}, {"code": 123, "indent": 4, "parameters": ["B", 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [2, "C", 1]}, {"code": 123, "indent": 5, "parameters": ["C", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 123, "indent": 5, "parameters": ["D", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 246, "indent": 2, "parameters": [2]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["Start"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Kiss"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomChairKiss3[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomChair<PERSON>iss3[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 245, "indent": 2, "parameters": [{"name": "MilfMedKissBJ_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Ride"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 16, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomChairSex3[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomChairSex3[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 246, "indent": 2, "parameters": [2]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [200, 200, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomChairSex3[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomChair<PERSON>iss3[4x4]", 0, 0, 460, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [5, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 1]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomChairSex3[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomChairKiss3[4x4]", 0, 0, 580, 160, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 11, 0, 6, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 121, "indent": 0, "parameters": [200, 200, 1]}, {"code": 121, "indent": 0, "parameters": [144, 144, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 161, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]*sigh*\\! I'm so bored right now..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]So I thought that maybe I could entertain myself with your company."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Just sit back and let \\n[12] have some fun ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3zAuntChair0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 231, "indent": 2, "parameters": [23, "3zAuntChairCum0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3xAuntChair0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 231, "indent": 2, "parameters": [23, "3xAuntChairCum0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["Start"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [2, "D", 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Mmh ~\\! Give me more ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "C", 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>I can barely feel my legs, but I just don't want to stop ~ ♥️"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "B", 0]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>Y-You don't want me to stop yet?!\\! \\n[1] ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>You're ready for more?\\! I love a man with vigor ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>Just sit back and relax.\\! I'll take good care of you ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xAuntChair0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3xAuntChairCum0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zAuntChair0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3zAuntChairCum0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [22, "3zAuntChair1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 231, "indent": 2, "parameters": [23, "3zAuntChairCum1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [22, "3xAuntChair1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 231, "indent": 2, "parameters": [23, "3xAuntChairCum1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 5]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedSlow_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xAuntChair1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3xAuntChairCum1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3zAuntChair1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3zAuntChairCum1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 6]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xAuntChair1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3xAuntChairCum1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3zAuntChair1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3zAuntChairCum1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 7]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedFast_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Cum", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Cum"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xAuntChair1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3xAuntChairCum1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3zAuntChair1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3zAuntChairCum1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [21, "3zAuntChair0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [23, "3zAuntChairCum0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3xAuntChair0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [23, "3xAuntChairCum0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Finish1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 1]}, {"code": 250, "indent": 2, "parameters": [{"name": "Z Milf2 Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "Z Milf2 Finish3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>You came so hard for \\n[12]!\\! I'm so glad ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 1]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>Your cum feels so warm inside me, I love it!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "C", 1]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>With this much cum inside me, you're playing a dangerous game ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "C", 1]}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>*pant*\\! H-How are you still cumming?!"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>Ohhh, fuck ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Keep Going!", "Finish", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Keep Going!"]}, {"code": 111, "indent": 2, "parameters": [2, "A", 1]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "B", 1]}, {"code": 123, "indent": 4, "parameters": ["B", 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [2, "C", 1]}, {"code": 123, "indent": 5, "parameters": ["C", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 123, "indent": 5, "parameters": ["D", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["Start"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xAuntChair0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [23, "3xAuntChairCum0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zAuntChair0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [23, "3zAuntChairCum0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [24, 24, 1, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 121, "indent": 0, "parameters": [44, 44, 0]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 11, 0, 6, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 121, "indent": 0, "parameters": [161, 161, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 161, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]*phew*\\! What a good workout!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Would you like to help me with some cardio to cool down..?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Don't worry, I'll set the pace ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3zAuntChair0a[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 231, "indent": 2, "parameters": [23, "3zAuntChairCum0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [21, "3xAuntChair0a[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 231, "indent": 2, "parameters": [23, "3xAuntChairCum0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["Start"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [2, "D", 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Mmh ~\\! Give me more ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "C", 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>I can barely feel my legs, but I just don't want to stop ~ ♥️"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "B", 0]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>Y-You don't want me to stop yet?!\\! \\n[1] ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>You're ready for more?\\! I love a man with vigor ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>Just sit back and relax.\\! I'll take good care of you ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xAuntChair0a[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3xAuntChairCum0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zAuntChair0a[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3zAuntChairCum0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [22, "3zAuntChair1a[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 231, "indent": 2, "parameters": [23, "3zAuntChairCum1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [22, "3xAuntChair1a[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 231, "indent": 2, "parameters": [23, "3xAuntChairCum1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 5]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedSlow_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xAuntChair1a[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3xAuntChairCum1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3zAuntChair1a[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3zAuntChairCum1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 6]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xAuntChair1a[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3xAuntChairCum1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3zAuntChair1a[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3zAuntChairCum1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 6"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 7]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedFast_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Cum", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Cum"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xAuntChair1a[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3xAuntChairCum1[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3zAuntChair1a[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [23, "3zAuntChairCum1[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 4"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [21, "3zAuntChair0a[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [23, "3zAuntChairCum0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3xAuntChair0a[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [23, "3xAuntChairCum0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Finish1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 1]}, {"code": 250, "indent": 2, "parameters": [{"name": "Z Milf2 Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "Z Milf2 Finish3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>You came so hard for \\n[12]!\\! I'm so glad ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 1]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>Your cum feels so warm inside me, I love it!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "C", 1]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>With this much cum inside me, you're playing a dangerous game ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "C", 1]}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>*pant*\\! H-How are you still cumming?!"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[21]\\N[3]>Ohhh, fuck ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Keep Going!", "Finish", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Keep Going!"]}, {"code": 111, "indent": 2, "parameters": [2, "A", 1]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "B", 1]}, {"code": 123, "indent": 4, "parameters": ["B", 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [2, "C", 1]}, {"code": 123, "indent": 5, "parameters": ["C", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 123, "indent": 5, "parameters": ["D", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["Start"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Finish"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xAuntChair_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xAuntChair0a[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [23, "3xAuntChairCum0[4x4]", 0, 0, 425, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zAuntChair0a[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [23, "3zAuntChairCum0[4x4]", 0, 0, 480, 180, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [24, 24, 1, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 121, "indent": 0, "parameters": [44, 44, 0]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 11, 0, 6, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 121, "indent": 0, "parameters": [161, 161, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 15, "y": 0}, {"id": 18, "name": "Valencia Event", "note": "<Hitbox Right: 1> <Hitbox Down: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 94, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>An apple?\\! What's this doing here?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>...And why is it gold?!"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Poison", "volume": 65, "pitch": 150, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*stomach growls*"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Urgh ~\\! I am pretty hungry right about now..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>And the kitchen's so far away!\\! Guess I don't have any other choice!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [14]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Damage4", "volume": 50, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Ow!\\! What is this thing made out of?!\\! Solid rock or something?!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I think I ~\\! *yawn*\\! Broke a tooth or ~\\! Something..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow3", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 42, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 94, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["Take a bite from the apple?"]}, {"code": 102, "indent": 0, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Yes"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 201, "indent": 1, "parameters": [0, 42, 0, 14, 8, 0]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "No"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 212, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["Would you like to go to the Valencian Garden?"]}, {"code": 102, "indent": 0, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Yes"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 201, "indent": 1, "parameters": [0, 42, 0, 14, 8, 0]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "No"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 94, "switch1Valid": true, "switch2Id": 214, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>I shouldn't get distracted right now."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 212, "switch1Valid": true, "switch2Id": 214, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>I shouldn't get distracted right now."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 4}, {"id": 19, "name": "Camp Zomi", "note": "<Hitbox Up: 3>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\"Book your ultimate summer vacation at Camp Zomi!\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'm not really interested in camping.\\! But the girl on the poster's hot!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 7}, {"id": 20, "name": "Sexy <PERSON>", "note": "<Hitbox Right: 3> <Hitbox Up: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I should replay Sexy Quest one of these days..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I really like that Cleric girl!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 5}, {"id": 21, "name": "Mom Bed Sex Events", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 145, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 118, "indent": 0, "parameters": ["Start"]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomBedRide1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Switch", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Switch"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 111, "indent": 3, "parameters": [0, 16, 0]}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 231, "indent": 5, "parameters": [21, "3xMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 231, "indent": 5, "parameters": [21, "3xMomBedRide1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 231, "indent": 5, "parameters": [21, "3zMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 231, "indent": 5, "parameters": [21, "3zMomBedRide1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [200, 200, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [0, 16, 0]}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 231, "indent": 5, "parameters": [21, "3xMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 231, "indent": 5, "parameters": [21, "3xMomBedMate1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 231, "indent": 5, "parameters": [21, "3zMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 231, "indent": 5, "parameters": [21, "3zMomBedMate1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [200, 200, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>Fuck me baby ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>Let \\n[11] take the lead..."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedMate1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedRide1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedMate1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedRide1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedMate4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedRide4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedMate4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedRide4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 111, "indent": 0, "parameters": [2, "B", 0]}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 7]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [2, "B", 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Finish1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 102, "indent": 0, "parameters": [["Another Round", "Finish"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Another Round"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 119, "indent": 1, "parameters": ["End"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Finish"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]><PERSON><PERSON>, I love you \\n[1] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 11, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 121, "indent": 0, "parameters": [200, 200, 1]}, {"code": 121, "indent": 0, "parameters": [145, 145, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 145, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 118, "indent": 0, "parameters": ["Start"]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedMate4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3zMomBedRide4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedMate4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [21, "3xMomBedRide4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [2, "D", 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>.\\| .\\| .\\| ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "C", 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>\\n[1]?!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>Ooh fuck!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "B", 0]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>How have you not have enough yet baby?!"]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>You're fucking \\n[11] like an animal ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>You can still keep going?!"]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>That youthful stamina is incredible ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Harder!", "Switch", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Harder!"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Switch"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 111, "indent": 3, "parameters": [0, 16, 0]}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 231, "indent": 5, "parameters": [21, "3xMomBedRide4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 231, "indent": 5, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 231, "indent": 5, "parameters": [21, "3zMomBedRide4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 231, "indent": 5, "parameters": [21, "3zMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [200, 200, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [0, 16, 0]}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 231, "indent": 5, "parameters": [21, "3xMomBedMate4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 231, "indent": 5, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 231, "indent": 5, "parameters": [21, "3zMomBedMate4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 231, "indent": 5, "parameters": [21, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [200, 200, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedMate4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedRide4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3xMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 122, "indent": 2, "parameters": [8, 8, 0, 0, 1]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedMate4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedRide4[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [21, "3zMomBedRide2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 122, "indent": 2, "parameters": [8, 8, 0, 0, 1]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 111, "indent": 0, "parameters": [2, "B", 0]}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 7]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3zMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3xMomBedRide3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [2, "B", 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Finish1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 102, "indent": 0, "parameters": [["Another Round", "Finish"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Another Round"]}, {"code": 111, "indent": 1, "parameters": [2, "C", 0]}, {"code": 123, "indent": 2, "parameters": ["D", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "B", 0]}, {"code": 123, "indent": 3, "parameters": ["C", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "A", 0]}, {"code": 123, "indent": 4, "parameters": ["B", 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 123, "indent": 4, "parameters": ["A", 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 119, "indent": 1, "parameters": ["Start"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Finish"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [2, "D", 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>What...\\| time...\\| is it ~ ♥️"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "C", 0]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>*pant* ~\\| *pant*"]}, {"code": 101, "indent": 3, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[23]\\N[2]>I love you baby ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [2, "B", 0]}, {"code": 101, "indent": 4, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 4, "parameters": ["\\n<\\c[23]\\N[2]>I can feel you still cumming inside \\n[11] ~ ♥️"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [2, "A", 0]}, {"code": 101, "indent": 5, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 5, "parameters": ["\\n<\\c[23]\\N[2]>You have so much energy \\n[1]!"]}, {"code": 101, "indent": 5, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 5, "parameters": ["\\n<\\c[23]\\N[2]>I love it ~ ♥️"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 101, "indent": 5, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 5, "parameters": ["\\n<\\c[23]\\N[2]>Did I give you a second wind baby?"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 11, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 121, "indent": 0, "parameters": [200, 200, 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 121, "indent": 0, "parameters": [145, 145, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 162, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [20, "3xMomBed_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3xMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 245, "indent": 0, "parameters": [{"name": "PixieDeepFast", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>That's it!\\! Ooohh, yes \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Don't stop!\\! Ngh - It feels so good!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Fuck me harder baby ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 140, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 140, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 101, "indent": 0, "parameters": ["", 10, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["                                                      Knock, knock ~ ♫"]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomCaught0[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 231, "indent": 0, "parameters": [23, "3zMomCaughtA[4x4]", 0, 0, 1000, 130, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Loop"]}, {"code": 231, "indent": 0, "parameters": [30, "3zMomCaughtDoor", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\n[3]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Don't pay me any attention.\\! I don't want to disturb two love birds in the middle "]}, {"code": 401, "indent": 0, "parameters": ["of the action..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>T-This isn't...\\! I can explain!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>*giggle*\\! We're well beyond the need for explanations, you know?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I...\\! I'm just showing \\n[2] some cool new wrestling moves!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Oh?\\! And what would you call this move?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>The Missionary Mayhem?\\! Or maybe the Penultimate Penetration?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Honestly, the two of you shouldn't end the fun just because I'm here."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Judging by the sound of your \\n[5]'s moans, you were really giving it to her!\\! A beautiful melody of passion!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Don't stop now \\n[1]!\\! Put on a show for your \\n[6]!"]}, {"code": 242, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [22, "3zMomCaught1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, false]}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\n[1]!\\! Wh-What are you?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [24, "3zMomCaughtB[4x4]", 0, 0, 1000, 130, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 24 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 24 Loop"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [23, 0, 0, 0, 1000, 130, 100, 100, 0, 0, 40, false]}, {"code": 232, "indent": 0, "parameters": [24, 0, 0, 0, 1000, 130, 100, 100, 255, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[Oh my!\\! He's actually doing it..!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[I didn't really expect him to just go at it again.\\! \\n[1] is fucking his own \\n[5] right in front of my eyes...]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [23, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 4"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, false]}, {"code": 232, "indent": 0, "parameters": [23, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 245, "indent": 0, "parameters": [{"name": "PixieDeepFast", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>B-Baby!\\! We sho-\\! N-Ngh!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>F-Forgive me \\n[3]!\\! F-Fuck!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Harder \\n[1]!\\! Mmm ~ Fuck \\n[11]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Oh, to catch a \\n[5] and \\n[8] in the heat of the moment!\\! My stomach is filled with butterflies!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[M-My!\\! I'm soaking wet just watching them...]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>[But I shouldn't rush things...\\! This is their moment ~ ♥️]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>I'm-\\! I'm going to cum baby!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>M-<PERSON> too \\n[2]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 231, "indent": 0, "parameters": [23, "3zMomBedMate3[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 231, "indent": 0, "parameters": [24, "3zMomCaughtC[4x4]", 0, 0, 1000, 130, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 24 Speed 8"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Finish2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>*giggle*\\! It's about time I make my exit...\\! <PERSON><PERSON><PERSON>, you two!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 130, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [24]}, {"code": 235, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\n[1]..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Yeah, \\n[2]?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Did we just..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Have sex in front of \\n[6] \\n[3]?\\! Yup..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>.\\| .\\| .\\| Oh dear."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Mom3[BUST][Exp4x3]')"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]\\bustExp[1, 4] \\^"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]This is terrible!\\! What are we going to do?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Well...\\! She already knows now.\\! Maybe we just ignore it?"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]As much as I'd like to pretend nothing happened, I don't think your \\n[6] will let this one go..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I don't know.\\! I guess maybe you could try talking to her?"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 1]And just how would I even explain that?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]\"Yes, \\n[3].\\! Now you know that I have sex with my \\n[8].\\! Surprise!\""]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hah hah!\\! Well, maybe that could be a funny way to break the ice!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]*sigh*\\! You're not helping mister..!"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]Listen honey.\\! There's something important I've been meaning to ask you..."]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]Are you and your \\n[6] having sex too..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>H-Huh?!\\! W-Why are you asking me that?"]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]It's just-\\! \\n[3] seems so coy about all of this.\\! Like she's been planning something."]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]Never mind.\\! It was wrong of me to ask you a personal question like that..."]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Maybe I just need to talk to her by myself."]}, {"code": 101, "indent": 0, "parameters": ["Mom3[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]However...\\! I should probably not approach her naked like this."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 130, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]*sigh*\\! Maybe I can find a way to fix this mess..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]I just hope she won't be mad with me."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Something tells me that won't be an issue."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]You really think so?\\! I really hope that's the case."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 4]I'm going to go get some space and clear my head for a little while..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Yeah.\\! Of course, \\n[2]."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [90, "8StatUp_MomAunt1", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 11, 0, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 12, 0, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [17, 17, 1, 0, 1]}, {"code": 122, "indent": 0, "parameters": [15, 15, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 121, "indent": 0, "parameters": [163, 163, 1]}, {"code": 121, "indent": 0, "parameters": [162, 162, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 16, "y": 0}, {"id": 22, "name": "Bulletin", "note": "<Hitbox Up: 3> <Hitbox Right: 5>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 31, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(5, \"$gameSwitches.value(198) == false\")"]}, {"code": 102, "indent": 0, "parameters": [["Profile", "Gallery", "Title Screen Image", "On/Off Auto Title", "Credits", "Cancel"], 5, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Profile"]}, {"code": 117, "indent": 1, "parameters": [44]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Gallery"]}, {"code": 356, "indent": 1, "parameters": ["picture_gallery"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Title Screen Image"]}, {"code": 111, "indent": 1, "parameters": [0, 198, 0]}, {"code": 117, "indent": 2, "parameters": [35]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [36]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "On/Off Auto Title"]}, {"code": 111, "indent": 1, "parameters": [0, 199, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 2, "parameters": ["The title screen is currently set to automatically change.\\! Disable title screen auto update?"]}, {"code": 102, "indent": 2, "parameters": [["Yes", "No"], 1, 0, 1, 0]}, {"code": 402, "indent": 2, "parameters": [0, "Yes"]}, {"code": 121, "indent": 3, "parameters": [199, 199, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "No"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 2, "parameters": ["The title screen is currently set to not update.\\! Enable title screen auto update?"]}, {"code": 102, "indent": 2, "parameters": [["Yes", "No"], 1, 0, 1, 0]}, {"code": 402, "indent": 2, "parameters": [0, "Yes"]}, {"code": 121, "indent": 3, "parameters": [199, 199, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "No"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "Credits"]}, {"code": 121, "indent": 1, "parameters": [196, 196, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 201, "indent": 1, "parameters": [0, 43, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [5, "Cancel"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 214, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>Now's really not the time for that."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 5}, null, null]}