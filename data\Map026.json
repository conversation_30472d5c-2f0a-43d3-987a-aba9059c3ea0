{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "Dining Room", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0Dining_Day", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Exit", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 2, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 13}, {"id": 2, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 62, 0]}, {"code": 231, "indent": 1, "parameters": [13, "Present1", 0, 0, 1076, 542, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 67, 0]}, {"code": 111, "indent": 1, "parameters": [0, 3, 0]}, {"code": 231, "indent": 2, "parameters": [14, "4Dining_EmilyGaming", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [14, "4Dining_Game1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 18, 1]}, {"code": 111, "indent": 1, "parameters": [0, 99, 1]}, {"code": 231, "indent": 2, "parameters": [16, "4D<PERSON>_<PERSON>ather", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 3, "name": "Backyard", "note": "<Hitbox Up: 8> <Hitbox Right: 4>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 23, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 35, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 10}, {"id": 4, "name": "<PERSON><PERSON><PERSON>", "note": "<Hitbox Up: 12> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 23, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 30, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 137, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 34, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 13}, {"id": 5, "name": "Game", "note": "<Hitbox Up: 1> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 67, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Solo Quest", "Leave"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Solo Quest"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>This game \\n[4] bought actually looks kinda fun!\\! Let's try one of these 'solo adventures' out."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [35, 35, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [16, 16, 1, 0, 1]}, {"code": 122, "indent": 1, "parameters": [33, 33, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [72, 72, 0]}, {"code": 121, "indent": 1, "parameters": [73, 73, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Key", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Chest2", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 117, "indent": 2, "parameters": [11]}, {"code": 201, "indent": 2, "parameters": [0, 27, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 67, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Solo Quest", "Leave"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Solo Quest"]}, {"code": 111, "indent": 1, "parameters": [0, 72, 1]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>A solo quest sounds fun right about now!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [35, 35, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [33, 33, 1, 0, 1]}, {"code": 121, "indent": 2, "parameters": [72, 72, 0]}, {"code": 121, "indent": 2, "parameters": [73, 73, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 250, "indent": 2, "parameters": [{"name": "Key", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 250, "indent": 2, "parameters": [{"name": "Chest2", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [8]}, {"code": 111, "indent": 2, "parameters": [0, 4, 0]}, {"code": 117, "indent": 3, "parameters": [11]}, {"code": 201, "indent": 3, "parameters": [0, 27, 0, 14, 0, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\n[1]>I've already played a solo quest today.\\! I think I need a break..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 8}, {"id": 6, "name": "Present", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 62, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 108, "indent": 0, "parameters": ["Old Present X value = 847"]}, {"code": 232, "indent": 0, "parameters": [13, 0, 0, 0, 1076, 542, 100, 100, 0, 0, 40, true]}, {"code": 235, "indent": 0, "parameters": [13]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 111, "indent": 0, "parameters": [1, 31, 0, 0, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["Be sure to leave me some cookies for me tonight..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["I'll be sure to bring the milk ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 31, 0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["Have you been naughty or nice this year?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["This year, I suppose either one might excite me ~ ♥️"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 31, 0, 2, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 3, "parameters": ["I brought several gifts for you.\\! I can't wait to stuff them into your stocking!"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 3, "parameters": ["But you didn't forget about me right?\\! My stocking needs stuffed too ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [1, 31, 0, 0, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Erhm- Thanks <PERSON>?\\! I guess I am sort of thirsty right now..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 31, 0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Really?!\\! No coal this year?\\! Sweet!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 31, 0, 2, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Well I- I didn't think to get Santa a...\\| a gift..."]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>*yawn*\\! Man, I'm...\\! So sleepy..."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [31, 31, 1, 0, 1]}, {"code": 111, "indent": 0, "parameters": [1, 31, 0, 3, 0]}, {"code": 111, "indent": 1, "parameters": [0, 199, 1]}, {"code": 356, "indent": 2, "parameters": ["ChangeTitle1 Title_Holiday"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 201, "indent": 1, "parameters": [0, 25, 0, 14, 8, 0]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [62, 62, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 12}, {"id": 7, "name": "Sis Gaming", "note": "<Hitbox Up: 5> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 67, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [14]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [14, "4Dining_EmilyGaming", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 67, "switch2Valid": true, "variableId": 16, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Adventure", "Leave"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 235, "indent": 1, "parameters": [14]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [17]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [14, "4Dining_EmilyGaming", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Adventure"]}, {"code": 111, "indent": 1, "parameters": [0, 69, 1]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>I hope you're ready!\\! This adventure is going to be totally action packed!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>Awesome!\\! Let's pick back up from where we last stopped..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [7]}, {"code": 201, "indent": 1, "parameters": [0, 28, 13, 14, 8, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["MC", 2], "indent": null}]}, {"code": 211, "indent": 1, "parameters": [1]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [0, 69, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 75, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 111, "indent": 0, "parameters": [1, 16, 0, 12, 0]}, {"code": 235, "indent": 1, "parameters": [14]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [17]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [14, "4Dining_EmilyGaming", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["Talk", "<PERSON> Queen (FJ)", "Leave"], 2, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "Talk"]}, {"code": 235, "indent": 2, "parameters": [14]}, {"code": 235, "indent": 2, "parameters": [15]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 117, "indent": 2, "parameters": [17]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 231, "indent": 2, "parameters": [14, "4Dining_EmilyGaming", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "<PERSON> Queen (FJ)"]}, {"code": 235, "indent": 2, "parameters": [15]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>You wish to visit the Evil Queen's throne room again?"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>*fu fu*\\! You have chosen danger, I see..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [11]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 8]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Leave"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 117, "switch2Valid": true, "variableId": 16, "variableValid": false, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Begin Adventure", "Leave"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 235, "indent": 1, "parameters": [14]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [17]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [14, "4Dining_EmilyGaming", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Begin Adventure"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>Okay!\\! It's time to begin our descent into a dark and mysterious tomb!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [7]}, {"code": 201, "indent": 1, "parameters": [0, 29, 14, 10, 2, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC1", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["MC1", 4], "indent": null}]}, {"code": 211, "indent": 1, "parameters": [0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 110, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Queen's Treasure (Sex)", "<PERSON> Queen (FJ)", "Leave"], 3, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 235, "indent": 1, "parameters": [14]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [17]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [14, "4Dining_EmilyGaming", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Queen's Treasure (Sex)"]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>Another trip to the Royal Queen's Private Chambers..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>You've become quite the treasure hunter..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 13]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON> Queen (FJ)"]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>You wish to visit the Evil Queen's throne room again?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>*fu fu*\\! You have chosen danger, I see..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 8]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 11}, {"id": 8, "name": "Post Quest Cutscene", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 74, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]This next part of the quest can't continue here."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[4]! What are you talking about?!\\! I wanted to see the Evil Queen..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]Oh but you will...\\! In her throne room."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]I think the best place for this will be in my room.\\! Let's go quietly!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Quietly..?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 6]SHH!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]The Evil Queen's castle is full of sensitive hearing demons.\\! We must go quietly!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 284, "indent": 0, "parameters": ["0EmilyBR_Day1", false, false, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]We've made it to the throne room undetected."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]Now lie down and close your eyes!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Close my eyes?\\! Just what do you have planned, \\n[4]?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]You're in the throne room.\\! There is no \\n[4], just the Evil Queen..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 8]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 116, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Mom1[BUST][Exp4x3]')"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 0]It makes me so happy seeing the two of you getting along so well!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 3]That's great and all \\n[2], but you're kind of..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 1]Oh!\\! You two are still in the middle of your little game!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[2, 3]I'll leave you two alone so you can continue your adventures!\\! *giggle*"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 3]Thanks \\n[2]..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).<PERSON><PERSON><PERSON>(-600, 0, 65)"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).<PERSON><PERSON><PERSON>(-250, 0, 60)"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 0]I'm so glad we were able to beat that boss together."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 2]I hope you had fun...\\! It's a bit tough to get the attacks just right and the-"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Had fun?\\! I had a blast \\n[4]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 6]Y-You did..?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Of course!\\! I may not be very good at table top games, but I could tell you put a lot into designing this adventure."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>We should keep playing new adventures this summer!\\! I'd like that."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 3]\\n[1]...\\! No one's ever told me they've enjoyed my games before..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 8]It really means a lot to me that you said that, you know?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Well I meant it!\\! I'm looking forward to the next adventure."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 3]W-Well, who's to say this adventure is already over?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 3]You have to go check on the kingdom that you just saved, don't you?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>That's right!\\! The Queen's reward!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I wonder what sort of reward she has in mind?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bustExp[3, 8]Well then, let's go find out.\\! The Queen would like to speak to you in her private chambers."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(2).clear()"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 284, "indent": 0, "parameters": ["0EmilyBR_Day1", false, false, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]The Queen's Private Chambers..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]You should rest on the Queen's bed and close your eyes again..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]The Queen will visit your shortly..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 13]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 9, "name": "<PERSON><PERSON> Duster", "note": "<Hitbox Up: 1> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 159, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 15, "variableValid": true, "variableValue": 18}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "choice_confirm_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [16]}, {"code": 111, "indent": 0, "parameters": [0, 199, 1]}, {"code": 356, "indent": 1, "parameters": ["ChangeTitle1 Title_Maid"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>A feather duster?\\! Did \\n[2] leave this here?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Usually she just cleans with a rag or something...\\! Weird."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["You've unlocked the special Maid Outfit for \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["During the Morning, you can ask her to change into it while she's cleaning!"]}, {"code": 121, "indent": 0, "parameters": [99, 99, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 99, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 7}, null]}