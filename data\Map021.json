{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "<PERSON>'s Bedroom", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0EmilyBR_Day1", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Exit", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 7, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 13}, {"id": 2, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 135, 0]}, {"code": 231, "indent": 1, "parameters": [13, "4SisterBR_EasterEggs", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 108, "indent": 0, "parameters": ["Old Present Coordinates - 847,502"]}, {"code": 111, "indent": 0, "parameters": [0, 3, 0]}, {"code": 111, "indent": 1, "parameters": [0, 67, 1]}, {"code": 231, "indent": 2, "parameters": [10, "4SisterBR_EmilyGame1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 1, 0]}, {"code": 111, "indent": 2, "parameters": [0, 137, 1]}, {"code": 111, "indent": 3, "parameters": [1, 16, 0, 2, 1]}, {"code": 231, "indent": 4, "parameters": [10, "4SisterBR_EmilyChanging1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 231, "indent": 4, "parameters": [10, "4SisterBR_EmilyGame1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 70, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "4SisterBR_EmilyChanging1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 121, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 27, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Sis2[BUST][Exp3x3]')"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]\\bustExp[1, 7] \\^"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [90, "8StatUp_Sis3", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]We really did it, huh?"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]I really just fucked my little \\n[10]!\\! Hah hah!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'm just as surprised as you are..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>But it felt incredible..!"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]You can say that again!\\! Your dick is incredible \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>So does that mean we can do this again sometime?"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]Of course!\\! At this point, I'm going to become addicted to fucking you, you know?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]But I also wanted to just say...\\! Th-Thanks for being cool and stuff..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>What?\\! Are you getting all soft on me \\n[4]?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]Of course not, you dork!"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]I've just...\\! Never really felt this close to someone before."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Of course.\\! You're the best big \\n[7] ever!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I love you \\n[4]!"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]! ! !"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]I-\\! I love you too..."]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]Dork ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [3, 3, 1]}, {"code": 121, "indent": 0, "parameters": [4, 4, 0]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 117, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [19, 19, 0]}, {"code": 121, "indent": 0, "parameters": [122, 122, 0]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 8, 8, 0]}, {"code": 122, "indent": 0, "parameters": [32, 32, 0, 0, 3]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 0, 14]}, {"code": 121, "indent": 0, "parameters": [110, 110, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 121, "indent": 0, "parameters": [121, 121, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 172, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 27, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 199, 1]}, {"code": 356, "indent": 1, "parameters": ["ChangeTitle1 Title_Pajamas"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [171, 171, 1]}, {"code": 122, "indent": 0, "parameters": [48, 48, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]He's still asleep..."]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]Hey \\n[1].\\! Wake up..!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Snooo<PERSON>e ~ \\! Mmmmm ~ "]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Hellooo ~ ?\\! Wake up dude!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Princess ~\\! Rescue ~\\! *snooore*"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]WAKE UP!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>OW!\\! What happened?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Gee<PERSON>, you're a heavy sleeper..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*yaaawn*\\! \\n[4]..?"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]You know you snore right?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]And you kicked me in the back once!\\! What's up with that?!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I did?!\\! Hah hah, sorry \\n[4]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I was out like a rock!\\! I slept great!"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]You did, huh?\\! At least I was able to help you fall asleep, I guess..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>So does that mean I can sleep with you again tonight?"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]Tonight?!\\! Hey, now wait a minute..!"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]*hmph*\\! . . . I'll think about it."]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]Now get going before \\n[2] catches you in here!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [32, 32, 0, 0, 4]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 0, 22]}, {"code": 121, "indent": 0, "parameters": [172, 172, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 178, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 27, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [1, "4SisterBR_Sleep1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 90, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]*yaaawn* ~\\! Do we have to get up..?"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]I wish I could always sleep in!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [178, 178, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 179, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 27, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [1, "4SisterBR_Sleep3", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 90, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]*yaaawn* ~\\! What a night..."]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]You really fucked me hard too...\\! I'm all sore now!"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]I'm going to expect you to pound me like that every time, you know?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [179, 179, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 176, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 27, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [1, "6Closeup_EmilyCaught1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "6Closeup_EmilyCaught2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 101, "indent": 0, "parameters": ["", 10, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["                                                 Good morning, \\n[4] ~ ♫"]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh my goodness!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 90, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>How precious!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [10, "4SisterBR_Sleep1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [11, "4SisterBR_Sleep2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>I can't believe \\n[1] and \\n[4] fell asleep together!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh ~ it just warms my heart to see how well they've been getting along this summer!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>I almost don't want to wake them!\\! Maybe I shouldn't..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>But letting them sleep in wouldn't be good for them either.\\! Oh, I guess it can't be helped."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\n[4],\\! \\n[1] ~ !\\! Wakey wakey ~ ♫"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [10, "4SisterBR_Sleep3", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [11, "4SisterBR_Sleep4", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [12, "4SisterBR_Sleep5", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 101, "indent": 0, "parameters": ["", 10, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["                                                              *GASP*"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>OH MY GOD!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 255, 0, 90, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 90, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\N[2]?!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[2]!\\! What are you-"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>H-Hang on \\n[2]!\\! This isn't what it looks like!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Close1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Sis4[BUST][Exp3x3]')"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]\\bustExp[1, 6] \\^"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 6]Dude.\\! Please tell me we're still dreaming right?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]WE'RE DREAMING RIGHT?!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Y'know \\n[4], I don't think we're dreaming."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Pretty sure we're busted..."]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 6]How are you not freaking out right now?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 6]\\n[2] just walked in.\\! And she saw us naked..."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>What if we tell her it was too hot while we were trying to sleep?"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 6]\\n[1].\\! Are you kidding?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Well, I know it's a stretch but I-"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]I STILL HAVE YOUR CUM ON ME!"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'll ~\\! tell her I had a wet dream!"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]*grr*\\! You're no help!"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]Ohh, what do I do?!\\! This is mortifying..!"]}, {"code": 101, "indent": 0, "parameters": ["Sis4[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]Just get out.\\! I need to think about this..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [90, "8StatUp_SisMom1", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 108, "indent": 0, "parameters": ["== Old Placeholder =="]}, {"code": 111, "indent": 0, "parameters": [1, 16, 0, 99, 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["\\n[4] and \\n[2]'s storyline will be continued soon in a future update!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [16, 16, 1, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 7, 0, 14, 0, 0]}, {"code": 121, "indent": 0, "parameters": [180, 180, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 215, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [1, "4SisterBR_Sleep1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 90, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]*yaaawn* ~\\! Good morning..."]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Weird.\\! I don't remember tucking myself in to sleep..."]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]Did \\n[2] do that?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*yawn* ~\\! I guess she did."]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]*giggle*\\! She's still the same old \\n[5], huh?"]}, {"code": 101, "indent": 0, "parameters": ["Sis5[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]Come on \\n[1].\\! Let's get up."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [178, 178, 1]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 0, "parameters": ["Congratulations on achieving a threesome with both \\n[2] and \\n[4]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 0, "parameters": ["Continue building relationships around the house to unlock new events and interactions!"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 121, "indent": 0, "parameters": [216, 216, 0]}, {"code": 121, "indent": 0, "parameters": [215, 215, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 3, "name": "Sis Gaming", "note": "<Hitbox Up: 6> <Hitbox Right: 3>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [10, "4SisterBR_EmilyGame1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 16, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [10, "4SisterBR_EmilyGame1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 67, "switch2Valid": true, "variableId": 33, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 12}, {"id": 4, "name": "Goddesses", "note": "<Hitbox Up: 1> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's a statue of two beautiful ladies."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>They almost look like they could be Goddesses!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 10}, {"id": 5, "name": "<PERSON><PERSON>", "note": "<Hitbox Up: 9> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 16, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 231, "indent": 0, "parameters": [11, "4SisterBR_EmilyChanging2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [12, "4SisterBR_EmilyChanging3", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 241, "indent": 0, "parameters": [{"name": "Deliciously Sour", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\n[1]!?\\! I'm getting changed your perv!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Sorry \\n[4]!\\! It was an honest mistake, I swear!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Well then why haven't you left?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Huh..?\\! Wait a sec..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Do you have a BONER?!\\! Oh my gosh you are such a pervert, \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It was an accident!\\! Please don't tell \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Heh.\\! And ruin all the fun?!\\! Now why would I do that?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [0, 11, 0]}, {"code": 231, "indent": 1, "parameters": [20, "lqEmilyTease2[2x4]", 0, 0, 150, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [20, "5EmilyTease2[4x4]", 0, 0, 150, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 150, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I would be the meanest \\n[7] if I left my little \\n[10] hanging, now wouldn't I?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>This makes you feel better, doesn't it?!\\! You just wanted a peek at your \\n[7]'s giant tits, huh?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>H-Hey...\\! You don't have to rub it in my face..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Oh, I'm sure you'd LOVE for me to rub them in your face, wouldn't you?!\\! Hah hah!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>That's enough eye candy for you today.\\! Get out before I call \\n[2]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Sis2[BUST][Exp3x3]')"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 6]I feel like my eyes are playing tricks on me!"]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]\\n[1]'s cock was absolutely bulging when I saw him!\\! His pants were about to burst..."]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]No way a shrimp like him is really packing THAT much down there..."]}, {"code": 101, "indent": 0, "parameters": ["Sis2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]Maybe this summer won't be as boring as I thought..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [73, 73, 0]}, {"code": 122, "indent": 0, "parameters": [35, 35, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [16, 16, 1, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 7, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(3, \"$gameSwitches.value(75) === false\")"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(4, \"$gameSwitches.value(110) === false\")"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Stare", "<PERSON><PERSON><PERSON>", "Sex", "Leave"], 4, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [17]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Stare"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([21,11,'C'], true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON><PERSON><PERSON>"]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]You want me to use my feet again?!"]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]You're such a weirdo...\\! Fine!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 14]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Sex"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["Choose how \\n[4] is clothed."]}, {"code": 102, "indent": 1, "parameters": [["Clothed", "<PERSON><PERSON>"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Clothed"]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "<PERSON><PERSON>"]}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]I've been looking forward to riding you again."]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]Come on!\\! What are you waiting for?!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 16]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 137, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 13}, {"id": 6, "name": "Poster", "note": "<Hitbox Up: 3>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>The lady on this poster is so pretty!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hmm ~\\! Her smile reminds me a lot of \\n[2]..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 6}, {"id": 7, "name": "ToP", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Whoa, that's a scary looking knight!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>But she does have really big boobs."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 5}, {"id": 8, "name": "ToP", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hey look, he's short just like me."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'm not sure who this guy is supposed to be, but I bet he's cool!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 5}, {"id": 9, "name": "Spring Complete Scene", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 135, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>*yawn*\\! Ugh... What happened?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Did I fall asleep on \\n[4]'s bed..?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Where did all of her decorations go?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]Man, you really worked up a sweat didn't you?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]You pounded me so hard and then passed out like a light!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>\\n[4]?\\! What happened to your outfit?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]I can't wear that outfit around the house all day!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]\\n[2] and \\n[6] \\n[3] would have a lot of questions about that..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]But don't worry, I still have the costume in my closet."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]That way we can still have some spring-themed fun from time to time."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["You can now revisit \\n[4]'s Spring Bedroom by interacting "]}, {"code": 401, "indent": 0, "parameters": ["with the Easter Eggs on her closet!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Happy Spring!"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 10, "name": "Spring Room", "note": "<Hitbox Right: 3> <Hitbox Up: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 135, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 33, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 2}, {"id": 11, "name": "<PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','5EmilyTease1[4x4]');"]}, {"code": 655, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','5EmilyTease1a[4x4]');"]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 231, "indent": 0, "parameters": [11, "4SisterBR_EmilyChanging2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [12, "4SisterBR_EmilyChanging3", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>H-Hey..!\\! You're staring again..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Fine.\\! If it's a show you want, here!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [20, "5EmilyTease2[4x4]", 0, 0, 150, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [21, "5EmilyTease2a[4x4]", 0, 0, 150, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 150, 0, 100, 100, 255, 0, 120, false]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Hmm ~\\! How's this..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>What's the matter?!\\! Cat got your tongue?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Or are you just a dirty perv who loves to look at his older \\n[7]'s tits?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 102, "indent": 1, "parameters": [["Continue", "Glasses", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Continue"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Glasses"]}, {"code": 111, "indent": 2, "parameters": [0, 16, 0]}, {"code": 232, "indent": 3, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 255, 0, 45, true]}, {"code": 121, "indent": 3, "parameters": [16, 16, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 232, "indent": 3, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 0, 0, 45, true]}, {"code": 121, "indent": 3, "parameters": [16, 16, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [20, "5EmilyTease1[4x4]", 0, 0, 150, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 16, 0]}, {"code": 231, "indent": 1, "parameters": [21, "5EmilyTease1a[4x4]", 0, 0, 150, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "5EmilyTease1a[4x4]", 0, 0, 150, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I bet you wanna stick your cock right in between them.\\! Just like this."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Feeling my tits rub back and forth.\\! Mmmh ~\\! So dirty!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>But this is all you get for now!\\! Only a peek, you weirdo!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(110) == true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(110) == false\")"]}, {"code": 102, "indent": 1, "parameters": [["Finish", "Glasses", "???", "More ♥️", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Finish"]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 232, "indent": 2, "parameters": [20, 0, 0, 0, 150, 0, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 2, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 0, 0, 60, true]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]There.\\! You got what you came here to see."]}, {"code": 101, "indent": 2, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]Now run along squirt!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 117, "indent": 2, "parameters": [11]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 123, "indent": 2, "parameters": ["C", 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Glasses"]}, {"code": 111, "indent": 2, "parameters": [0, 16, 0]}, {"code": 232, "indent": 3, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 255, 0, 45, true]}, {"code": 121, "indent": 3, "parameters": [16, 16, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 232, "indent": 3, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 0, 0, 45, true]}, {"code": 121, "indent": 3, "parameters": [16, 16, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "???"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Cancel1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>Nope!\\! Don't get any ideas."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>This is all you're getting!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "More ♥️"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>I should've known that just a peek wouldn't be enough for you..."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>Consider yourself lucky.\\! I'll let you have a little more fun ~ ♥️"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 117, "indent": 2, "parameters": [11]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 121, "indent": 2, "parameters": [96, 96, 0]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 16]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 11, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','lqEmilyTease1[2x4]');"]}, {"code": 655, "indent": 0, "parameters": ["Galv.CACHE.load('pictures','lqEmilyTease2[2x4]');"]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 231, "indent": 0, "parameters": [11, "4SisterBR_EmilyChanging2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [12, "4SisterBR_EmilyChanging3", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>H-Hey..!\\! You're staring again..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Fine.\\! If it's a show you want, here!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [20, "lqEmilyTease2[2x4]", 0, 0, 150, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [21, "lqEmilyTease2a[2x4]", 0, 0, 150, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 150, 0, 100, 100, 255, 0, 120, false]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Hmm ~\\! How's this..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>What's the matter?!\\! Cat got your tongue?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Or are you just a dirty perv who loves to look at his older \\n[7]'s tits?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 102, "indent": 1, "parameters": [["Continue", "Glasses", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Continue"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Glasses"]}, {"code": 111, "indent": 2, "parameters": [0, 16, 0]}, {"code": 232, "indent": 3, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 255, 0, 45, true]}, {"code": 121, "indent": 3, "parameters": [16, 16, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 232, "indent": 3, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 0, 0, 45, true]}, {"code": 121, "indent": 3, "parameters": [16, 16, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 231, "indent": 0, "parameters": [20, "lqEmilyTease1[2x4]", 0, 0, 150, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 16, 0]}, {"code": 231, "indent": 1, "parameters": [21, "lqEmilyTease1a[2x4]", 0, 0, 150, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "lqEmilyTease1a[2x4]", 0, 0, 150, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I bet you wanna stick your cock right in between them.\\! Just like this."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Feeling my tits rub back and forth.\\! Mmmh ~\\! So dirty!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>But this is all you get for now!\\! Only a peek, you weirdo!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(110) == true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(4, \"$gameSwitches.value(110) == false\")"]}, {"code": 102, "indent": 1, "parameters": [["Finish", "Glasses", "???", "More ♥️", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Finish"]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 232, "indent": 2, "parameters": [20, 0, 0, 0, 150, 0, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 2, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 0, 0, 60, true]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]There.\\! You got what you came here to see."]}, {"code": 101, "indent": 2, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]Now run along squirt!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 117, "indent": 2, "parameters": [11]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 121, "indent": 2, "parameters": [20, 20, 1]}, {"code": 123, "indent": 2, "parameters": ["C", 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Glasses"]}, {"code": 111, "indent": 2, "parameters": [0, 16, 0]}, {"code": 232, "indent": 3, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 255, 0, 45, true]}, {"code": 121, "indent": 3, "parameters": [16, 16, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 232, "indent": 3, "parameters": [21, 0, 0, 0, 150, 0, 100, 100, 0, 0, 45, true]}, {"code": 121, "indent": 3, "parameters": [16, 16, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "???"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Cancel1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>Nope!\\! Don't get any ideas."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>This is all you're getting!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "More ♥️"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>I should've known that just a peek wouldn't be enough for you..."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>Consider yourself lucky.\\! I'll let you have a little more fun ~ ♥️"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 117, "indent": 2, "parameters": [11]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 121, "indent": 2, "parameters": [96, 96, 0]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 16]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 26, "y": 0}]}