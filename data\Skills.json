[null, {"id": 1, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 4 - b.def * 2", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 1, "iconIndex": 76, "message1": " attacks!", "message2": "", "mpCost": 0, "name": "Attack", "note": "Skill #1 will be used when you select\nthe Attack command.", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 2, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 2, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 81, "message1": " guards.", "message2": "", "mpCost": 0, "name": "Guard", "note": "Skill #2 will be used when you select\nthe Guard command.", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 2000, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 3, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 4 - b.def * 2", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 1, "iconIndex": 76, "message1": " attacks!", "message2": "", "mpCost": 0, "name": "Dual Attack", "note": "", "occasion": 1, "repeats": 2, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 5}, {"id": 4, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 4 - b.def * 2", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 1, "iconIndex": 76, "message1": " attacks!", "message2": "", "mpCost": 0, "name": "Double Attack", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 4, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 5}, {"id": 5, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 4 - b.def * 2", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 1, "iconIndex": 76, "message1": " attacks!", "message2": "", "mpCost": 0, "name": "Triple Attack", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 5, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 4}, {"id": 6, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 41, "dataId": 0, "value1": 0, "value2": 0}], "hitType": 0, "iconIndex": 82, "message1": " flees.", "message2": "", "mpCost": 0, "name": "Escape", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 7, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 81, "message1": " waits.", "message2": "", "mpCost": 0, "name": "Wait", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 0, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 8, "animationId": 41, "damage": {"critical": false, "elementId": 0, "formula": "200 + a.mat", "type": 3, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 72, "message1": " casts %1!", "message2": "", "mpCost": 5, "name": "Heal", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 9, "animationId": 66, "damage": {"critical": false, "elementId": 2, "formula": "100 + a.mat * 2 - b.mdf * 2", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 2, "iconIndex": 64, "message1": " casts %1!", "message2": "", "mpCost": 5, "name": "Fire", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 10, "animationId": 78, "damage": {"critical": false, "elementId": 4, "formula": "100 + a.mat * 2 - b.mdf * 2", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 2, "iconIndex": 66, "message1": " casts %1!", "message2": "", "mpCost": 5, "name": "Spark", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 10}]