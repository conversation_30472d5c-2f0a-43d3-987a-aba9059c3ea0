{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 4, "width": 27, "data": [6684, 6684, 6694, 7283, 7282, 7286, 1572, 1572, 1572, 1572, 1572, 4128, 4112, 4112, 4112, 4136, 1572, 1572, 1572, 1572, 1572, 7283, 7282, 7286, 6696, 6684, 6684, 7283, 7282, 7286, 7281, 7280, 7284, 1572, 1572, 1572, 1572, 1572, 4128, 4112, 4112, 4112, 4136, 1572, 1572, 1572, 1572, 1572, 7281, 7280, 7284, 7283, 7282, 7282, 7281, 7280, 7284, 7289, 7288, 7292, 1572, 1572, 1572, 1572, 1572, 4152, 4140, 4140, 4140, 4150, 1572, 1572, 1572, 1572, 1572, 7289, 7288, 7292, 7281, 7280, 7280, 7289, 7288, 7292, 2048, 2048, 2048, 7243, 7242, 7242, 7242, 7246, 1608, 1609, 1609, 1609, 1610, 7243, 7242, 7242, 7242, 7246, 2048, 2048, 2048, 7289, 7288, 7288, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1608, 1609, 1609, 1609, 1610, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 2048, 2048, 2048, 2048, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2227, 2237, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2226, 2228, 0, 0, 0, 2238, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2232, 2230, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Sis Guide", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 69, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]What's up?"]}, {"code": 102, "indent": 0, "parameters": [["R<PERSON>", "Hint", "Leave Game", "Nevermind"], 3, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "R<PERSON>"]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]\"I once read of a tale of a wandering soldier...\""]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]\"He steps foot into the mysterious kingdom of Goo Rine...\""]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]\"When he stubs his toe only to yell, ow!\""]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]\"The pain left him feeling blue with defeat...\""]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Hint"]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]There are four orbs that can change colors..."]}, {"code": 101, "indent": 1, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Starting from left to right, maybe the riddle has colors hidden within the message."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave Game"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 26, 0, 14, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["MC", 1], "indent": null}]}, {"code": 211, "indent": 1, "parameters": [0]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Nevermind"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 10}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 1}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 1}, {"id": 4, "name": "Boss", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$BigMonster2", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 8}, {"id": 5, "name": "Evil Queen", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 5}, {"id": 6, "name": "Cutscene", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "Battle3", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]The hero makes his way to the end of the Evil Queen's dungeon."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Only to find the Evil Queen herself!\\! And her treacherous pet!"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Collapse4", "volume": 35, "pitch": 150, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [4, 5, true]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\bust[1]\\bustExp[1, 4]Treacherous pet?!\\! I don't have any weapons and my combat is level 1 \\n[4]..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\bust[1]\\bustExp[1, 4]Can't we do like a puzzle or something instead to reach the Evil Queen..?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Ugh fine.\\! Give me just a moment..."]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [6], "indent": null}, {"code": 13, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [68, 68, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Book2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 250, "indent": 0, "parameters": [{"name": "Magic4", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]The hero makes his way to the end of the Evil Queen's dungeon."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Only to find a-\\| puzzle between him and the Evil Queen..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]It is here that the wise words of the Oracle come back to our hero..."]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\bust[1]\\bustExp[1, 2]Uhh, \\n[4].\\! I kind of forgot what the <PERSON> had told me..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]WHAT THE HELL DO YOU MEAN YOU FORGOT?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\bust[1]\\bustExp[1, 5]... Sorry.\\! I kind of spaced out during that part."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]*sigh*"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]Desperate to find the solution, another mysterious Oracle appears before him."]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 212, "indent": 0, "parameters": [1, 36, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 121, "indent": 0, "parameters": [69, 69, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]The beautiful and mysterious Oracle begins to recite the magic riddle..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]\"I once read of a tale of a wandering soldier...\""]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]\"He steps foot into the mysterious kingdom of Goo Rine...\""]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]\"When he stubs his toe only to yell, ow!\""]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]\"The pain left him feeling blue with defeat...\""]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\bust[1]\\bustExp[1, 3]What kind of story is that, \\n[4]..?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Would you just shut it and play along?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]I have to improvise a bit since you didn't want to fight the boss I designed."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]I spent so much time working on the boss fight patterns too..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\bust[1]\\bustExp[1, 2]Alright, alright...\\! I'll solve the riddle.\\! Just stop looking so sad."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Excellent!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]Don't take too long, <PERSON>.\\! The Evil Queen awaits..."]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 12}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 6, "pattern": 2, "characterIndex": 7}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 41, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 41, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 41, "variableValid": true, "variableValue": 3}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 41, "variableValid": true, "variableValue": 4}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 6, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 8}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 6, "pattern": 2, "characterIndex": 7}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 44, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 44, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 44, "variableValid": true, "variableValue": 3}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 44, "variableValid": true, "variableValue": 4}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 6, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [44, 44, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 8}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 6, "pattern": 2, "characterIndex": 7}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 43, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 43, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 43, "variableValid": true, "variableValue": 3}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 43, "variableValid": true, "variableValue": 4}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 6, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 7}, {"id": 10, "name": "Puzzle Tracker", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 41, 0, 1, 0]}, {"code": 111, "indent": 1, "parameters": [1, 42, 0, 4, 0]}, {"code": 111, "indent": 2, "parameters": [1, 43, 0, 3, 0]}, {"code": 111, "indent": 3, "parameters": [1, 44, 0, 2, 0]}, {"code": 123, "indent": 4, "parameters": ["A", 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 41, 0, 4, 0]}, {"code": 111, "indent": 2, "parameters": [1, 42, 0, 1, 0]}, {"code": 111, "indent": 3, "parameters": [1, 43, 0, 3, 0]}, {"code": 111, "indent": 4, "parameters": [1, 44, 0, 2, 0]}, {"code": 123, "indent": 5, "parameters": ["A", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]By the Goddess!\\! The hero successfully cleared the puzzle!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hah! Take that Evil Queen!\\! You can't run away from me any longer!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]Oh don't worry...\\! The Evil Queen never flees from conflict."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 8]She just prefers to play with her food first..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]With a loud \"whoosh\", the magic barrier dissipated and the path to the Evil Queen was open once again."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]The slightly fearful yet smart hero carried onward."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Magic2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["MC", 1], "indent": null}]}, {"code": 211, "indent": 0, "parameters": [0]}, {"code": 201, "indent": 0, "parameters": [0, 26, 0, 14, 0, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [74, 74, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 11}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 6, "pattern": 2, "characterIndex": 7}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 42, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 42, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 42, "variableValid": true, "variableValue": 3}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 42, "variableValid": true, "variableValue": 4}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other1", "direction": 6, "pattern": 1, "characterIndex": 6}, "list": [{"code": 102, "indent": 0, "parameters": [["Red", "Blue", "Yellow", "Green"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Red"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Blue"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Yellow"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Green"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 7}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 6, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 6}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 6, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 6}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 3}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 3}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 3}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 3}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 68, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 3}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 2}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 2}]}