{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 4, "width": 27, "data": [6900, 6924, 6924, 6924, 6934, 2289, 2288, 2290, 6936, 6924, 6934, 7283, 7282, 7282, 7282, 7286, 6936, 6924, 6934, 2289, 2288, 2290, 6936, 6924, 6924, 6924, 6904, 6920, 7282, 7282, 7282, 7287, 2289, 2288, 2290, 7283, 7282, 7286, 7289, 7288, 7288, 7288, 7292, 7283, 7282, 7286, 2289, 2288, 2290, 7287, 7282, 7282, 7282, 6912, 6920, 7284, 7288, 7288, 7285, 2280, 2268, 2278, 7281, 7280, 7284, 1571, 1571, 1571, 1571, 1571, 7281, 7280, 7284, 2280, 2268, 2278, 7285, 7288, 7288, 7281, 6912, 6920, 7292, 1571, 1571, 7293, 1571, 1571, 1571, 7289, 7288, 7292, 1571, 1571, 1571, 1571, 1571, 7289, 7288, 7292, 1571, 1571, 1571, 7293, 1571, 1571, 7289, 6912, 6920, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 6912, 6920, 1571, 1571, 1571, 1635, 1571, 1571, 1571, 1571, 3762, 3748, 3748, 3748, 3748, 3748, 3748, 3748, 3764, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 6912, 6898, 6932, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 3744, 3728, 3728, 3728, 3728, 3728, 3728, 3728, 3752, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 6930, 6897, 6896, 6920, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 3744, 3728, 3728, 3728, 3728, 3728, 3728, 3728, 3752, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 6912, 6896, 6900, 6934, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 3744, 3728, 3728, 3728, 3728, 3728, 3728, 3728, 3752, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 6936, 6904, 6920, 7286, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 3744, 3728, 3728, 3728, 3728, 3728, 3728, 3728, 3752, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 7283, 6912, 6920, 7284, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 3768, 3756, 3756, 3736, 3728, 3732, 3756, 3756, 3766, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 7281, 6912, 6920, 7292, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 3744, 3728, 3752, 1571, 1571, 1571, 1571, 1635, 1571, 1571, 1571, 1571, 1571, 7289, 6912, 6920, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 3744, 3728, 3752, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 6912, 6920, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 3744, 3728, 3752, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 1571, 6912, 6898, 6916, 6916, 6916, 6916, 6916, 6932, 1571, 1571, 1571, 1571, 1571, 3744, 3728, 3752, 1571, 1571, 1571, 1571, 1571, 6930, 6916, 6916, 6916, 6916, 6916, 6897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3170, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3109, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 0, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 465, 257, 465, 257, 465, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 159, 0, 0, 0, 0, 0, 0, 0, 0, 473, 265, 473, 265, 473, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 190, 191, 0, 204, 205, 66, 0, 0, 0, 0, 501, 502, 503, 0, 0, 0, 0, 66, 204, 205, 156, 190, 191, 0, 0, 0, 0, 198, 199, 0, 212, 213, 74, 0, 0, 0, 0, 509, 510, 511, 0, 0, 0, 0, 74, 212, 213, 164, 198, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 6}, null, null, {"id": 4, "name": "Boss", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 113, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$BigMonster2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 13, "y": 6}, {"id": 5, "name": "<PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 4}, {"id": 6, "name": "Cutscene", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [49, 49, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Dragon King!\\! Out of my way!\\! I'm here to rescue the Princess!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [5, 1, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 44, "parameters": [{"name": "Jump1", "volume": 90, "pitch": 150, "pan": 0}], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Jump1", "volume": 90, "pitch": 150, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[27]Princess>A knight in shining armor!\\! Please, save me!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 60, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 13, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Look out Princess!\\! If this fiend won't let you escape, then he shall perish by my blade!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's time we finish this here Dragon King!\\! Bring it on!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "Battle2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["The Dragon King lunges forward with a vicious swipe!\\! His speed and range is too intense to dodge!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 212, "indent": 0, "parameters": [-1, 8, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Damage4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 30, true]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["The attack strikes with heavy damage!\\! You're caught off guard and your defenses have been reduced!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>*Ngh!*\\! W-Well done Dragon King..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[That attack really hurt, what should I do?]"]}, {"code": 102, "indent": 0, "parameters": [["Attack", "Heal"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Attack"]}, {"code": 121, "indent": 1, "parameters": [173, 173, 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Now you shall face my wrath!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [4, 6, true]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["With a mighty swing, your sword lands a deep slice across the <PERSON> King's chest!\\! He's noticeably injured after the attack "]}, {"code": 401, "indent": 1, "parameters": ["but still has plenty of energy to fight!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Heal"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I've come prepared in case of an injury.\\! Heal!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [-1, 41, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["A warm energy engulfs your body, healing off the injuries from the Dragon <PERSON>'s attack!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Your attacks are useless Dragon King!\\! Now release the Princess at once!"]}, {"code": 122, "indent": 1, "parameters": [49, 49, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I'm going to have to make this next attack count!"]}, {"code": 111, "indent": 0, "parameters": [1, 49, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>[I've used up a lot of my MP with that heal.\\! I'll need to be cautious about "]}, {"code": 401, "indent": 1, "parameters": ["how much I have left...]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>[I took a lot of damage from that first slash.\\! I'll need to be cautious about how many more hits I take..!]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["The Knight carefully plans his next move!"]}, {"code": 102, "indent": 0, "parameters": [["Piercing Lunge", "Lightning Magic"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Piercing Lunge"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Dodge this, fiend!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [4, 12, true]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [1, 49, 0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["Your lunge strikes perfectly into the Dragon King dealing critical damage!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>What's wrong Dragon King?\\! Are you unable to withstand my attacks?!"]}, {"code": 122, "indent": 2, "parameters": [49, 49, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["Your lunge strikes effectively into the Dragon King dealing a little damage!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>[I think that worked but I'm still weak from his last attack...]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Lightning Magic"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Lightning!\\! Strike down!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [-1, 76, true]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["Lightning strikes down onto the Dragon King dealing significant damage!"]}, {"code": 111, "indent": 1, "parameters": [1, 49, 0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>[That attack was effective but now my MP is running low...]"]}, {"code": 121, "indent": 2, "parameters": [174, 174, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>[That attack really stunned him!\\! That was a good choice!]"]}, {"code": 122, "indent": 2, "parameters": [49, 49, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 212, "indent": 0, "parameters": [4, 51, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[27]Princess>Look out!\\! He's preparing something strong!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[She's right!\\! This next attack he's preparing will be all or nothing!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[I need to think carefully about what I do next!]"]}, {"code": 102, "indent": 0, "parameters": [["Fire Magic", "Ice Magic", "Defend"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Fire Magic"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Here we go!\\! Fire!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [4, 66, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [4, 41, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 213, "indent": 1, "parameters": [-1, 6, false]}, {"code": 213, "indent": 1, "parameters": [5, 6, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["The fire magic absorbs right into the Dragon King!\\! His scales glisten with a powered up radiance!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>W-What?!\\! That healed him?!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[27]Princess>Did you really just use fire magic..?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[27]Princess>Everyone knows the Dragon King's primary element is fire..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>*gulp*\\! I'm in trouble now..."]}, {"code": 122, "indent": 1, "parameters": [49, 49, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Ice Magic"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Here we go!\\! Ice!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 111, "indent": 1, "parameters": [0, 174, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "Miss", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 212, "indent": 2, "parameters": [4, 71, true]}, {"code": 250, "indent": 2, "parameters": [{"name": "Damage1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [0, 174, 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["The ice magic-\\! fizzles out in a powdery mist."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>N-No!\\! That attack should've been effective!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>[I used up too much MP...\\! This isn't good at all!]"]}, {"code": 122, "indent": 2, "parameters": [49, 49, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["The ice magic dealt significant damage to the Dragon King!\\! His charged attack "]}, {"code": 401, "indent": 2, "parameters": ["has significantly weakened!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[27]Princess>Way to go!\\! That attack caught him off guard!"]}, {"code": 122, "indent": 2, "parameters": [49, 49, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Defend"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I have no other choice!\\! I need to defend against it!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [-1, 53, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["With no other choice left, you brace with all your might, prepared to take the attack head on!"]}, {"code": 111, "indent": 1, "parameters": [0, 173, 0]}, {"code": 122, "indent": 2, "parameters": [49, 49, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 212, "indent": 0, "parameters": [-1, 110, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [1, 49, 0, 0, 0]}, {"code": 242, "indent": 1, "parameters": [3]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Princess...\\! I...\\! Tried my best..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [175, 175, 1]}, {"code": 119, "indent": 1, "parameters": ["End"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["Meteors rain down with devastating strength!\\! But you manage to persevere and hang on to consciousness!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 213, "indent": 1, "parameters": [5, 1, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[27]Princess>Oh brave knight!\\! Please save me!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>N-Never fear Princess..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>For it is I who will defeat the Dragon King and save you!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 250, "indent": 1, "parameters": [{"name": "Monster4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 225, "indent": 1, "parameters": [5, 5, 60, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>[He's preparing another attack just like the last one!]"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>[This is it!\\! This attack will decide the fate of this battle!]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 49, 0, 3, 1]}, {"code": 102, "indent": 1, "parameters": [["Ultimate Ray", "No"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Ultimate Ray"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Foul beast!\\! Your reign of terror ends here..."]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Ultimate.\\! Ray.\\! OF LIGHT!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 212, "indent": 2, "parameters": [4, 100, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "No"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["Light Slash", "No"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Light Slash"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Foul beast!\\! I said you will fall to my blade!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Light.\\! SLASH!"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 212, "indent": 2, "parameters": [4, 23, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "No"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster5", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 120, false]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 249, "indent": 0, "parameters": [{"name": "Victory1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[27]Princess>You defeated the Dragon King and rescued me from his clutches!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[27]Princess>My hero ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 121, "indent": 0, "parameters": [175, 175, 0]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["MC", 1], "indent": null}]}, {"code": 211, "indent": 0, "parameters": [0]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 28]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 7}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 10}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 10}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 2}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 12}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 2}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 6}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 12}, null, null, null, null, null, null, null]}