{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 4, "width": 27, "data": [7147, 7150, 6770, 6780, 6780, 6780, 6790, 7139, 7138, 7138, 7142, 7147, 7146, 7146, 7146, 7150, 7139, 7138, 7138, 7142, 6792, 6780, 6780, 6780, 6777, 7147, 7150, 6785, 6785, 6791, 7138, 7138, 7138, 7138, 7145, 7144, 7144, 7148, 6787, 6785, 6785, 6785, 6789, 7145, 7144, 7144, 7148, 7138, 7138, 7138, 7138, 6793, 6785, 6785, 7139, 7138, 7138, 7145, 7144, 7144, 7144, 6787, 6785, 6785, 6785, 6791, 7138, 7138, 7138, 6793, 6785, 6785, 6785, 6789, 7144, 7144, 7144, 7148, 7138, 7138, 7142, 7145, 7144, 7144, 6787, 6785, 6785, 6785, 6791, 7138, 7138, 7138, 7142, 7144, 7144, 7144, 7139, 7138, 7138, 7138, 6793, 6785, 6785, 6785, 6789, 7144, 7144, 7148, 6785, 6785, 6785, 6791, 7138, 7138, 7138, 7142, 7144, 7144, 7144, 7148, 1573, 1573, 1573, 7145, 7144, 7144, 7144, 7139, 7138, 7138, 7138, 6793, 6785, 6785, 6785, 7139, 7138, 7138, 7142, 7144, 7144, 7144, 7148, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 7145, 7144, 7144, 7144, 7139, 7138, 7138, 7142, 7145, 7144, 7144, 7148, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 7145, 7144, 7144, 7148, 2740, 2740, 2740, 2756, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 2754, 2740, 2740, 2740, 2720, 2720, 2724, 2758, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 2760, 2728, 2720, 2720, 2720, 2720, 2744, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1637, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 2736, 2720, 2720, 2720, 2720, 2744, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 2736, 2720, 2720, 2720, 2720, 2744, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 2736, 2720, 2720, 2720, 2720, 2744, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 2736, 2720, 2720, 2720, 2720, 2722, 2756, 1573, 1573, 1573, 1573, 1637, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 2754, 2721, 2720, 2720, 2720, 2720, 2720, 2722, 2740, 2740, 2756, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 1573, 2754, 2740, 2740, 2721, 2720, 2720, 2720, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 214, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 174, 175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 188, 0, 0, 181, 182, 183, 0, 0, 188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 204, 205, 0, 0, 0, 196, 0, 0, 0, 0, 0, 0, 0, 196, 0, 0, 0, 204, 205, 0, 0, 0, 0, 0, 0, 152, 0, 212, 206, 207, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 235, 212, 213, 0, 159, 0, 0, 0, 0, 0, 0, 204, 205, 215, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 219, 219, 0, 0, 0, 0, 0, 0, 0, 0, 212, 213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 204, 205, 0, 0, 0, 0, 0, 0, 224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 212, 213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 12}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 4}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 4}, {"id": 4, "name": "Boss", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 113, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$BigMonster1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 6}, {"id": 5, "name": "<PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Sister", "direction": 2, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 10}, {"id": 6, "name": "Cutscene", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "Attack2", "volume": 75, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 241, "indent": 0, "parameters": [{"name": "Dungeon3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]With a skillful landing, I've infiltrated the final tomb."]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["Sister", 3], "indent": null}, {"code": 15, "parameters": [90], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [40], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["Sister", 3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [90], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [40], "indent": null}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]The Tomb of Mythics.\\! A dark and uncharted tomb said to be filled with dangerous monsters and glorious treasure."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]I look over to my side to find that my partner...\\! Still hasn't rolled his movement option yet."]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 15, "parameters": [50], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [50], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Come on \\n[1]!\\! It's your turn to roll!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Whoops!\\! Sorry, sorry, I'll roll..."]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Fall", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 250, "indent": 0, "parameters": [{"name": "Damage2", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 3]And he misses the landing...\\! Your HP was reduced due to fall damage."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Urgh, again?!\\! I already used up all of my potions too..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]Guess you'll just have to be more careful then unless you want to lose this quest?"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [40], "indent": null}, {"code": 41, "parameters": ["MC1", 3], "indent": null}, {"code": 44, "parameters": [{"name": "Jump2", "volume": 90, "pitch": 130, "pan": 0}], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [40], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["MC1", 3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Jump2", "volume": 90, "pitch": 130, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>No way!\\! We've come too far!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]That's the right attitude!\\! And the finish line should be right up ahead."]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]But wait!\\! Just now, a threatening rumble begins to erupt from underneath our feet!"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth4", "volume": 90, "pitch": 60, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 60, false]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 212, "indent": 0, "parameters": [4, 103, false]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 121, "indent": 0, "parameters": [113, 113, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]The Guardian of this domain!\\! The Spirit of Vengeance!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>The spirit of who?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Vengeance..!\\! You know, like, pure anger and revenge?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Ahh! So then he must not be happy that we're raiding through his home, right?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 7]Exactly!\\! Which means there's only one way out of this predicament!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Aw yeah!\\! It's boss battle time!"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "Battle3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 5]The Spirit begins with a chilling screech!\\! We both feel a drop in our strength while we struggle to maintain our composure."]}, {"code": 213, "indent": 0, "parameters": [5, 6, false]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 0]But that's no matter!\\! It's my turn to strike fast with a staggering shot!"]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 212, "indent": 0, "parameters": [4, 111, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Damage4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 1]A critical hit!\\! We're starting this fight off right!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 2]But can we keep up the momentum?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 0]What will you do for your turn, \\n[1]?"]}, {"code": 102, "indent": 0, "parameters": [["Attack", "Cheer Team"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Attack"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>It's time to strike fast!"]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 2]The boss completely dodged it..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 2]That screeching debuff seems to really have affected your agility, \\n[1]."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Aw man...\\! Now we're in trouble."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Cheer Team"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I'm going to cheer us up to shake off that chilling shriek!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 1]Excellent thinking, \\n[1]!"]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [-1, 36, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 1]YES!\\! The cheer was effective!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Now our stats are back to normal just like before!"]}, {"code": 122, "indent": 1, "parameters": [46, 46, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 212, "indent": 0, "parameters": [4, 52, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 6]Uh-oh!\\! He looks like he's charging up for something powerful..!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 1]No problem!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 0]I'll begin charging up for my ultimate attack as well!"]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 212, "indent": 0, "parameters": [5, 51, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 0]What are you going to do for your next turn?"]}, {"code": 102, "indent": 0, "parameters": [["Attack", "Defend"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Attack"]}, {"code": 111, "indent": 1, "parameters": [1, 46, 0, 0, 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>This time it's my turn to knock him off his feet!\\! Or uhh, ghost... tail."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>I'm going to keep up the pressure and attack again!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage2", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 0]Not a bad hit!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 7]But he's still kicking!\\! We're not done yet!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Defend"]}, {"code": 111, "indent": 1, "parameters": [1, 46, 0, 0, 0]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>I think I better play it safe this turn.\\! I'm going to defend!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Now's not a good time to go on the offense.\\! I'm going to defend myself!"]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 1]Just before he unleashes his super attack.\\! Smart move!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [-1, 51, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 0]Your defense is unbreakable now!"]}, {"code": 122, "indent": 1, "parameters": [46, 46, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [114, 114, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 6]Here it comes!\\! The boss's super attack!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 7]From the dark dimension, a charged blast of negative energy fires straight into your direction!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 5]Look out!"]}, {"code": 355, "indent": 0, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 212, "indent": 0, "parameters": [-1, 102, true]}, {"code": 111, "indent": 0, "parameters": [0, 114, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 5]Whoa!\\! You tanked that hit like a champ!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Hah!\\! Is that all you got?!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 41, "parameters": ["MC1", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["MC1", 4], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 2]Oh geez...\\! That attacked knocked you out cold..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>AGAIN?!\\! Ugh... I can't catch a break."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 2]I have one last Revive on hand.\\! Make this count!"]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [5, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [-1, 42, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [20], "indent": null}, {"code": 41, "parameters": ["MC1", 3], "indent": null}, {"code": 44, "parameters": [{"name": "Jump2", "volume": 90, "pitch": 130, "pan": 0}], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["MC1", 3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 44, "parameters": [{"name": "Jump2", "volume": 90, "pitch": 130, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [5, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).loadBitmap('face', 'Sis1[BUST][Exp3x3]')"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 46, 0, 2, 1]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 7]I'm all charged up and ready to go now!\\! It's time for my-"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Wait, \\n[4]!\\! My ultimate attack is ready too!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 5]No way!?\\! You're right!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Doesn't that mean we can choose our special combo attack?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 7]I've taught you well."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 1]Now let's give it everything we've got with this final attack!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 1]SUPER!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>COMBO!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<Both>BARRAAAGE!"]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [5, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [4, 112, true]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [4, 25, true]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 7]I'm all charged up and ready to go now!\\! It's time for my special attack!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[2, 1]ACTIVATE GUN BARRAGE!"]}, {"code": 355, "indent": 1, "parameters": ["$bust(2).clear()"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 205, "indent": 1, "parameters": [5, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 212, "indent": 1, "parameters": [4, 112, true]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster5", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 120, false]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [113, 113, 1]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 249, "indent": 0, "parameters": [{"name": "Victory2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 1]We did it!\\! We put a stop to the evil curse!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Talk about a wild fight..!\\! That boss really gave us a workout..."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]But with our dynamic duo, we're unstoppable!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>And now it's time to get our treasure!"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [115, 115, 0]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]My!\\! The two of you sound like you're having so much fun!"]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 213, "indent": 0, "parameters": [5, 6, false]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 213, "indent": 0, "parameters": [14, 4, true]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["MC", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["MC", 1], "indent": null}]}, {"code": 211, "indent": 0, "parameters": [0]}, {"code": 201, "indent": 0, "parameters": [0, 26, 0, 14, 0, 0]}, {"code": 121, "indent": 0, "parameters": [116, 116, 0]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 12}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 3}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 3}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 5}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 12}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 5}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 8}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other2", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 8}, {"id": 14, "name": "Mom", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 115, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Mom", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 14}, null, null, null, null, null, null]}