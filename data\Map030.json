{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "Laundry Room", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0Laundry_Day", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Exit", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 26, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 7}, {"id": 2, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "4Hallway_Exit1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 123, 1]}, {"code": 231, "indent": 1, "parameters": [11, "4Laundry_Laundry1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 2, 0]}, {"code": 111, "indent": 1, "parameters": [1, 12, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [12, "4Laundry_Mom2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 132, 0]}, {"code": 231, "indent": 1, "parameters": [13, "4SpringEgg_Laundry", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 44, 0]}, {"code": 117, "indent": 1, "parameters": [21]}, {"code": 121, "indent": 1, "parameters": [44, 44, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 3, "name": "<PERSON> (Day)", "note": "<Hitbox Up: 9> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 33, "switch2Valid": false, "variableId": 12, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 235, "indent": 0, "parameters": [13]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]I have some time to spare until the laundry is finished."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(2, \"$gameSwitches.value(124) == false\")"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(4, \"$gameSwitches.value(128) == false\")"]}, {"code": 355, "indent": 0, "parameters": ["hide_choice(5, \"$gameSwitches.value(124) == false\")"]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Sex", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Change Outfit", "Leave"], 5, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 231, "indent": 1, "parameters": [12, "4Laundry_Mom2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [0, 132, 0]}, {"code": 231, "indent": 2, "parameters": [13, "4SpringEgg_Laundry", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Sex"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I recognize that look in your eyes baby."]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]If you want my body, then come and have it."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 17]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON><PERSON><PERSON><PERSON>"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Lean up against the laundry baby...\\! I want to give you a taste ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 121, "indent": 1, "parameters": [16, 16, 1]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 18]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "<PERSON><PERSON><PERSON>"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([30,6,'C'], true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "Change Outfit"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]You prefer my other outfit?\\! If that's what you like, give me one moment honey."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 122, "indent": 1, "parameters": [12, 12, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [5, "Leave"]}, {"code": 231, "indent": 1, "parameters": [12, "4Laundry_Mom2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [0, 132, 0]}, {"code": 231, "indent": 2, "parameters": [13, "4SpringEgg_Laundry", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 129, "switch2Valid": true, "variableId": 12, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 235, "indent": 0, "parameters": [12]}, {"code": 235, "indent": 0, "parameters": [13]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]\\n[1]!\\! You're just in time!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]I'm about to wash a new load of laundry if you'd like to help \\n[11]?"]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Sure \\n[2]!\\! I'd love to help you out."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Okay.\\! If you have any other clothes you need me to wash, go on and hand them over now."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]Everything that you need washed honey.\\! Don't be modest."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I don't think I have much for you to wash right now..."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Unless you mean the clothes that I'm wearing right now?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Including the one's you're wearing right now, dear."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I'm sure those shorts could use a wash."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]Thank you baby."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]You know, I think it's probably time I washed my own outfit too..."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]I've been in this top for quite a while now..."]}, {"code": 101, "indent": 0, "parameters": ["", 1, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I CAN HELP WITH THAT!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]*giggle*\\! I thought you might be happy to hear that."]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Why don't you come in closer and help \\n[11] take this top off?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 17]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 12, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 12}, {"id": 4, "name": "<PERSON><PERSON><PERSON>", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": false, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Ugh!\\! I hate washing the laundry."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["<PERSON><PERSON>", "Leave"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<PERSON><PERSON>"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>The laundry needs washing.\\! I'll make \\n[2]'s day a little easier by doing this."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [22, 22, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [123, 123, 0]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [37, 37, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 250, "indent": 1, "parameters": [{"name": "Water1", "volume": 40, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "Water1", "volume": 40, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 31, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 35, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I think \\n[2] and \\n[6] \\n[3] are in \\n[2]'s room talking right now.\\! Maybe I should "]}, {"code": 401, "indent": 0, "parameters": ["try and listen in on their conversation instead."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 123, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 7}, {"id": 5, "name": "<PERSON><PERSON><PERSON>", "note": "<Hitbox Up: 3> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": false, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Ugh!\\! I hate washing the laundry."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 102, "indent": 0, "parameters": [["<PERSON><PERSON>", "Leave"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<PERSON><PERSON>"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\n[1]>The laundry needs washing.\\! I'll make \\n[2]'s day a little easier by doing this."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [22, 22, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [123, 123, 0]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [37, 37, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 250, "indent": 1, "parameters": [{"name": "Water1", "volume": 40, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "Water1", "volume": 40, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 0]}, {"code": 201, "indent": 2, "parameters": [0, 31, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": true, "switch2Id": 35, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I think \\n[2] and \\n[6] \\n[3] are in \\n[2]'s room talking right now.\\! Maybe I should "]}, {"code": 401, "indent": 0, "parameters": ["try and listen in on their conversation instead."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 123, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 12}, {"id": 6, "name": "<PERSON> <PERSON><PERSON>e <PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [20, "5L<PERSON>Bend1[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Y-You just want to play with my butt?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Okay.\\! Just don't get carried away..!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wow!\\! You have the best ass ever \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>It's a little embarrassing being in this position so please try to hurry baby..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I don't know about that.\\! I want to take my time enjoying this!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh \\n[1] ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's so soft ~\\! Your butt cheeks feel so soft and squishy \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[It's so lewd having him lust over my body with all of this intensity!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[But his excitement...\\! He's so happy admiring my body and that makes me feel so good...]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[Hmmm ~ What should I do now..?]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Squeeze \\i[31]", "Spank \\i[30]"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Squeeze \\i[31]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["*squish*\\! *squish*"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you enjoying yourself baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I could sit here and play with your butt forever \\n[2]!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then keep going baby!\\! Play with \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Spank \\i[30]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Oh!\\! Geez, you're so naughty baby!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Does that turn you on?\\! Spanking \\n[11] like I've been a bad girl?!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You bet it does!\\! I love watching your butt jiggle when I spank you!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then do it again!\\! Spank \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 18, 1]}, {"code": 111, "indent": 1, "parameters": [0, 11, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3MomBendSexA_PreIdle", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3MomBendSexA1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 232, "indent": 1, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you just going to sit there and keep staring baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>You know you can't leave \\n[11] unsatisfied, right?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 118, "indent": 1, "parameters": ["1"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Finish", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 32]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 119, "indent": 2, "parameters": ["End"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Finish"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>*sigh*\\! I suppose it can't be helped..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 232, "indent": 2, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Alright baby.\\! I think that's enough fun for now."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 27, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 12, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [20, "5L<PERSON><PERSON>end2[2x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]W-Why would you want to do something like that?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 8]Fine.\\! But only for a little bit..!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wow!\\! You have the best ass ever \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>It's a little embarrassing being in this position so please try to hurry baby..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I don't know about that.\\! I want to take my time enjoying this!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh \\n[1] ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>It's so soft ~\\! Your butt cheeks feel so soft and squishy \\n[2]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[It's so lewd having him lust over my body with all of this intensity!]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>[But his excitement...\\! He's so happy admiring my body and that makes me feel so good...]"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[Hmmm ~ What should I do now..?]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Squeeze \\i[31]", "Spank \\i[30]"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Squeeze \\i[31]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["*squish*\\! *squish*"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you enjoying yourself baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I could sit here and play with your butt forever \\n[2]!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then keep going baby!\\! Play with \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Spank \\i[30]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Oh!\\! Geez, you're so naughty baby!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Does that turn you on?\\! Spanking \\n[11] like I've been a bad girl?!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>You bet it does!\\! I love watching your butt jiggle when I spank you!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Then do it again!\\! Spank \\n[11] ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 15, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 85, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 18, 1]}, {"code": 111, "indent": 1, "parameters": [0, 11, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3MomBendSexB_PreIdle", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [21, "3MomBendSexB1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 232, "indent": 1, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 120, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>Are you just going to sit there and keep staring baby?"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>You know you can't leave \\n[11] unsatisfied, right?"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 118, "indent": 1, "parameters": ["1"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Finish", "View"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 235, "indent": 2, "parameters": [21]}, {"code": 122, "indent": 2, "parameters": [7, 7, 0, 0, 33]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 119, "indent": 2, "parameters": ["End"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Finish"]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[23]\\N[2]>*sigh*\\! I suppose it can't be helped..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 232, "indent": 2, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, true]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Mom8[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Alright baby.\\! I think that's enough fun for now."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [18, 18, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [37, 37, 0]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 2, "y": 0}, {"id": 7, "name": "<PERSON><PERSON><PERSON> Complete", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 124, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 27, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 10, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["A little later..."]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).loadBitmap('face', 'Mom1[BUST][Exp4x3]')"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\bust[1]\\bustExp[1, 2] \\^"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 3]All of today's laundry is finally done drying!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 0]It's always so nice to be caught up on laundry."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 7]Or at least, that's what I would say if you didn't make such a mess on my other clothes..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Hah hah, whoops!\\! Sorry \\n[2]..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]I don't mind baby...\\! I loved seeing how excited that outfit made you."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]If you'd like to see me wear that outfit while I'm cleaning, just ask, okay honey?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>You're letting me decide what you wear?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]You love to watch me clean..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 10]I might as well let you choose your preferred view."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>You're the best \\n[5] ever!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]I love you baby ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I love you too, \\n[2]."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["You can now change \\n[2]'s outfit by talking to her during the Morning or Midday!"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 122, "indent": 0, "parameters": [12, 12, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 13]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 8, "name": "Easter Egg", "note": "<Hitbox Up: 1> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 132, "switch1Valid": true, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon activity;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "choice_confirm_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [13]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [1, 38, 0, 0, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Is that...\\! An egg?\\! It's just like the one \\n[4] showed me."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Could there be more of these hidden around the house?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 38, 0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Another egg?!\\! Who in the world is hiding these around the house?!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 38, 0, 2, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>There's another egg!"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Man, I feel like I've search every part of the house now...\\! Could that be all of them?"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Maybe \\n[4] has found some more too.\\! I should go check up with her and see."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [38, 38, 1, 0, 1]}, {"code": 111, "indent": 0, "parameters": [1, 38, 0, 3, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 201, "indent": 1, "parameters": [0, 33, 0, 14, 8, 0]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [132, 132, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 3}, null, null]}