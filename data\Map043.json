{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "Happy - syncop<PERSON>", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 27, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [7]}, {"code": 241, "indent": 0, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 284, "indent": 0, "parameters": ["0Bedroom_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomCaught1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [23, "3zMomCaughtA[4x4]", 0, 0, 1000, 130, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [24, "3zMomCaughtDoor", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_1a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_1b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [24, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [23, 0, 0, 0, 1000, 130, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 235, "indent": 0, "parameters": [24]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Laundry_DayStuck", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zSisLaundryDuring1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zSisLaundryDuring2[4x4]", 0, 0, 490, 200, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_2a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_2b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 490, 200, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 490, 200, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Kitchen_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zAuntStand1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zAuntStand2[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_3a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_3b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["2Bathroom_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomBathPai1[4x4]", 0, 0, 640, 120, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3zMomBathPai_Water", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_4a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_4b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0LivingRoom_Day1", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3zMomLRSex_Sofa", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_5a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_5b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 600, 0, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 600, 0, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Bedroom_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomBJ[4x4]", 0, 0, 600, 233, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_6a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_6b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 580, 160, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 600, 233, 100, 100, 0, 0, 40, true]}, {"code": 242, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 241, "indent": 0, "parameters": [{"name": "Scene2_MZ", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [20, "Credits_7", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Ah!\\! You're finally up baby!"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>I'm just cleaning the living room..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Would you mind lending \\n[11] a hand ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 242, "indent": 0, "parameters": [4]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 117, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 14, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [19, 19, 0]}, {"code": 121, "indent": 0, "parameters": [197, 197, 0]}, {"code": 121, "indent": 0, "parameters": [198, 198, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 196, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [7]}, {"code": 241, "indent": 0, "parameters": [{"name": "Happy - syncop<PERSON>", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 284, "indent": 0, "parameters": ["0Bedroom_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomBedMate2[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomCaught1[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [23, "3zMomCaughtA[4x4]", 0, 0, 1000, 130, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [24, "3zMomCaughtDoor", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 20 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Loop"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_1a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_1b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [24, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [23, 0, 0, 0, 1000, 130, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 235, "indent": 0, "parameters": [24]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Laundry_DayStuck", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zSisLaundryDuring1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zSisLaundryDuring2[4x4]", 0, 0, 490, 200, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_2a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_2b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 490, 200, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 490, 200, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Kitchen_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zAuntStand1[4x4]", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zAuntStand2[4x4]", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_3a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_3b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["2Bathroom_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomBathPai1[4x4]", 0, 0, 640, 120, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3zMomBathPai_Water", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_4a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_4b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0LivingRoom_Day1", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3zMomLRSex_Sofa", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomSisCaughtA2[4x4]", 0, 0, 600, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomSisCaughtA4[4x4]", 0, 0, 600, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_5a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_5b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 600, 0, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 600, 0, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 284, "indent": 0, "parameters": ["0Bedroom_Day", false, false, 0, 0]}, {"code": 231, "indent": 0, "parameters": [19, "3zMomBJ_BGfix", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "3zMomBJ[4x4]", 0, 0, 600, 233, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zMomChairKiss2[4x4]", 0, 0, 580, 160, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [25, "Credits_6a", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "Credits_6b", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 0, 0, 40, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 580, 160, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 600, 233, 100, 100, 0, 0, 40, true]}, {"code": 242, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [19]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 241, "indent": 0, "parameters": [{"name": "Scene2_MZ", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [20, "Credits_7", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Ah!\\! You're finally up baby!"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>I'm just cleaning the living room..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Would you mind lending \\n[11] a hand ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [0, 4, 0]}, {"code": 201, "indent": 1, "parameters": [0, 11, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 12, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 121, "indent": 0, "parameters": [196, 196, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}]}