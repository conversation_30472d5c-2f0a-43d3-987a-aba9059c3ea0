{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "Hallway", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0Hallway_Day", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 0, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 0, 0, 0, 0, 0, 1544, 1544, 1544, 1544, 1544, 0, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 1544, 0, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "4Hallway_Exit1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 3, 0]}, {"code": 231, "indent": 1, "parameters": [11, "4HallwayDay_Bathroom2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 44, 0]}, {"code": 117, "indent": 1, "parameters": [21]}, {"code": 121, "indent": 1, "parameters": [44, 44, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 219, 0]}, {"code": 111, "indent": 1, "parameters": [0, 217, 1]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<Me>. . . Huh?\\! \\n[2], \\n[4], and \\n[6] \\n[3] are talking in the living room."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<Me>I wonder what they're talking about..."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 231, "indent": 2, "parameters": [90, "8StatUp_Group", 0, 0, -230, 400, 100, 100, 255, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 2, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 250, "indent": 2, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 250, "indent": 2, "parameters": [{"name": "save_01", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["A very important event is about to take place!\\! Go "]}, {"code": 401, "indent": 2, "parameters": ["and see it!"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 232, "indent": 2, "parameters": [90, 0, 0, 0, 0, 400, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 2, "parameters": [90]}, {"code": 121, "indent": 2, "parameters": [195, 195, 0]}, {"code": 121, "indent": 2, "parameters": [217, 217, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 149, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 199, 1]}, {"code": 356, "indent": 1, "parameters": ["ChangeTitle1 Title_Bath"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [11, "4HallwayDay_Bathroom2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 121, "indent": 0, "parameters": [150, 150, 1]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 3]Now where did my bra end up?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]Perhaps I left it in the bathroom?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]Aww...\\! Looks like someone's already in there."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>*giggle*\\! Hey!\\! That tickles!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]Wait just a minute...\\! Sis?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]Is she talking to someone in the bathtub?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Mmh ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]*gasp*\\! No she isn't!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Oh ~ I've got to hear this!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 245, "indent": 0, "parameters": [{"name": "PixieDeepFast", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Ahh ~ Yeah!\\! Nice and slow..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>Oh baby ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Now THIS is going to be fun."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 22]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 2, "name": "Exit", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 2, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 7}, {"id": 3, "name": "MC's Room", "note": "<Hitbox Up: 8> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 195, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>No.\\! I should go check on the girls in the living room first!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 11}, {"id": 4, "name": "Bathroom", "note": "<Hitbox Up: 13> <Hitbox Right: 3>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 3, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>That's strange...\\! \\n[2] left the door open."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>She never does that when she's in the bath."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Maybe I could get a peek just to help me stop these thoughts.\\! Ya, that should work!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 13, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 3, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 13, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 19, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 3, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 3, "switch1Valid": true, "switch2Id": 28, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I probably shouldn't peek on \\n[2] again today.\\! She'll probably catch me!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 195, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>No.\\! I should go check on the girls in the living room first!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 14}, {"id": 5, "name": "Emily's Room", "note": "<Hitbox Up: 8> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>If I go into 'her' room when she's not here...\\! *gulp*"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 16, 0, 999, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>That's <PERSON><PERSON>' room.\\! She'd kill me if she found out I went in there!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 21, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 208, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Go away!\\! Don't bother me \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>[I think she's mad at me.\\! I should give her time to cool off...]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 195, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>No.\\! I should go check on the girls in the living room first!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 11}, {"id": 6, "name": "Mom's Room", "note": "<Hitbox Up: 14> <Hitbox Right: 3>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>That's \\n[2]'s room.\\! I don't have a reason to go in there right now."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 18, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 33, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3] is still sleeping in there.\\! I shouldn't risk waking her up."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 45, "switch1Valid": false, "switch2Id": 1, "switch2Valid": true, "variableId": 17, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Maybe \\n[6] \\n[3] is touching herself again in there.\\! I probably shouldn't check but... I can't help it."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 231, "indent": 0, "parameters": [20, "3zAuntHJ_Before", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Looking for me, honey?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]!\\! I was uh, just looking for something that I lost..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>You were looking for a nice peek again, weren't you?\\! Don't think I didn't notice you snooping around last time!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Y-You saw me?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Well, you did just confirm it now you naughty boy!\\! But you weren't very discreet about it either..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>And not only were you just watching...\\! You were touching yourself too, weren't you?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>...!"]}, {"code": 231, "indent": 0, "parameters": [22, "3zAuntHJ_During", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Mmm, look at how hard that big cock is again..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>You just can't stop thinking about seeing me naked...\\! Touching myself like the naughty girl I am..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Do you want me to touch you as well?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>W-Well yeah...\\! But what about the others?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>No need to worry about them. \\!We'll be nice and quiet about it..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [21, "3zAuntHJ[4x4]", 0, 0, 480, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>*giggle*\\! Shhh..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Mmm, are you going to cum \\n[1]?\\! Do it! Cum for me honey!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 70, "pitch": 90, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Loud1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 NoLoop"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>Did you like that?\\! Hmm, now run along.\\! We don't want the others to find out, now would we?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 4]}, {"code": 121, "indent": 0, "parameters": [44, 44, 0]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 111, "indent": 0, "parameters": [0, 4, 1]}, {"code": 201, "indent": 1, "parameters": [0, 7, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 10, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [17, 17, 0, 0, 8]}, {"code": 121, "indent": 0, "parameters": [46, 46, 0]}, {"code": 121, "indent": 0, "parameters": [45, 45, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 45, "switch1Valid": false, "switch2Id": 1, "switch2Valid": true, "variableId": 17, "variableValid": true, "variableValue": 8}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I wonder if \\n[6] \\n[3] is still in the bedroom."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Can I hear her in there?\\! Hmmm..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["I Hear Her (Masturbation)", "It's Quiet (Handjob)", "Leave"], 2, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "I Hear Her (Masturbation)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 121, "indent": 1, "parameters": [45, 45, 0]}, {"code": 201, "indent": 1, "parameters": [0, 18, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "It's Quiet (Handjob)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 231, "indent": 1, "parameters": [20, "3zAuntHJ_Before", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Oh you naughty, naughty boy..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>You just can't help yourself, can you?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I wasn't trying to peep, I swear!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Honey...\\! You can't fool anyone with that erection of yours."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>You were so excited to see me pleasing myself again, weren't you?"]}, {"code": 231, "indent": 1, "parameters": [22, "3zAuntHJ_During", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Don't worry.\\! I'll finish you off nice and fast so the others don't find us."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [22]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 231, "indent": 1, "parameters": [21, "3zAuntHJ[4x4]", 0, 0, 480, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 1]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>*giggle*\\! Shhh..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Ahhh, are you ready to cum \\n[1]?\\! Cum all over me, baby!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Cumshot", "volume": 70, "pitch": 90, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Loud1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 NoLoop"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Hmm...\\! You may carry on now..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [24, 24, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [44, 44, 0]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [21]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 1]}, {"code": 201, "indent": 2, "parameters": [0, 7, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 201, "indent": 2, "parameters": [0, 10, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [46, 46, 0]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 85, "switch1Valid": false, "switch2Id": 1, "switch2Valid": true, "variableId": 17, "variableValid": true, "variableValue": 11}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 18, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 85, "switch1Valid": true, "switch2Id": 1, "switch2Valid": true, "variableId": 17, "variableValid": false, "variableValue": 9}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I wonder if \\n[6] \\n[3] is still in the bedroom."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Can I hear her in there?\\! Hmmm..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["I Hear Her (Masturbation)", "It's Quiet (Handjob)", "Leave"], 2, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "I Hear Her (Masturbation)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 201, "indent": 1, "parameters": [0, 18, 0, 14, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "It's Quiet (Handjob)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 231, "indent": 1, "parameters": [20, "3zAuntHJ_Before", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Oh you naughty, naughty boy..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>You just can't help yourself, can you?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I wasn't trying to peep, I swear!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Honey...\\! You can't fool anyone with that erection of yours."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>You were so excited to see me pleasing myself again, weren't you?"]}, {"code": 231, "indent": 1, "parameters": [22, "3zAuntHJ_During", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 232, "indent": 1, "parameters": [20, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Don't worry.\\! I'll finish you off nice and fast so the others don't find us."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [22]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 231, "indent": 1, "parameters": [21, "3zAuntHJ[4x4]", 0, 0, 480, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 1]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]..."]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>*giggle*\\! Shhh..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 2"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]!"]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Ahhh, are you ready to cum \\n[1]?\\! Cum all over me, baby!"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 4"]}, {"code": 122, "indent": 1, "parameters": [8, 8, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [9, 9, 0, 0, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Cumshot", "volume": 70, "pitch": 90, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 Speed 6"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Loud1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["AnimatedPicture 21 NoLoop"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>Hmm...\\! You may carry on now..."]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [24, 24, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [44, 44, 0]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [21]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 111, "indent": 1, "parameters": [0, 4, 1]}, {"code": 201, "indent": 2, "parameters": [0, 7, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 201, "indent": 2, "parameters": [0, 10, 0, 14, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 121, "indent": 1, "parameters": [46, 46, 0]}, {"code": 121, "indent": 1, "parameters": [20, 20, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 46, "switch1Valid": true, "switch2Id": 1, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I... probably should leave her alone right now."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 35, "switch1Valid": true, "switch2Id": 2, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Maybe I can hear what \\n[2] and \\n[6] \\n[3] are talking about..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 18, 0, 14, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 35, "switch1Valid": true, "switch2Id": 1, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Maybe I can hear what \\n[2] and \\n[6] \\n[3] are talking about..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 18, 0, 14, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 50, "switch1Valid": true, "switch2Id": 4, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 19, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 195, "switch1Valid": true, "switch2Id": 2, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<Me>No.\\! I should go check on the girls in the living room first!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 14}, {"id": 7, "name": "Fap Notification", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 27, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 21, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I have to do something about this boner!\\! I've got some tissues in my room that I could use!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 29, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 21, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 7]There's no mistaking it.\\! I heard him saying my name..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 8]He was really masturbating to the thought of me..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bustExp[1, 8]But why do I feel...\\| excited about that?!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 201, "indent": 0, "parameters": [0, 11, 0, 5, 8, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>What a day...\\! I should probably just call it a night now and get some sleep."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 121, "indent": 0, "parameters": [26, 26, 1]}, {"code": 121, "indent": 0, "parameters": [29, 29, 1]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 0}, {"id": 8, "name": "Aunt <PERSON><PERSON>/Sis Cutscene", "note": "After Paizuri Scene", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 59, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 21, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Geez...\\! There you are."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Ah!\\! \\n[4]!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Have you seen my game by..\\| any chance..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I-Is something the matter?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 6]You just walked out of \\n[2]'s room and you're sweaty.\\! What were you doing in there..?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>O-Oh!\\! I was just exercising alone like usual!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).move<PERSON><PERSON>(-200, 0, 60)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 0]Oh! Don't mind me!\\! I'm going to squeeze past you two and make a banana smoothie.\\! Would anyone care for some?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]I think we will pass on the offer \\n[6] \\n[3]."]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 1]More for me then!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 4]Ah, I almost forgot!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt2[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[3]\\bustExp[3, 6]That partner workout was fantastic, \\n[1].\\! You really know all the right places to help stretch me out..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).move<PERSON><PERSON>(-1200, 0, 130)"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).move<PERSON><PERSON>(+200, 0, 60)"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Working out alone, huh..?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Hmph."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 355, "indent": 0, "parameters": ["$bust(3).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 5}, {"id": 9, "name": "Aunt Sex Events", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 45, "switch2Valid": true, "variableId": 21, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Short2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>What was that noise?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Short3", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wait...\\! I'm hearing something coming from...\\! \\n[2]'s bedroom?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 121, "indent": 0, "parameters": [47, 47, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 47, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 1}, {"id": 10, "name": "Aunt <PERSON> Sound Effects", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [43]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 130, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [21]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 140, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 25, "y": 4}, {"id": 11, "name": "<PERSON><PERSON>", "note": "<PERSON>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Soft2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Soft3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Soft1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Soft4", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Soft2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Soft1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf2 Soft5", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Mary_Soft4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Mary_Soft5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [135]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Mary_Soft6", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [140]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Mary_Soft2", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ1", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ2", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf BJ3", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf BJ3", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf BJ5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [110]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex1", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [190]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [190]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex4", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex5", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex6", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 8}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex4", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf Sex5", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Z Milf Sex6", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [130]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 10}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 4}, {"id": 12, "name": "Android 0.8.2 Update", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 185, "switch1Valid": true, "switch2Id": 14, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 9, 1]}, {"code": 121, "indent": 1, "parameters": [11, 11, 0]}, {"code": 121, "indent": 1, "parameters": [9, 9, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [186, 186, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 186, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 0}, {"id": 13, "name": "Aunt & Threesome Cutscenes", "note": "|Triggers Date Event", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 45, "switch2Valid": true, "variableId": 17, "variableValid": false, "variableValue": 18}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Loud1", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "Z Milf2 Soft4", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Was that \\n[6] \\n[3]?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>That was weird...\\! She sounds like she's struggling with something in \\n[2]'s bedroom."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I should probably check on her and make sure she's alright."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 18, 0, 14, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 17, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 59, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 17, "variableValid": true, "variableValue": 18}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]\\n[1]!\\! It's an emergency!\\! Everything is falling apart!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Whoa, hang on a second!\\! What's going on \\n[6] \\n[3]?!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 8]My car is DEAD!\\! Oooh nooo ~ *sob*"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Dead?\\! You mean the car battery, right?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 8]Waaah!\\! Our date is ruined!\\! I'm such a failure!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Hey, it's alright \\n[6] \\n[3]!\\! You don't have to cry!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Maybe we can just postpone our date until we get your car fixed?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 8]It's hopeless!\\! Waaah!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>[She's sobbing uncontrollably!\\! Quick, \\n[1]!\\! You've got to think of something fast!]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Umm...\\! What if we still have our date tonight \\n[6] \\n[3]?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]*sniffle*\\! What do you mean?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Let's move our date to the dining room!\\! We can still dress up and eat there tonight instead of going out!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]That's a fantastic idea, \\n[1]!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]We'll have a lovely, perfect, romantic dinner at home tonight!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]You're such a quick thinker, love!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>[Wasn't she just crying a second ago..?]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>See?\\! There's no reason to be upset!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]But now I have to think about my new outfit!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Wait, didn't you already have your outfit figured out?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]For the restaurant!\\! But now the theme is completely different!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]The dining room has a much warmer tone.\\! Compared to the restaurant's softer tones.\\! That means a turquoise shade would be unacceptable!\\! So instead maybe I just go with something spicier, like my crimson red dress..."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]But then I have to consider "]}, {"code": 401, "indent": 0, "parameters": ["what sort of heels I'm going to where to match those? I do have a pair of stiletto's that could convey a "]}, {"code": 401, "indent": 0, "parameters": ["sexier more mature expression. Oh but then there's the hint of gold that I'm going to need to..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>.\\| .\\| .\\| [She's off in her own world, isn't she..?]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Um.\\! \\n[6] \\n[3]?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]Yes dear?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>It sounds like you have a lot to think about for our date tonight so I'll give you some space okay?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Oh, you're too kind!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]Just make sure you don't forget about our date tonight, okay?\\! The color to wear is red!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]I'll be waiting for you in the dining room tonight."]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]The others should be in their rooms then so we can have full privacy."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [86, 86, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 163, "switch1Valid": true, "switch2Id": 45, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 18}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 2]There you are.\\! I'm ready, baby..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Are we going to have sex right now?!"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Nice and rough!\\! I want you to pound me real hard on your bed right now!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Will the others catch us?"]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]\\n[3] is helping your \\n[7] out with something right now.\\! We should have some time to ourselves..."]}, {"code": 101, "indent": 0, "parameters": ["Mom1[BUST][Exp4x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[23]\\N[2]>\\bust[1]\\bustExp[1, 9]Let's not waste any more time, baby!\\! Come and fuck \\n[11]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 14, 0, 0]}, {"code": 121, "indent": 0, "parameters": [162, 162, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 176, "switch1Valid": true, "switch2Id": 45, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 18}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]Oh dear, is everything alright \\n[9]?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I saw your \\n[5] sprinting off.\\! Her face was burning red..!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Uhh yeah...\\! Everything's okay \\n[6] \\n[3]."]}, {"code": 111, "indent": 0, "parameters": [0, 166, 0]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Something tells me you're getting into more naughty mischief, aren't you..?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I guess you could say it's something like that..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]I should have known."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Maybe I'll pay your \\n[5] a visit later and help her get those feelings off of her chest!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Oh!\\! And you should probably put some clothes on..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Whoops!\\! [I completely forgot I'm naked!]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Something tells me you've been naughty, haven't you..?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>I guess you could say it's something like that..."]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Well, you better be on your best behavior this summer for your \\n[5]!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Or don't!\\! *giggle*"]}, {"code": 101, "indent": 1, "parameters": ["Aunt1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]Oh!\\! And you should probably put some clothes on..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>R-Right!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 12, 0, 14, 0, 0]}, {"code": 121, "indent": 0, "parameters": [176, 176, 1]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 1, "y": 0}, {"id": 14, "name": "Sis Spring Event Start", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 110, "switch1Valid": true, "switch2Id": 45, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 18}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Hold it right there."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Uhh, hey \\n[4].\\! What's up?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Are you trying to play a funny prank on me or something?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Huh?\\! I haven't been playing any pranks.\\! At least, I don't think I have..?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 4]Well, I woke up this morning and found this weird "]}, {"code": 401, "indent": 0, "parameters": ["easter egg on my bed."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>An easter egg?\\! Can't say that rings any bells."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 6]Wait, really?!\\! You swear that wasn't you?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Ya, I swear.\\! Can I see it?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Ya, sure.\\! Here you go."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Whoa, this actually looks really well made!\\! It's got cool patterns "]}, {"code": 401, "indent": 0, "parameters": ["and everything!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 5]Right?!"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]I just don't understand where it came from."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>You don't think it could have been \\n[2] or \\n[6] \\n[3], could it?"]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 2]Both of them were completely unaware of it."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Weird.\\! I don't know, but I'll keep my eyes open for any other weird eggs."]}, {"code": 101, "indent": 0, "parameters": ["Sis1[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>\\bust[1]\\bustExp[1, 0]Cool.\\! Thanks \\n[1]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["The Spring Easter Egg Hunt has officially begun!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Can you find all of the strange easter eggs hidden around the house?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["I'm sure a special experience will be waiting for you if you can collect them all!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["Eggs can only be found during the Morning, Day, or Evening times.\\! <br>Happy hunting!"]}, {"code": 121, "indent": 0, "parameters": [132, 132, 0]}, {"code": 121, "indent": 0, "parameters": [134, 134, 0]}, {"code": 121, "indent": 0, "parameters": [133, 133, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 20, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 0}]}