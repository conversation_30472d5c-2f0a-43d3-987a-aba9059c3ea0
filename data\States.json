[null, {"id": 1, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 1, "maxTurns": 1, "message1": " has fallen!", "message2": " is slain!", "message3": "", "message4": " revives!", "minTurns": 1, "motion": 3, "name": "Knockout", "note": "State #1 will be automatically added when\nHP reaches 0.", "overlay": 0, "priority": 100, "releaseByDamage": false, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 23, "dataId": 9, "value": 0}]}, {"id": 2, "autoRemovalTiming": 2, "chanceByDamage": 100, "description": "", "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "Guard", "note": "", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": true, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 62, "dataId": 1, "value": 0}]}, {"id": 3, "autoRemovalTiming": 0, "chanceByDamage": 100, "description": "", "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 14, "dataId": 1, "value": 0}]}, {"id": 4, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 2, "maxTurns": 1, "message1": " is poisoned!", "message2": " is poisoned!", "message3": "", "message4": " is no longer poisoned!", "minTurns": 1, "motion": 1, "overlay": 1, "name": "Poison", "note": "", "priority": 50, "releaseByDamage": false, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 7, "value": -0.1}]}, {"id": 5, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 3, "maxTurns": 5, "message1": " is blinded!", "message2": " is blinded!", "message3": "", "message4": " is no longer blinded!", "minTurns": 3, "motion": 1, "name": "Blind", "note": "", "overlay": 2, "priority": 60, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 0, "value": -0.5}]}, {"id": 6, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 4, "maxTurns": 5, "message1": " is silenced!", "message2": " is silenced!", "message3": "", "message4": " is no longer silenced!", "minTurns": 3, "motion": 1, "name": "Silence", "note": "", "overlay": 3, "priority": 65, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 42, "dataId": 1, "value": 0}]}, {"id": 7, "autoRemovalTiming": 1, "chanceByDamage": 50, "iconIndex": 5, "maxTurns": 4, "message1": " is enraged!", "message2": " is enraged!", "message3": "", "message4": " is no longer enraged!", "minTurns": 2, "motion": 1, "name": "Rage", "note": "", "overlay": 4, "priority": 70, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 1, "stepsToRemove": 100, "traits": []}, {"id": 8, "autoRemovalTiming": 1, "chanceByDamage": 50, "iconIndex": 6, "maxTurns": 4, "message1": " is confused!", "message2": " is confused!", "message3": "", "message4": " is no longer confused!", "minTurns": 2, "motion": 1, "name": "Confusion", "note": "", "overlay": 5, "priority": 75, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 2, "stepsToRemove": 100, "traits": []}, {"id": 9, "autoRemovalTiming": 1, "chanceByDamage": 50, "iconIndex": 7, "maxTurns": 4, "message1": " is fascinated!", "message2": " is fascinated!", "message3": "", "message4": " is no longer fascinated!", "minTurns": 2, "motion": 1, "name": "Fascination", "note": "", "overlay": 6, "priority": 80, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 3, "stepsToRemove": 100, "traits": []}, {"id": 10, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 8, "maxTurns": 5, "message1": " falls asleep!", "message2": " falls asleep!", "message3": " is sleeping.", "message4": " wakes up!", "minTurns": 3, "motion": 2, "name": "Sleep", "note": "", "overlay": 7, "priority": 90, "releaseByDamage": true, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 1, "value": -1}]}]