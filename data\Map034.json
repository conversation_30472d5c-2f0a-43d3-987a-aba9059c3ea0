{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "Laundry Room", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0Laundry_DayStuck", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Exit", "note": "<Hitbox Up: 2> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 26, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 7}, {"id": 2, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "4Hallway_Exit1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [21, "3zSisLaundryIdle1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Loop"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 37, 0]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 121, "indent": 1, "parameters": [37, 37, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 44, 0]}, {"code": 117, "indent": 1, "parameters": [21]}, {"code": 121, "indent": 1, "parameters": [44, 44, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 3, "name": "<PERSON><PERSON><PERSON>", "note": "<Hitbox Up: 5> <Hitbox Right: 3>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 33, "switch1Valid": false, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 111, "indent": 0, "parameters": [0, 199, 1]}, {"code": 356, "indent": 1, "parameters": ["ChangeTitle1 Title_Laundry"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Hello?!\\! \\n[1], is that you?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Hah hah hah!\\! How's that laundry going \\n[4]?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*sigh*\\! Can we just skip the jokes?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I know, I know...\\! \"Help me little \\n[10]! \\!I'm stuck!\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>How'd you even get stuck in there?\\! Are your tits too big or something?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>You would know!\\! You're always trying to cop a feel for them."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Would you hurry up already!\\! I need you to pull me out of here!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Sure can do.\\! I'll pull out, alright."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Undress", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Undress"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xSisLaundryIdle1[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zSisLaundryIdle1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [22, "3zSisLaundryIdle2[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [22, "3xSisLaundryIdle2[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>H-Hey!\\! What the hell do you think you're doing \\n[1]?!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Whoops!\\! Sorry \\n[4]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>When I grabbed your waist to pull you out, your pants slipped right off!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*sigh*\\! How convenient..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Look on the bright side!\\! I can get a better grip now on your waist!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I'll get you out in no time!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Right..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>So are you ever going to pull me out or are you going to keep staring at my ass?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>What?!\\! H-How could you tell..?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Ugh ~ so predictable..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["<PERSON><PERSON>", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "<PERSON><PERSON>"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xSisLaundryIdle2[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [22, "3zSisLaundryIdle2[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [23, "3zSisLaundryIdle3[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [23, "3xSisLaundryIdle3[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Let's see.\\! If I wrap around you like this, I think I can get you out."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>You dummy.\\! Did you think I wouldn't notice what you're doing, you pervert?!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Huh?\\! What are you talking about?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I can feel that you don't have any pants on...\\! And you have a boner!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Unreal.\\! You're just trying to fuck me now, aren't you?!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>N-No!\\! Of course not!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>My pants were too tight.\\! This will help me get a comfortable grip."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Idiot!\\! Just pull me out already!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Okay.\\! Hang on \\n[4]!\\! Here I go!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [23, "3xSisLaundryIdle3[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [23, "3zSisLaundryIdle3[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [24, "3zSisLaundryDuring1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [24, "3xSisLaundryDuring1[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 24 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedSlow_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Oooh ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Shit!\\! My dick slipped inside of you!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>U-Uh huh...\\! Just keep pulling me!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [24, "3xSisLaundryDuring1[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 24 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [24, "3zSisLaundryDuring1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 24 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 24 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I'm trying to wiggle you out!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Ahh ~ Keep pulling!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Mmh!\\! Mmmh ~ I think it's working!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>You've got to pull me harder ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Harder!", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Harder!"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [24, "3xSisLaundryDuring1[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 24 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [24, "3zSisLaundryDuring1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 24 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [25, "3zSisLaundryDuring2[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [25, "3xSisLaundryDuring2[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 25 Speed 4"]}, {"code": 235, "indent": 0, "parameters": [24]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedFast_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Oh fuck!\\! Right there!\\! That's it!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Ahn!\\! Ahh ~ Ah ~ Oooh ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Fuck, fuck yeah!\\! Oh fuuck mee ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>A-Almost ~ I'm almost there \\n[4]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Cum", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Cum"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [25, "3xSisLaundryDuring2[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 25 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [25, "3zSisLaundryDuring2[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 25 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [21, "3zSisLaundryAfter[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3xSisLaundryAfter[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "ZCottonMilf1_Orgasm", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I think you're loose enough to slip out of there now..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*pant*\\! Yeah...\\! I can wiggle out now."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>But I think I'm just going to sit here and rest for a sec ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Okay...\\! Let me know if you need any more help again."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Yeah...\\! Will do ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Leave", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Leave"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xSisLaundryAfter[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zSisLaundryAfter[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 121, "indent": 0, "parameters": [141, 141, 0]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 122, "indent": 0, "parameters": [16, 16, 1, 0, 1]}, {"code": 122, "indent": 0, "parameters": [35, 35, 0, 0, 2]}, {"code": 121, "indent": 0, "parameters": [73, 73, 0]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 26, 0, 14, 2, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 33, "switch1Valid": false, "switch2Id": 24, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>\\n[4]?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Help me little \\n[10]! \\!I'm stuck!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Heh.\\! Again?"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I know.\\! I'm such a clumsy \\n[7]!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>So hurry!\\! I need you to pull me out of here!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Okay, here I go!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Undress", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Undress"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xSisLaundryIdle1[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zSisLaundryIdle1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [22, "3zSisLaundryIdle2[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [22, "3xSisLaundryIdle2[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>My pants!\\! Oh no ~ !"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Whoops!\\! Sorry \\n[4]!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>When I grabbed your waist to pull you out, your pants slipped right off!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>How unfortunate..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["<PERSON><PERSON>", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "<PERSON><PERSON>"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [22, "3xSisLaundryIdle2[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [22, "3zSisLaundryIdle2[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 22 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [23, "3zSisLaundryIdle3[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [23, "3xSisLaundryIdle3[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Let's see.\\! If I wrap around you like this, I think I can get you out."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>All this pulling must be getting you excited little \\n[10]."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Huh?\\! What are you talking about?!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>I can feel your penis rubbing against my butt!\\! And you're fully erect!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>But don't worry about that!\\! Just pull me out already!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Okay.\\! Hang on \\n[4]!\\! Here I go!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Fuck", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Fuck"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [23, "3xSisLaundryIdle3[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [23, "3zSisLaundryIdle3[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 23 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [24, "3zSisLaundryDuring1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [24, "3xSisLaundryDuring1[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 24 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedSlow_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Oooh ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Shit!\\! My dick slipped inside of you!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>U-Uh huh...\\! Just keep pulling me!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Faster", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Faster"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [24, "3xSisLaundryDuring1[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 24 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [24, "3zSisLaundryDuring1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 24 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 24 Speed 6"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I'm trying to wiggle you out!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Ahh ~ Keep pulling!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Mmh!\\! Mmmh ~ I think it's working!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>You've got to pull me harder ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Harder!", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Harder!"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [24, "3xSisLaundryDuring1[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 24 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [24, "3zSisLaundryDuring1[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 24 Speed 6"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [25, "3zSisLaundryDuring2[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [25, "3xSisLaundryDuring2[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 25 Speed 4"]}, {"code": 235, "indent": 0, "parameters": [24]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 3]}, {"code": 245, "indent": 0, "parameters": [{"name": "MilfMedFast_Cotton", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Oh fuck!\\! Right there!\\! That's it!"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Ahn!\\! Ahh ~ Ah ~ Oooh ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Fuck, fuck yeah!\\! Oh fuuck mee ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>A-Almost ~ I'm almost there \\n[4]!"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Cum", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Cum"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [25, "3xSisLaundryDuring2[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 25 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [25, "3zSisLaundryDuring2[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 25 Speed 4"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 16, 1]}, {"code": 231, "indent": 1, "parameters": [21, "3zSisLaundryAfter[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [21, "3xSisLaundryAfter[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cumshot", "volume": 75, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "ZCottonMilf1_Orgasm", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>I think you're loose enough to slip out of there now..."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>*pant*\\! Yeah...\\! I can wiggle out now."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>But I think I'm just going to sit here and rest for a sec ~ ♥️"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\n[1]>Okay...\\! Let me know if you need any more help again."]}, {"code": 101, "indent": 0, "parameters": ["", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[10]\\N[4]>Yeah...\\! Will do ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(16) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(16) === false\")"]}, {"code": 102, "indent": 1, "parameters": [["Leave", "Zoom In", "Zoom Out", "View"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Leave"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Zoom In"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 231, "indent": 2, "parameters": [20, "3xSisLaundry_BG", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [21, "3xSisLaundryAfter[4x4]", 0, 0, 250, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Zoom Out"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [20]}, {"code": 231, "indent": 2, "parameters": [21, "3zSisLaundryAfter[4x4]", 0, 0, 490, 200, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["AnimatedPicture 21 Speed 8"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "View"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 121, "indent": 0, "parameters": [16, 16, 1]}, {"code": 122, "indent": 0, "parameters": [35, 35, 0, 0, 2]}, {"code": 121, "indent": 0, "parameters": [73, 73, 0]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 26, 0, 14, 2, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 13}, {"id": 4, "name": "<PERSON> Sound Effects", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [18]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [14]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 1", "volume": 55, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [33]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [9]}, {"code": 250, "indent": 0, "parameters": [{"name": "Stroke 2", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [22]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 20, "y": 0}, null, null, null, null, null, null]}