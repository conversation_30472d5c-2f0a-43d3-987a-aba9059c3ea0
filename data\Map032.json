{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "Hotel_2 - migfus2", "pan": 0, "pitch": 100, "volume": 50}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "Dining Room", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "0Dining_Date", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 27, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Exit", "note": "<Hitbox Up: 1> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 4, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 13}, {"id": 2, "name": "Scene Load In", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 91, 1]}, {"code": 231, "indent": 1, "parameters": [14, "4Dining_AuntDate", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 121, "indent": 1, "parameters": [73, 73, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [20, 20, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 20, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 3, "name": "Backyard", "note": "<Hitbox Up: 8> <Hitbox Right: 4>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 23, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>I don't need to go out there right now..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 10}, {"id": 4, "name": "<PERSON><PERSON><PERSON>", "note": "<Hitbox Up: 12> <Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 23, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon door;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 201, "indent": 0, "parameters": [0, 31, 0, 14, 8, 0]}, {"code": 121, "indent": 0, "parameters": [20, 20, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 13}, {"id": 5, "name": "Aunt Date", "note": "<Hitbox Up: 8> <Hitbox Right: 2>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 3, "switch1Valid": false, "switch2Id": 110, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [14]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [0, 199, 1]}, {"code": 356, "indent": 1, "parameters": ["ChangeTitle1 Title_Date"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 17, 0, 18, 0]}, {"code": 122, "indent": 1, "parameters": [17, 17, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["^^^ This conditional is to fix a temporary bug where A Dialogue "]}, {"code": 408, "indent": 0, "parameters": ["was incorrectly 1 ahead of where it should be."]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["Still needs to add to dialogue for most so keep this"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]So?\\! What do you think?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]...\\! You look absolutely stunning!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]What a relief!\\! I was so worried if you would like my dress!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]You clean up quite well yourself, handsome."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Heh, thanks.\\! I'm not really used to getting all dressed up for things."]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Well you should consider it more often.\\! You'd sweep a lot of ladies right off their feet looking like that!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]So what do you say we take a seat now before out food gets cold?"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Help With Her Seat", "Oh Boy! Let's Eat!"], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Help With Her Seat"]}, {"code": 122, "indent": 1, "parameters": [37, 37, 1, 0, 1]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Here.\\! Let me help you with your seat."]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Wow.\\! Well dressed and courteous?!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]This date is starting off on all of the right notes."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Oh Boy! Let's Eat!"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\N[1]>Aw yeah, I'm starving!\\! Let's dig in!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]*giggle*\\! Yes, let's eat!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$bust(1).clear()"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 80, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]I hope you find the food looks appetizing..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Are you kidding?\\! This food looks delicious!\\! And it's so fancy too!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>How'd you manage to pull this off in such a short time?!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Oh good.\\! I had it delivered from the restaurant where our reservations were at."]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Your \\n[5] had some spare candles and decor in the garage.\\! It really helps to set the mood just right."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Wow.\\! You really outdid yourself \\n[6] \\n[3]."]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Thank you dear..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_choice(1, \"$gameSwitches.value(88) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(2, \"$gameSwitches.value(89) === true\")"]}, {"code": 355, "indent": 1, "parameters": ["hide_choice(3, \"$gameSwitches.value(90) === true\")"]}, {"code": 102, "indent": 1, "parameters": [["Ask About Work", "Talk About Travel", "Discuss TV Shows", "Finish Dinner"], -1, 0, 1, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Ask About Work"]}, {"code": 122, "indent": 2, "parameters": [37, 37, 1, 0, 1]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Say, \\n[6] \\n[3].\\! What actually do you do for a living?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>I feel like I've never really seen you talk about work at all."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]Hmm ~\\! I guess it makes sense your \\n[5] has never really told you about that."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]But I haven't worked in years now!"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I used to be a model."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>A model?!\\! Like, wearing bikinis and stuff?!"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]*giggle*\\! Well aren't you eager to know?"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I was actually.\\! Bikinis, fashion, and even sexy outfits."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]I actually had quite a bit of fame within the industry for my work!"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Did you ever, you know, pose nude or anything?"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]Oh heavens no!"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]Believe me, I received countless inquiries and requests to do so."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]But I'm a bit more old fashioned."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]I don't just let anyone see me nude ~ ♥️"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]It was quite a fun career while it lasted, though."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]I often miss those days..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>What made you decide to quit modeling then?"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Nothing lasts forever, I suppose."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]With so many young pretty faces joining the scene, I began to feel like I had overstayed my time in the industry."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I felt it was my time to bow out with grace."]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 102, "indent": 2, "parameters": [["You Should Model!", "What About Design?"], -1, 0, 1, 0]}, {"code": 402, "indent": 2, "parameters": [0, "You Should Model!"]}, {"code": 117, "indent": 3, "parameters": [4]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>You should totally model again \\n[6] \\n[3]!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Why do I get the feeling you just want a private phot shoot?"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]I'd say my modeling days are long behind me."]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]But I'm sure you'll catch me around in my bathing suit this summer."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "What About Design?"]}, {"code": 117, "indent": 3, "parameters": [4]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Instead of modeling, have you considered actually designing clothes?"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>You still seem to be very interested in fashion!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]I'm surprised you noticed!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]Becoming a world-renowned fashion designer is my dream!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Perhaps I'll finally give it a real go this year..."]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>That would be awesome!\\! I'll support you every step of the way!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]That means a lot.\\! Truly, dear ~ ♥️"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [88, 88, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Talk About Travel"]}, {"code": 122, "indent": 2, "parameters": [37, 37, 1, 0, 1]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Once your new house situation is all figured out, do you have any fun plans this year?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>You're always traveling around the world, it seems like."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]Oh hoh hoh!\\! I suppose I do appear to travel a lot, don't I?"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]I don't have any current travel plans, but I do think it would be fun for the four of us to travel somewhere."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Oh ya?\\! Where did you have in mind?"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]There are endless options!\\! A bustling city, a remote island, a scenic cruise ship."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]Do any of those ideas excite you?"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 102, "indent": 2, "parameters": [["Bustling City", "Cruise Ship"], -1, 0, 1, 0]}, {"code": 402, "indent": 2, "parameters": [0, "Bustling City"]}, {"code": 117, "indent": 3, "parameters": [4]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>A bustling city sounds a little intimidating, but fun too!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]If you have the opportunity, you should visit Crimson City for a few days!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]The restaurants and food options are heavenly!\\! You'll never run out of options!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]Oh and how could I forget about the entertainment!\\! It's so lively!"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Whoa, it does sound like a lot of fun!"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "Cruise Ship"]}, {"code": 117, "indent": 3, "parameters": [4]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>I've never been on a cruise ship before..."]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]They're quite relaxing.\\! Wonderful food, relaxing spas, and breathtaking views throughout!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]If we went on one, I would finally have someone to help me put on sun screen too ~ ♥️"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Let's go now!\\! We have a boat to catch!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]*giggle*\\! You're too funny, \\n[1]!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]Who knows...\\! Maybe I'll browse around for a fun cruise for us to embark on."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [89, 89, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Discuss TV Shows"]}, {"code": 122, "indent": 2, "parameters": [37, 37, 1, 0, 1]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Have there been any good new shows you've been catching up on?"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]I'm sure you know I can never get enough of my romance shows!"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]Oh!\\! That does remind me of a new show that has me absolutely hooked!"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]It's a dating show involving a bunch of milfs."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 4]I just learned that term recently!\\! Mother I'd Like To Fuck?!"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\N[1]>Hah hah!\\! Yup, that's a milf alright."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]All of these beautiful milfs are forced to live together with these younger men.\\! Close to your age."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]And the rest of the show, well...\\! We'll leave that up to your imagination ~ ♥️"]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 102, "indent": 2, "parameters": [["Sounds Hot!", "Sounds Funny!"], -1, 0, 1, 0]}, {"code": 402, "indent": 2, "parameters": [0, "Sounds Hot!"]}, {"code": 117, "indent": 3, "parameters": [4]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Wow, that show sounds really hot actually!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]I figured you would be into the idea of 'milfs'."]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I'm sure it's tough living in the same house with one.\\! Your \\n[5] is the very definition of a milf!"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Wha-\\! that's uhh-\\! I mean..."]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]No need to be embarrassed honey.\\! I've caught you staring at her inappropriately numerous times now."]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]And at your age, I'm sure those hormones are just pumping your brain full of dirty thoughts ~ ♥️"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>\\n[6] \\n[3]..."]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I'm just pulling your tongue sweetheart.\\! Don't worry."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "Sounds Funny!"]}, {"code": 117, "indent": 3, "parameters": [4]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>Sounds like the show is probably really funny then, huh?"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]Oh, it's an absolute riot!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I hate to admit it but the show has me glued to my seat the whole time!"]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]It doesn't help that the overall theme of the show turns me on quite a bit too..."]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Maybe we should watch it together sometime ~ ♥️"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\N[1]>I'll watch it with you but something tells me we won't stay focused on the show for long."]}, {"code": 101, "indent": 3, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 3, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]*giggle*\\! It doesn't help that your cock is always distracting me, you know?"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [90, 90, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Finish Dinner"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [1, 37, 0, 4, 1]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Dear, you have been an absolutely marvelous date tonight!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]I've loved every minute that we've gotten to spend together!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]But you know, a romantic date can never end with just dinner ~ ♥️"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 37, 0, 2, 1]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]You know, this has been a lovely night, \\n[1]."]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]Thank you for keeping me company and being such a lovely date!"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]I want to show you my gratitude for being my date tonight."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 2]Oh.\\! I suppose it is getting quite late isn't it?"]}, {"code": 101, "indent": 2, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 2, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]But certainly you still have a little bit of free time left, right?"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>What did you have in mind \\n[6] \\n[3]?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Here, grab my hand.\\! We're going to take a ride in my car!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\N[1]>Your car?!\\! I thought you said it wouldn't start up?"]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]I'm not talking about that sort of 'ride'.\\! We won't need to drive anywhere with what I'm about to do to you!"]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]Come on!\\! I have to show you my thanks ~ ♥️"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [7, 7, 0, 0, 19]}, {"code": 117, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 87, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["hover_icon talk;"]}, {"code": 108, "indent": 0, "parameters": ["click_activate!"]}, {"code": 108, "indent": 0, "parameters": ["<Click Trigger>"]}, {"code": 235, "indent": 0, "parameters": [14]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 5]Hey there, handsome..."]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 102, "indent": 0, "parameters": [["Talk", "Date (Sex)", "Bedroom Sex", "Leave"], 3, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Talk"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]I always knew this dress would be useful again."]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 0]I once wore this dress for a special page in a fancy magazine!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]While that was fun, I must admit, I think I prefer wearing it for this special occasion even more ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Date (Sex)"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 1]I've been looking forward to this date all day!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]And after our date, well, you know what comes next ~ ♥️"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 1, "parameters": ["Some time later..."]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 19]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 119, "indent": 1, "parameters": ["End"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Bedroom Sex"]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 6]You just want to skip to the climax, you naughty boy!"]}, {"code": 101, "indent": 1, "parameters": ["Aunt3[BUST][Exp3x3]", 10, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["\\n<\\c[21]\\N[3]>\\bust[1]\\bustExp[1, 7]Wait for me in your room.\\! Let me get undressed and then I'll be there."]}, {"code": 121, "indent": 1, "parameters": [16, 16, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 122, "indent": 1, "parameters": [7, 7, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 119, "indent": 1, "parameters": ["End"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Leave"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [14, "4Dining_AuntDate", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [15, "4Bathroom_Exit", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 118, "indent": 0, "parameters": ["End"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 91, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 12}, null, null]}